/**
 * @file Basic Usage Example - Universal AI Brain
 * 
 * This example demonstrates how to set up and use the Universal AI Brain
 * with MongoDB Atlas Vector Search and OpenAI embeddings.
 * 
 * 🚀 This is your vision in action!
 */

import { UniversalAIBrain, MongoVectorStore, OpenAIEmbeddingProvider } from '@mongodb-ai/core';

async function basicUsageExample() {
  console.log('🧠 Universal AI Brain - Basic Usage Example');
  console.log('===========================================\n');

  // 1. Configure the Universal AI Brain
  const brainConfig = {
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb+srv://your-atlas-connection-string',
      dbName: 'ai_brain_db'
    },
    embeddingConfig: {
      provider: 'openai' as const,
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key',
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'vector_index',
      collectionName: 'embedded_content',
      minScore: 0.7,
      maxResults: 10
    },
    features: {
      enableHybridSearch: true,
      enableConversationMemory: true,
      enableKnowledgeGraph: false,
      enableRealTimeUpdates: false
    }
  };

  try {
    // 2. Initialize the Universal AI Brain
    console.log('🔧 Initializing Universal AI Brain...');
    const brain = new UniversalAIBrain(brainConfig);
    await brain.initialize();
    console.log('✅ Brain initialized successfully!\n');

    // 3. Store some knowledge in the brain
    console.log('📚 Storing knowledge in the brain...');
    
    const knowledgeDocuments = [
      {
        text: 'MongoDB Atlas Vector Search is a powerful feature for building AI applications with semantic search capabilities.',
        metadata: { type: 'documentation', topic: 'mongodb', category: 'vector-search' },
        source: 'mongodb_docs'
      },
      {
        text: 'TypeScript provides static type checking for JavaScript, making it easier to build large-scale applications.',
        metadata: { type: 'documentation', topic: 'typescript', category: 'programming' },
        source: 'typescript_docs'
      },
      {
        text: 'The Universal AI Brain integrates with any TypeScript framework to provide intelligent context injection.',
        metadata: { type: 'documentation', topic: 'ai-brain', category: 'integration' },
        source: 'ai_brain_docs'
      }
    ];

    // Store knowledge using the brain's vector store
    const vectorStore = brain['vectorStore']; // Access private property for demo
    for (const doc of knowledgeDocuments) {
      await vectorStore.storeDocument(doc.text, doc.metadata, doc.source);
    }
    console.log('✅ Knowledge stored successfully!\n');

    // 4. Enhance prompts with intelligent context
    console.log('🎯 Enhancing prompts with intelligent context...');
    
    const userQuery = 'How can I use vector search in my TypeScript application?';
    console.log(`User Query: "${userQuery}"`);
    
    const enhancedPrompt = await brain.enhancePrompt(userQuery, {
      frameworkType: 'vercel-ai',
      maxContextItems: 3,
      enhancementStrategy: 'hybrid'
    });

    console.log('\n📈 Enhanced Prompt:');
    console.log('Original:', enhancedPrompt.originalPrompt);
    console.log('Enhanced:', enhancedPrompt.enhancedPrompt.substring(0, 200) + '...');
    console.log('Context Sources:', enhancedPrompt.metadata.contextSources);
    console.log('Injected Context Items:', enhancedPrompt.injectedContext.length);

    // 5. Store an interaction for learning
    console.log('\n💾 Storing interaction for learning...');
    
    const interactionId = await brain.storeInteraction({
      conversationId: 'demo-conversation-1',
      userMessage: userQuery,
      assistantResponse: 'You can use MongoDB Atlas Vector Search with TypeScript by integrating the Universal AI Brain...',
      context: enhancedPrompt.injectedContext,
      metadata: {
        model: 'gpt-4',
        tokens: 150,
        responseTime: 1200
      },
      framework: 'vercel-ai'
    });
    
    console.log('✅ Interaction stored with ID:', interactionId);

    // 6. Retrieve relevant context
    console.log('\n🔍 Retrieving relevant context...');
    
    const relevantContext = await brain.retrieveRelevantContext(
      'What are the benefits of using TypeScript?',
      {
        limit: 5,
        minRelevanceScore: 0.6
      }
    );

    console.log('Found', relevantContext.length, 'relevant context items:');
    relevantContext.forEach((context, index) => {
      console.log(`${index + 1}. [${context.source}] ${context.content.substring(0, 100)}... (score: ${context.relevanceScore.toFixed(3)})`);
    });

    // 7. Get brain statistics
    console.log('\n📊 Brain Statistics:');
    const stats = await brain.getStats();
    console.log('Health:', stats.isHealthy ? '✅ Healthy' : '❌ Unhealthy');
    console.log('Interactions:', stats.collections.interactions);
    console.log('Conversations:', stats.collections.conversations);
    console.log('Embedding Model:', stats.embeddingProvider.model);
    console.log('Vector Dimensions:', stats.embeddingProvider.dimensions);

    console.log('\n🎉 Universal AI Brain demo completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Create a vector search index in MongoDB Atlas');
    console.log('2. Integrate with your preferred framework (Mastra, Vercel AI, etc.)');
    console.log('3. Start building intelligent AI applications!');

  } catch (error) {
    console.error('❌ Error in Universal AI Brain demo:', error);
    throw error;
  }
}

// Framework Integration Examples
async function frameworkIntegrationExamples() {
  console.log('\n🔌 Framework Integration Examples');
  console.log('==================================\n');

  // Example 1: Vercel AI Integration (conceptual)
  console.log('📝 Vercel AI Integration:');
  console.log(`
import { generateText } from 'ai';
import { UniversalAIBrain } from '@mongodb-ai/core';

const brain = new UniversalAIBrain(config);
await brain.initialize();

// Enhance prompts before sending to AI
const enhancedPrompt = await brain.enhancePrompt(userMessage);

const result = await generateText({
  model: openai('gpt-4'),
  prompt: enhancedPrompt.enhancedPrompt
});

// Store interaction for learning
await brain.storeInteraction({
  conversationId,
  userMessage,
  assistantResponse: result.text,
  context: enhancedPrompt.injectedContext,
  framework: 'vercel-ai'
});
  `);

  // Example 2: Mastra Integration (conceptual)
  console.log('📝 Mastra Integration:');
  console.log(`
import { Agent } from '@mastra/core';
import { UniversalAIBrain } from '@mongodb-ai/core';

const brain = new UniversalAIBrain(config);
await brain.initialize();

const agent = new Agent({
  name: 'Intelligent Agent',
  instructions: 'You are an AI assistant with access to a knowledge base.',
  model: {
    provider: 'OPEN_AI',
    name: 'gpt-4'
  }
});

// Enhance agent responses with brain intelligence
agent.beforeGenerate(async (context) => {
  const enhanced = await brain.enhancePrompt(context.messages.slice(-1)[0].content);
  context.messages[context.messages.length - 1].content = enhanced.enhancedPrompt;
  return context;
});
  `);

  // Example 3: LangChain Integration (conceptual)
  console.log('📝 LangChain.js Integration:');
  console.log(`
import { ChatOpenAI } from '@langchain/openai';
import { UniversalAIBrain } from '@mongodb-ai/core';

const brain = new UniversalAIBrain(config);
await brain.initialize();

const llm = new ChatOpenAI({ modelName: 'gpt-4' });

// Create a custom chain with brain enhancement
const enhancedChain = async (input: string) => {
  const enhanced = await brain.enhancePrompt(input);
  const result = await llm.invoke(enhanced.enhancedPrompt);
  
  await brain.storeInteraction({
    conversationId: 'langchain-session',
    userMessage: input,
    assistantResponse: result.content,
    context: enhanced.injectedContext,
    framework: 'langchain'
  });
  
  return result;
};
  `);
}

// Vector Search Index Creation Guide
function vectorSearchIndexGuide() {
  console.log('\n🏗️ MongoDB Atlas Vector Search Index Creation');
  console.log('==============================================\n');

  console.log('To create the required vector search index in MongoDB Atlas:');
  console.log('\n1. Go to your MongoDB Atlas cluster');
  console.log('2. Navigate to "Search" tab');
  console.log('3. Click "Create Search Index"');
  console.log('4. Choose "Atlas Vector Search"');
  console.log('5. Use this index definition:\n');

  const indexDefinition = {
    "name": "vector_index",
    "type": "vectorSearch",
    "definition": {
      "fields": [
        {
          "type": "vector",
          "path": "embedding",
          "numDimensions": 1536,
          "similarity": "cosine"
        },
        {
          "type": "filter",
          "path": "source"
        },
        {
          "type": "filter",
          "path": "metadata.type"
        },
        {
          "type": "filter",
          "path": "timestamp"
        }
      ]
    }
  };

  console.log(JSON.stringify(indexDefinition, null, 2));
  console.log('\n6. Select your database and collection (embedded_content)');
  console.log('7. Click "Create Search Index"');
  console.log('\n✅ Your Universal AI Brain will be ready to use!');
}

// Run the examples
if (require.main === module) {
  (async () => {
    try {
      await basicUsageExample();
      await frameworkIntegrationExamples();
      vectorSearchIndexGuide();
    } catch (error) {
      console.error('Demo failed:', error);
      process.exit(1);
    }
  })();
}

export { basicUsageExample, frameworkIntegrationExamples, vectorSearchIndexGuide };
