import { Agent, Task, Crew } from 'crewai-js';
import { ToolExecutor } from '../packages/core/src/agent/ToolExecutor';
import { IDataStore } from '../packages/core/src/persistance/IDataStore';
import { ToolDefinition, ToolExecutionLog } from '../packages/core/src/agent/ToolExecutor';
import { MongoDataStore } from '../packages/core/src/persistance/MongoDataStore';
import { getTestDb, setupTestDb, teardownTestDb } from '../packages/core/src/__tests__/setup';

class CrewAITool {
  private toolExecutor: ToolExecutor;
  public toolId: string;

  constructor(toolExecutor: ToolExecutor, toolId: string) {
    this.toolExecutor = toolExecutor;
    this.toolId = toolId;
  }

  async execute(input: any): Promise<any> {
    return this.toolExecutor.execute(this.toolId, input, {
      agent_id: 'crewai-agent',
      workflow_id: 'crewai-workflow',
      timeout_ms: 5000,
    });
  }
}

async function run() {
  await setupTestDb();
  const db = getTestDb();
  const toolStore = new MongoDataStore<ToolDefinition>(db, 'agent_tools');
  const executionStore = new MongoDataStore<ToolExecutionLog>(db, 'tool_executions');
  const toolExecutor = new ToolExecutor(toolStore, executionStore);

  await toolExecutor.createTool({
    tool_id: 'test-tool',
    name: 'Test Tool',
    description: 'A test tool.',
    input_schema: {},
    output_schema: {},
    execute: async (input: any) => ({ result: `test tool executed with input: ${JSON.stringify(input)}` }),
  });

  const testTool = new CrewAITool(toolExecutor, 'test-tool');

  const agent = new Agent({
    name: 'Test Agent',
    role: 'Test Agent',
    goal: 'Test the tool',
    backstory: 'You are a test agent.',
    tools: [testTool.toolId],
  });

  const task = new Task({
    description: 'Test the tool with some input',
    agent,
  });

  const crew = new Crew({
    name: 'Test Crew',
    agents: [agent],
    tasks: [task],
  });

  const result = await crew.kickoff();
  console.log(result);

  await teardownTestDb();
}

run().catch(console.error);