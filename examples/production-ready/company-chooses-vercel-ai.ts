/**
 * @file Company Chooses Vercel AI SDK - Production Ready Example
 * 
 * This example demonstrates how a company can choose Vercel AI SDK
 * and be 90% done building the smartest AI agent system by integrating
 * with the Universal AI Brain.
 * 
 * SCENARIO: An e-commerce company wants to build AI shopping assistants
 * CHOICE: They choose Vercel AI SDK for its streaming capabilities
 * RESULT: With Universal AI Brain, they're 90% done in 30 minutes!
 */

import { 
  UniversalAIBrain, 
  VercelAIAdapter 
} from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * STEP 1: Company chooses Vercel AI SDK and sets up Universal AI Brain
 */
async function setupUniversalAIBrain() {
  console.log('🛒 ShopSmart E-commerce - Setting up AI Shopping Assistants');
  console.log('📋 Framework Choice: Vercel AI SDK (for streaming UX)');
  console.log('🧠 Intelligence Layer: Universal AI Brain (MongoDB-powered)\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb+srv://your-atlas-cluster',
      dbName: 'shopsmart_ai_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'shopsmart_vector_index',
      collectionName: 'shopsmart_knowledge',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized - Ready to make Vercel AI 90% smarter!\n');

  return brain;
}

/**
 * STEP 2: Integrate Vercel AI SDK with Universal AI Brain
 */
async function integrateWithVercelAI(brain: UniversalAIBrain) {
  console.log('🔌 Integrating Vercel AI SDK with Universal AI Brain...');

  const vercelAdapter = new VercelAIAdapter({
    enableStreamEnhancement: true,
    enableToolIntegration: true,
    enableChatMemory: true
  });

  // ONE LINE OF CODE - Vercel AI gets MongoDB superpowers!
  const enhancedAI = await vercelAdapter.integrate(brain);
  
  console.log('✅ Vercel AI SDK now has MongoDB superpowers!\n');
  console.log('🎉 What Vercel AI gained instantly:');
  console.log('   • Intelligent streaming with context injection');
  console.log('   • Perfect memory across chat sessions');
  console.log('   • Semantic search across product catalog');
  console.log('   • Learning from every customer interaction');
  console.log('   • MongoDB-powered tools and functions');
  console.log('   • Production-ready infrastructure\n');

  return enhancedAI;
}

/**
 * STEP 3: Store e-commerce knowledge
 */
async function storeEcommerceKnowledge(brain: UniversalAIBrain) {
  console.log('🛍️ Storing ShopSmart e-commerce knowledge...');

  const productKnowledge = [
    {
      question: 'What is ShopSmart?',
      answer: 'ShopSmart is a premium online electronics store offering the latest smartphones, laptops, gaming gear, and smart home devices. We provide free shipping, 30-day returns, and expert customer support.',
      category: 'company_info'
    },
    {
      question: 'What are your shipping options?',
      answer: 'We offer free standard shipping (3-5 business days), express shipping ($9.99, 1-2 business days), and overnight shipping ($19.99, next business day). All orders over $50 qualify for free shipping.',
      category: 'shipping'
    },
    {
      question: 'What is your return policy?',
      answer: 'We offer a 30-day return policy on all items. Products must be in original condition with all accessories. Returns are free, and refunds are processed within 3-5 business days.',
      category: 'returns'
    },
    {
      question: 'Do you offer warranty on products?',
      answer: 'Yes! All products come with manufacturer warranty. We also offer extended warranty plans: 1-year ($29), 2-year ($49), and 3-year ($79) for additional protection.',
      category: 'warranty'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, MasterCard, American Express), PayPal, Apple Pay, Google Pay, and Buy Now Pay Later options through Klarna and Afterpay.',
      category: 'payment'
    }
  ];

  for (const knowledge of productKnowledge) {
    await brain.storeInteraction({
      conversationId: 'ecommerce_knowledge',
      userMessage: knowledge.question,
      assistantResponse: knowledge.answer,
      context: [],
      framework: 'vercel-ai',
      metadata: {
        type: 'ecommerce_knowledge',
        category: knowledge.category,
        source: 'shopsmart_knowledge_base'
      }
    });
  }

  console.log(`✅ Stored ${productKnowledge.length} knowledge items in MongoDB`);
  console.log('🧠 The brain now knows everything about ShopSmart!\n');
}

/**
 * STEP 4: Create intelligent Vercel AI applications
 */
async function createIntelligentApplications(enhancedAI: any) {
  console.log('💬 Creating intelligent Vercel AI applications...');

  // Shopping Assistant Chat
  async function createShoppingAssistant() {
    console.log('🛒 Shopping Assistant with streaming responses...');
    
    const response = await enhancedAI.streamText({
      model: { modelId: 'gpt-4' },
      messages: [
        { 
          role: 'system', 
          content: 'You are a helpful shopping assistant for ShopSmart. Help customers find products, answer questions about shipping, returns, and provide excellent customer service.' 
        },
        { 
          role: 'user', 
          content: 'I\'m looking for a good laptop for college. What do you recommend?' 
        }
      ],
      conversationId: 'shopping_session_001',
      onFinish: (result) => {
        console.log(`✅ Streaming completed (${result.usage?.totalTokens} tokens)`);
      }
    });

    console.log('🤖 Shopping Assistant Response:');
    for await (const chunk of response.textStream) {
      process.stdout.write(chunk);
    }
    console.log('\n');
  }

  // Product Recommendation Engine
  async function createProductRecommendations() {
    console.log('🎯 Product Recommendation Engine...');
    
    const recommendations = await enhancedAI.generateObject({
      model: { modelId: 'gpt-4' },
      messages: [
        { 
          role: 'user', 
          content: 'I need a smartphone under $800 with good camera and battery life' 
        }
      ],
      schema: {
        type: 'object',
        properties: {
          recommendations: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                productName: { type: 'string' },
                price: { type: 'number' },
                features: { type: 'array', items: { type: 'string' } },
                rating: { type: 'number' },
                reason: { type: 'string' }
              }
            }
          },
          summary: { type: 'string' }
        }
      },
      conversationId: 'product_recommendations_001'
    });

    console.log('📱 Product Recommendations:');
    console.log(JSON.stringify(recommendations.object, null, 2));
    console.log();
  }

  // Customer Support Chat
  async function createCustomerSupport() {
    console.log('🎧 Customer Support with MongoDB context...');
    
    const supportResponse = await enhancedAI.generateText({
      model: { modelId: 'gpt-4' },
      messages: [
        { 
          role: 'user', 
          content: 'I ordered a laptop 3 days ago but haven\'t received shipping confirmation. What\'s going on?' 
        }
      ],
      conversationId: 'support_session_001'
    });

    console.log('🤖 Support Response:');
    console.log(supportResponse.text);
    console.log(`📊 Context Sources: ${supportResponse.enhancedContext?.map(c => c.source).join(', ')}\n`);
  }

  await createShoppingAssistant();
  await createProductRecommendations();
  await createCustomerSupport();

  console.log('✅ Created intelligent Vercel AI applications with MongoDB superpowers!\n');
}

/**
 * STEP 5: Test MongoDB tools integration
 */
async function testMongoDBTools(enhancedAI: any) {
  console.log('🛠️ Testing MongoDB tools integration...\n');

  const mongoDBTools = enhancedAI.createMongoDBTools();

  // Test knowledge base search
  console.log('🔍 Testing knowledge base search tool...');
  const searchResult = await mongoDBTools.searchKnowledgeBase.execute({
    query: 'shipping and returns policy',
    limit: 3
  });

  console.log('📋 Search Results:');
  searchResult.results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.content.substring(0, 100)}... (Score: ${result.relevanceScore})`);
  });

  // Test memory storage
  console.log('\n💾 Testing memory storage tool...');
  const storeResult = await mongoDBTools.storeMemory.execute({
    content: 'Customer prefers laptops with long battery life and good build quality',
    metadata: { customerType: 'student', preferences: ['battery_life', 'build_quality'] }
  });

  console.log('✅ Memory Storage Result:', storeResult.message);
  console.log();
}

/**
 * STEP 6: Show the revolutionary results
 */
async function showRevolutionaryResults(brain: UniversalAIBrain) {
  console.log('📊 REVOLUTIONARY RESULTS - What ShopSmart achieved:\n');

  const brainStats = await brain.getStats();
  const collectionStats = await brain.collections.getCollectionStats();

  console.log('🎯 BUSINESS IMPACT:');
  console.log('   ✅ Chose Vercel AI SDK (their preference for streaming)');
  console.log('   ✅ Added Universal AI Brain (ONE line of code)');
  console.log('   ✅ Intelligent streaming chat with context');
  console.log('   ✅ Perfect memory across customer sessions');
  console.log('   ✅ Smart product recommendations');
  console.log('   ✅ MongoDB-powered customer support');
  console.log('   ✅ Production-ready e-commerce AI\n');

  console.log('📈 TECHNICAL ACHIEVEMENTS:');
  console.log(`   • Streaming Enhancement: Real-time context injection`);
  console.log(`   • Memory Management: Persistent across sessions`);
  console.log(`   • Tool Integration: MongoDB-powered functions`);
  console.log(`   • Vector Search: ${brainStats.collections?.vectorStore?.documentCount || 0} documents indexed`);
  console.log(`   • System Health: ${brainStats.isHealthy ? 'Healthy' : 'Issues detected'}\n`);

  console.log('⏱️ TIME TO VALUE:');
  console.log('   • Setup Time: 30 minutes');
  console.log('   • Framework Choice: Preserved (Vercel AI SDK)');
  console.log('   • Intelligence Gained: 90% complete AI system');
  console.log('   • Streaming UX: Enhanced with MongoDB context\n');

  console.log('🚀 WHAT THEY BUILT:');
  console.log('   • Intelligent shopping assistants with streaming');
  console.log('   • Smart product recommendation engine');
  console.log('   • Context-aware customer support');
  console.log('   • MongoDB-powered tools and functions');
  console.log('   • Persistent customer memory and preferences\n');

  console.log('🎉 THE UNIVERSAL AI BRAIN REVOLUTION:');
  console.log('   🔥 Company chose their favorite framework (Vercel AI)');
  console.log('   🔥 Added MongoDB intelligence with ONE line of code');
  console.log('   🔥 Got 90% complete AI system instantly');
  console.log('   🔥 Streaming UX enhanced with intelligent context');
  console.log('   🔥 Production-ready from day one\n');

  console.log('💡 THIS IS THE FUTURE OF AI DEVELOPMENT! 🧠⚡');
}

/**
 * Main execution
 */
async function main() {
  try {
    console.log('🌟 UNIVERSAL AI BRAIN - VERCEL AI SUCCESS STORY 🌟\n');
    console.log('Demonstrating how ShopSmart chose Vercel AI SDK');
    console.log('and got MongoDB superpowers instantly!\n');
    console.log('=' .repeat(60) + '\n');

    const brain = await setupUniversalAIBrain();
    const enhancedAI = await integrateWithVercelAI(brain);
    await storeEcommerceKnowledge(brain);
    await createIntelligentApplications(enhancedAI);
    await testMongoDBTools(enhancedAI);
    await showRevolutionaryResults(brain);

    console.log('=' .repeat(60));
    console.log('🎯 MISSION ACCOMPLISHED!');
    console.log('ShopSmart chose Vercel AI and got MongoDB superpowers!');
    console.log('They are now 90% done with the smartest AI system possible! 🚀');

  } catch (error) {
    console.error('❌ Error in Vercel AI success story:', error);
  }
}

// Run the complete example
if (require.main === module) {
  main();
}

export { main as companyChoosesVercelAIExample };
