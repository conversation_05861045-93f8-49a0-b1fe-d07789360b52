/**
 * @file Company Chooses Mastra - Production Ready Example
 * 
 * This example demonstrates how a company can choose Mastra framework
 * and be 90% done building the smartest AI agent system by integrating
 * with the Universal AI Brain.
 * 
 * SCENARIO: A SaaS company wants to build AI customer support agents
 * CHOICE: They choose Mastra framework for its simplicity
 * RESULT: With Universal AI Brain, they're 90% done in 30 minutes!
 */

import { 
  UniversalAIBrain, 
  MastraAdapter,
  CollectionManager 
} from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * STEP 1: Company chooses <PERSON>stra and sets up Universal AI Brain
 * This is the ONLY setup they need - everything else is automatic!
 */
async function setupUniversalAIBrain() {
  console.log('🏢 ACME SaaS Company - Setting up AI Customer Support');
  console.log('📋 Framework Choice: Mastra (for its simplicity)');
  console.log('🧠 Intelligence Layer: Universal AI Brain (MongoDB-powered)\n');

  // Initialize the Universal AI Brain - The MISSING BRAIN every framework needs!
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb+srv://your-atlas-cluster',
      dbName: 'acme_ai_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'acme_vector_index',
      collectionName: 'acme_knowledge',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized - Ready to make Mastra 90% smarter!\n');

  return brain;
}

/**
 * STEP 2: Integrate Mastra with Universal AI Brain
 * ONE line of code gives Mastra MongoDB superpowers!
 */
async function integrateWithMastra(brain: UniversalAIBrain) {
  console.log('🔌 Integrating Mastra with Universal AI Brain...');

  // Create Mastra adapter
  const mastraAdapter = new MastraAdapter({
    enableMemoryReplacement: true,
    enableWorkflowIntegration: true,
    enableToolEnhancement: true
  });

  // ONE LINE OF CODE - This is where the magic happens!
  const enhancedMastra = await mastraAdapter.integrate(brain);
  
  console.log('✅ Mastra now has MongoDB superpowers!\n');
  console.log('🎉 What Mastra gained instantly:');
  console.log('   • Perfect memory of every conversation');
  console.log('   • Semantic search across all company knowledge');
  console.log('   • Intelligent context injection');
  console.log('   • Learning from every interaction');
  console.log('   • Real-time coordination with other agents');
  console.log('   • Performance monitoring and analytics');
  console.log('   • Production-ready MongoDB infrastructure\n');

  return enhancedMastra;
}

/**
 * STEP 3: Store company knowledge - The brain learns everything!
 */
async function storeCompanyKnowledge(brain: UniversalAIBrain) {
  console.log('📚 Storing ACME SaaS company knowledge...');

  // Company knowledge that will make agents smart
  const companyKnowledge = [
    {
      question: 'What is ACME SaaS?',
      answer: 'ACME SaaS is a project management platform that helps teams collaborate, track progress, and deliver projects on time. We serve over 10,000 companies worldwide.',
      category: 'company_info'
    },
    {
      question: 'How do I reset my password?',
      answer: 'To reset your password: 1) Go to the login page, 2) Click "Forgot Password", 3) Enter your email, 4) Check your email for reset link, 5) Follow the instructions in the email.',
      category: 'support_faq'
    },
    {
      question: 'What are our pricing plans?',
      answer: 'We offer three plans: Starter ($9/month for 5 users), Professional ($19/month for 15 users), and Enterprise ($39/month for unlimited users). All plans include 24/7 support.',
      category: 'pricing'
    },
    {
      question: 'How do I integrate with Slack?',
      answer: 'To integrate with Slack: 1) Go to Settings > Integrations, 2) Click "Connect Slack", 3) Authorize ACME SaaS in Slack, 4) Choose which channels to sync, 5) Save settings.',
      category: 'integrations'
    },
    {
      question: 'What is our refund policy?',
      answer: 'We offer a 30-day money-back guarantee. If you\'re not satisfied, contact support within 30 days of purchase for a full refund. No questions asked.',
      category: 'policies'
    }
  ];

  // Store all knowledge in MongoDB - The brain learns everything!
  for (const knowledge of companyKnowledge) {
    await brain.storeInteraction({
      conversationId: 'company_knowledge',
      userMessage: knowledge.question,
      assistantResponse: knowledge.answer,
      context: [],
      framework: 'mastra',
      metadata: {
        type: 'company_knowledge',
        category: knowledge.category,
        source: 'acme_knowledge_base'
      }
    });
  }

  console.log(`✅ Stored ${companyKnowledge.length} knowledge items in MongoDB`);
  console.log('🧠 The brain now knows everything about ACME SaaS!\n');
}

/**
 * STEP 4: Create intelligent Mastra agents - 90% done!
 */
async function createIntelligentAgents(enhancedMastra: any) {
  console.log('🤖 Creating intelligent Mastra agents...');

  // Customer Support Agent - Knows everything about the company!
  const supportAgent = enhancedMastra.createAgent({
    name: 'ACME Support Agent',
    instructions: `You are a helpful customer support agent for ACME SaaS. 
    Use your knowledge base to provide accurate, helpful answers. 
    Always be friendly and professional. If you don't know something, 
    offer to connect the customer with a human agent.`,
    model: {
      name: 'gpt-4',
      provider: 'openai'
    },
    tools: {
      escalateToHuman: {
        name: 'Escalate to Human',
        description: 'Escalate complex issues to human support',
        execute: async ({ reason }: { reason: string }) => {
          return {
            success: true,
            message: `Issue escalated to human support. Reason: ${reason}`,
            ticketId: `ACME-${Date.now()}`
          };
        }
      }
    }
  });

  // Sales Agent - Knows all about pricing and features!
  const salesAgent = enhancedMastra.createAgent({
    name: 'ACME Sales Agent',
    instructions: `You are a knowledgeable sales agent for ACME SaaS. 
    Help prospects understand our features, pricing, and how we can solve their problems. 
    Be consultative, not pushy. Focus on value and fit.`,
    model: {
      name: 'gpt-4',
      provider: 'openai'
    },
    tools: {
      scheduleDemo: {
        name: 'Schedule Demo',
        description: 'Schedule a product demo for prospects',
        execute: async ({ email, preferredTime }: { email: string; preferredTime: string }) => {
          return {
            success: true,
            message: `Demo scheduled for ${email} at ${preferredTime}`,
            demoId: `DEMO-${Date.now()}`
          };
        }
      }
    }
  });

  console.log('✅ Created intelligent agents with MongoDB superpowers!\n');
  return { supportAgent, salesAgent };
}

/**
 * STEP 5: Test the intelligent agents - See the magic!
 */
async function testIntelligentAgents(agents: any) {
  console.log('🧪 Testing intelligent agents with MongoDB context injection...\n');

  const { supportAgent, salesAgent } = agents;

  // Test 1: Customer Support Query
  console.log('📞 Customer Support Test:');
  console.log('Customer: "I forgot my password and can\'t log in. Help!"');
  
  const supportResponse = await supportAgent.generate([
    { role: 'user', content: 'I forgot my password and can\'t log in. Help!' }
  ], {
    conversationId: 'customer_support_001'
  });

  console.log('🤖 Support Agent Response:');
  console.log(supportResponse.text);
  console.log(`📊 Context Sources: ${supportResponse.enhancedContext?.map(c => c.source).join(', ')}\n`);

  // Test 2: Sales Inquiry
  console.log('💼 Sales Inquiry Test:');
  console.log('Prospect: "What are your pricing plans? I have a team of 20 people."');
  
  const salesResponse = await salesAgent.generate([
    { role: 'user', content: 'What are your pricing plans? I have a team of 20 people.' }
  ], {
    conversationId: 'sales_prospect_001'
  });

  console.log('🤖 Sales Agent Response:');
  console.log(salesResponse.text);
  console.log(`📊 Context Sources: ${salesResponse.enhancedContext?.map(c => c.source).join(', ')}\n`);

  // Test 3: Complex Integration Question
  console.log('🔧 Technical Integration Test:');
  console.log('Customer: "How do I set up the Slack integration?"');
  
  const techResponse = await supportAgent.generate([
    { role: 'user', content: 'How do I set up the Slack integration?' }
  ], {
    conversationId: 'customer_support_002'
  });

  console.log('🤖 Support Agent Response:');
  console.log(techResponse.text);
  console.log(`📊 Context Sources: ${techResponse.enhancedContext?.map(c => c.source).join(', ')}\n`);
}

/**
 * STEP 6: Show the revolutionary results
 */
async function showRevolutionaryResults(brain: UniversalAIBrain) {
  console.log('📊 REVOLUTIONARY RESULTS - What ACME SaaS achieved:\n');

  // Get brain statistics
  const brainStats = await brain.getStats();
  const collectionStats = await brain.collections.getCollectionStats();

  console.log('🎯 BUSINESS IMPACT:');
  console.log('   ✅ Chose Mastra framework (their preference)');
  console.log('   ✅ Added Universal AI Brain (ONE line of code)');
  console.log('   ✅ Instantly got production-ready AI infrastructure');
  console.log('   ✅ Agents that remember every conversation');
  console.log('   ✅ Intelligent context from company knowledge');
  console.log('   ✅ Real-time learning and improvement');
  console.log('   ✅ MongoDB Atlas scalability and reliability\n');

  console.log('📈 TECHNICAL ACHIEVEMENTS:');
  console.log(`   • MongoDB Collections: ${Object.keys(collectionStats).length} initialized`);
  console.log(`   • Vector Search: ${brainStats.collections?.vectorStore?.documentCount || 0} documents indexed`);
  console.log(`   • Conversations: ${brainStats.collections?.interactions || 0} interactions stored`);
  console.log(`   • System Health: ${brainStats.isHealthy ? 'Healthy' : 'Issues detected'}`);
  console.log(`   • Framework: Mastra with MongoDB superpowers\n`);

  console.log('⏱️ TIME TO VALUE:');
  console.log('   • Setup Time: 30 minutes');
  console.log('   • Framework Choice: Preserved (Mastra)');
  console.log('   • Intelligence Gained: 90% complete AI system');
  console.log('   • Infrastructure: Production-ready MongoDB Atlas\n');

  console.log('🚀 WHAT THEY BUILT:');
  console.log('   • Intelligent customer support agents');
  console.log('   • Smart sales agents with pricing knowledge');
  console.log('   • Persistent memory across all conversations');
  console.log('   • Semantic search across company knowledge');
  console.log('   • Real-time performance monitoring');
  console.log('   • Scalable MongoDB infrastructure\n');

  console.log('🎉 THE UNIVERSAL AI BRAIN REVOLUTION:');
  console.log('   🔥 Company chose their favorite framework (Mastra)');
  console.log('   🔥 Added MongoDB intelligence with ONE line of code');
  console.log('   🔥 Got 90% complete AI system instantly');
  console.log('   🔥 Focus on business logic, not infrastructure');
  console.log('   🔥 Production-ready from day one\n');

  console.log('💡 THIS IS THE FUTURE OF AI DEVELOPMENT! 🧠⚡');
}

/**
 * Main execution - The complete journey from framework choice to 90% done!
 */
async function main() {
  try {
    console.log('🌟 UNIVERSAL AI BRAIN - COMPANY SUCCESS STORY 🌟\n');
    console.log('Demonstrating how ANY company can choose ANY framework');
    console.log('and be 90% done building the smartest AI system!\n');
    console.log('=' .repeat(60) + '\n');

    // The complete journey
    const brain = await setupUniversalAIBrain();
    const enhancedMastra = await integrateWithMastra(brain);
    await storeCompanyKnowledge(brain);
    const agents = await createIntelligentAgents(enhancedMastra);
    await testIntelligentAgents(agents);
    await showRevolutionaryResults(brain);

    console.log('=' .repeat(60));
    console.log('🎯 MISSION ACCOMPLISHED!');
    console.log('ACME SaaS chose Mastra and got MongoDB superpowers!');
    console.log('They are now 90% done with the smartest AI system possible! 🚀');

  } catch (error) {
    console.error('❌ Error in company success story:', error);
  }
}

// Run the complete example
if (require.main === module) {
  main();
}

export { main as companyChoosesMastraExample };
