import { CustomerServiceAgent } from './CustomerServiceAgent';
import { MongoConnection } from '@mongodb-ai/core';
import { MongoMemoryServer } from 'mongodb-memory-server';

describe('CustomerServiceAgent', () => {
  let mongoServer: MongoMemoryServer;
  let agent: CustomerServiceAgent;
  let mongoUri: string;

  beforeAll(async () => {
    // Start in-memory MongoDB instance
    mongoServer = await MongoMemoryServer.create();
    mongoUri = mongoServer.getUri();
    
    // Initialize agent with test database
    agent = new CustomerServiceAgent(mongoUri, 'test_ai_agents');
    
    // Connect to MongoDB
    const connection = MongoConnection.getInstance(mongoUri, 'test_ai_agents');
    await connection.connect();
  });

  afterAll(async () => {
    // Clean up
    const connection = MongoConnection.getInstance(mongoUri, 'test_ai_agents');
    await connection.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Initialize with sample data for each test
    await agent.initializeWithSampleData();
  });

  describe('handleCustomerMessage', () => {
    it('should handle a technical support request', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001',
        'I\'m experiencing API timeouts on my production environment',
        'chat'
      );

      expect(response).toBeDefined();
      expect(response.content).toContain('technical');
      expect(response.strategy).toBe('technical_detailed');
      expect(response.confidence).toBeGreaterThan(0.5);
      expect(response.response_time_ms).toBeGreaterThan(0);
      expect(response.suggested_actions).toContain('Check system status');
    });

    it('should handle a billing inquiry', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_002',
        'I have a question about my billing statement',
        'email'
      );

      expect(response).toBeDefined();
      expect(response.content).toContain('billing');
      expect(response.strategy).toBe('professional_standard');
      expect(response.confidence).toBeGreaterThan(0.5);
      expect(response.suggested_actions).toContain('Review billing history');
    });

    it('should escalate urgent issues', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001',
        'URGENT: Production is down and we\'re losing money!',
        'chat'
      );

      expect(response).toBeDefined();
      expect(response.escalation_needed).toBe(true);
      expect(response.strategy).toBe('urgent_resolution');
      expect(response.content).toContain('urgent');
    });

    it('should handle frustrated customers with empathy', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_002',
        'I\'m really frustrated with this service. This is terrible!',
        'chat'
      );

      expect(response).toBeDefined();
      expect(response.strategy).toBe('empathetic_recovery');
      expect(response.content).toContain('apologize');
    });

    it('should create new customer profile for unknown customer', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_new_999',
        'Hello, I need help with my account',
        'chat'
      );

      expect(response).toBeDefined();
      expect(response.content).toContain('help');
      expect(response.confidence).toBeGreaterThan(0.5);
    });

    it('should handle errors gracefully', async () => {
      // Simulate error by using invalid customer ID format
      const response = await agent.handleCustomerMessage(
        '', // Empty customer ID should cause error
        'Test message',
        'chat'
      );

      expect(response).toBeDefined();
      expect(response.strategy).toBe('fallback_escalation');
      expect(response.escalation_needed).toBe(true);
      expect(response.content).toContain('technical difficulties');
    });
  });

  describe('message analysis', () => {
    it('should detect negative sentiment', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001',
        'This is awful and I hate this service',
        'chat'
      );

      expect(response.strategy).toBe('empathetic_recovery');
    });

    it('should detect critical urgency', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001',
        'EMERGENCY: Critical production issue',
        'chat'
      );

      expect(response.escalation_needed).toBe(true);
      expect(response.strategy).toBe('urgent_resolution');
    });

    it('should detect positive sentiment', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001',
        'Thank you so much for the excellent service!',
        'chat'
      );

      expect(response.confidence).toBeGreaterThan(0.7);
    });
  });

  describe('performance metrics', () => {
    it('should track performance metrics', async () => {
      // Generate some interactions
      await agent.handleCustomerMessage('cust_001', 'Test message 1', 'chat');
      await agent.handleCustomerMessage('cust_002', 'Test message 2', 'email');
      await agent.handleCustomerMessage('cust_001', 'Test message 3', 'chat');

      const metrics = await agent.getPerformanceMetrics('last_24_hours');

      expect(metrics).toBeDefined();
      expect(metrics.total_interactions).toBeGreaterThanOrEqual(3);
      expect(metrics.avg_response_time).toBeGreaterThan(0);
      expect(metrics.avg_satisfaction).toBeGreaterThan(0);
      expect(metrics.escalation_rate).toBeGreaterThanOrEqual(0);
      expect(metrics.resolution_rate).toBeGreaterThanOrEqual(0);
    });

    it('should return zero metrics when no data exists', async () => {
      // Create new agent with different ID to avoid existing data
      const newAgent = new CustomerServiceAgent(mongoUri, 'test_ai_agents', 'test_agent_empty');
      
      const metrics = await newAgent.getPerformanceMetrics('last_24_hours');

      expect(metrics.total_interactions).toBe(0);
      expect(metrics.avg_response_time).toBe(0);
      expect(metrics.escalation_rate).toBe(0);
      expect(metrics.resolution_rate).toBe(0);
    });
  });

  describe('customer insights', () => {
    it('should update customer insights after interactions', async () => {
      const customerId = 'cust_001';
      
      // First interaction
      await agent.handleCustomerMessage(customerId, 'I need technical help', 'chat');
      
      // Second interaction
      await agent.handleCustomerMessage(customerId, 'Thank you for the help!', 'chat');

      // The customer insights should be updated in the database
      // This is tested indirectly through the agent's behavior
      const response = await agent.handleCustomerMessage(customerId, 'Another question', 'chat');
      
      expect(response).toBeDefined();
      expect(response.confidence).toBeGreaterThan(0.5);
    });
  });

  describe('batch processing', () => {
    it('should handle multiple messages efficiently', async () => {
      const messages = [
        { customerId: 'cust_001', message: 'Question 1' },
        { customerId: 'cust_002', message: 'Question 2' },
        { customerId: 'cust_001', message: 'Question 3' },
        { customerId: 'cust_002', message: 'Question 4' }
      ];

      const startTime = Date.now();
      
      const responses = await Promise.all(
        messages.map(({ customerId, message }) => 
          agent.handleCustomerMessage(customerId, message)
        )
      );

      const totalTime = Date.now() - startTime;

      expect(responses).toHaveLength(4);
      expect(responses.every(r => r.confidence > 0.5)).toBe(true);
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
    });
  });

  describe('escalation logic', () => {
    it('should escalate enterprise customers with high urgency', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001', // Enterprise customer
        'This is a high priority issue that needs immediate attention',
        'chat'
      );

      expect(response.escalation_needed).toBe(true);
    });

    it('should not escalate low urgency issues', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_002',
        'I have a general question when you have time',
        'chat'
      );

      expect(response.escalation_needed).toBe(false);
    });
  });

  describe('response personalization', () => {
    it('should personalize responses for enterprise customers', async () => {
      const response = await agent.handleCustomerMessage(
        'cust_001', // Enterprise customer
        'I need help with integration',
        'chat'
      );

      expect(response.content).toContain('enterprise');
      expect(response.strategy).toBe('technical_detailed');
    });

    it('should use appropriate tone for different customer tiers', async () => {
      const enterpriseResponse = await agent.handleCustomerMessage(
        'cust_001', // Enterprise
        'I need assistance',
        'chat'
      );

      const premiumResponse = await agent.handleCustomerMessage(
        'cust_002', // Premium
        'I need assistance',
        'chat'
      );

      expect(enterpriseResponse.content).toContain('enterprise');
      expect(premiumResponse.content).not.toContain('enterprise');
    });
  });
});