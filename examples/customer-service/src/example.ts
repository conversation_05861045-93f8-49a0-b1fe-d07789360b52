#!/usr/bin/env node

import { CustomerServiceAgent } from './CustomerServiceAgent';
import { MongoConnection } from '@mongodb-ai/core';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function runCustomerServiceExample() {
  console.log('🎧 MongoDB AI Agent Boilerplate - Customer Service Example');
  console.log('=' .repeat(60));

  // Check environment variables
  if (!process.env.MONGODB_URI) {
    console.error('❌ MONGODB_URI environment variable is required');
    console.log('Please set MONGODB_URI in your .env file');
    process.exit(1);
  }

  try {
    // Initialize MongoDB connection
    console.log('🔌 Connecting to MongoDB...');
    const connection = MongoConnection.getInstance(
      process.env.MONGODB_URI!,
      process.env.DATABASE_NAME || 'ai_agents'
    );
    await connection.connect();
    console.log('✅ Connected to MongoDB successfully');

    // Initialize customer service agent
    console.log('🤖 Initializing Customer Service Agent...');
    const agent = new CustomerServiceAgent(
      process.env.MONGODB_URI!,
      process.env.DATABASE_NAME || 'ai_agents'
    );

    // Initialize with sample data
    await agent.initializeWithSampleData();

    console.log('\n🎯 Running Customer Service Scenarios...\n');

    // Scenario 1: Technical Support Request
    console.log('📋 Scenario 1: Technical Support Request');
    console.log('-'.repeat(40));
    const response1 = await agent.handleCustomerMessage(
      'cust_001',
      'I\'m experiencing API timeouts on my production environment. This is urgent!',
      'chat'
    );
    
    console.log('Customer Message: "I\'m experiencing API timeouts on my production environment. This is urgent!"');
    console.log(`Agent Response: "${response1.content}"`);
    console.log(`Strategy: ${response1.strategy}`);
    console.log(`Confidence: ${response1.confidence}`);
    console.log(`Escalation Needed: ${response1.escalation_needed}`);
    console.log(`Response Time: ${response1.response_time_ms}ms`);
    console.log('');

    // Scenario 2: Billing Inquiry
    console.log('📋 Scenario 2: Billing Inquiry');
    console.log('-'.repeat(40));
    const response2 = await agent.handleCustomerMessage(
      'cust_002',
      'I have a question about my billing statement. There seems to be an unexpected charge.',
      'email'
    );
    
    console.log('Customer Message: "I have a question about my billing statement. There seems to be an unexpected charge."');
    console.log(`Agent Response: "${response2.content}"`);
    console.log(`Strategy: ${response2.strategy}`);
    console.log(`Confidence: ${response2.confidence}`);
    console.log(`Escalation Needed: ${response2.escalation_needed}`);
    console.log(`Response Time: ${response2.response_time_ms}ms`);
    console.log('');

    // Scenario 3: Frustrated Customer
    console.log('📋 Scenario 3: Frustrated Customer');
    console.log('-'.repeat(40));
    const response3 = await agent.handleCustomerMessage(
      'cust_001',
      'This is the third time I\'m contacting support about the same issue. I\'m really frustrated!',
      'chat'
    );
    
    console.log('Customer Message: "This is the third time I\'m contacting support about the same issue. I\'m really frustrated!"');
    console.log(`Agent Response: "${response3.content}"`);
    console.log(`Strategy: ${response3.strategy}`);
    console.log(`Confidence: ${response3.confidence}`);
    console.log(`Escalation Needed: ${response3.escalation_needed}`);
    console.log(`Response Time: ${response3.response_time_ms}ms`);
    console.log('');

    // Scenario 4: General Inquiry
    console.log('📋 Scenario 4: General Inquiry');
    console.log('-'.repeat(40));
    const response4 = await agent.handleCustomerMessage(
      'cust_002',
      'Hi, I\'m interested in learning more about your enterprise features.',
      'chat'
    );
    
    console.log('Customer Message: "Hi, I\'m interested in learning more about your enterprise features."');
    console.log(`Agent Response: "${response4.content}"`);
    console.log(`Strategy: ${response4.strategy}`);
    console.log(`Confidence: ${response4.confidence}`);
    console.log(`Escalation Needed: ${response4.escalation_needed}`);
    console.log(`Response Time: ${response4.response_time_ms}ms`);
    console.log('');

    // Get performance metrics
    console.log('📊 Agent Performance Metrics');
    console.log('-'.repeat(40));
    const metrics = await agent.getPerformanceMetrics('last_24_hours');
    console.log(`Total Interactions: ${metrics.total_interactions}`);
    console.log(`Average Response Time: ${metrics.avg_response_time}ms`);
    console.log(`Average Satisfaction: ${metrics.avg_satisfaction}/5.0`);
    console.log(`Escalation Rate: ${metrics.escalation_rate}%`);
    console.log(`Resolution Rate: ${metrics.resolution_rate}%`);
    console.log('');

    // Demonstrate batch processing
    console.log('📦 Batch Processing Example');
    console.log('-'.repeat(40));
    const batchMessages = [
      { customerId: 'cust_001', message: 'Quick question about API rate limits' },
      { customerId: 'cust_002', message: 'Need help with integration setup' },
      { customerId: 'cust_001', message: 'Thank you for the previous help!' }
    ];

    console.log('Processing 3 messages in batch...');
    const startTime = Date.now();
    
    const batchResponses = await Promise.all(
      batchMessages.map(({ customerId, message }) => 
        agent.handleCustomerMessage(customerId, message)
      )
    );

    const batchTime = Date.now() - startTime;
    console.log(`✅ Processed ${batchResponses.length} messages in ${batchTime}ms`);
    console.log(`Average time per message: ${Math.round(batchTime / batchResponses.length)}ms`);
    console.log('');

    // Show what's stored in MongoDB
    console.log('🗄️ Data Stored in MongoDB');
    console.log('-'.repeat(40));
    console.log('Collections created:');
    console.log('  ✅ customers - Customer profiles and preferences');
    console.log('  ✅ customer_interactions - Full interaction history');
    console.log('  ✅ vector_embeddings - Semantic search data');
    console.log('  ✅ agent_performance_metrics - Performance tracking');
    console.log('');
    console.log('Features demonstrated:');
    console.log('  ✅ Sentiment analysis and urgency detection');
    console.log('  ✅ Personalized responses based on customer tier');
    console.log('  ✅ Automatic escalation logic');
    console.log('  ✅ Performance metrics tracking');
    console.log('  ✅ Vector embeddings for future similarity search');
    console.log('  ✅ Customer insight learning and adaptation');
    console.log('');

    console.log('🎉 Customer Service Agent Example Completed Successfully!');
    console.log('');
    console.log('Next Steps:');
    console.log('  1. Explore the data in MongoDB Atlas');
    console.log('  2. Try the hybrid search capabilities');
    console.log('  3. Customize the agent for your use case');
    console.log('  4. Integrate with your existing systems');
    console.log('');

  } catch (error) {
    console.error('❌ Error running customer service example:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down gracefully...');
  try {
    const connection = MongoConnection.getInstance(
      process.env.MONGODB_URI!,
      process.env.DATABASE_NAME || 'ai_agents'
    );
    await connection.disconnect();
    console.log('✅ Disconnected from MongoDB');
  } catch (error) {
    console.error('Error during shutdown:', error);
  }
  process.exit(0);
});

// Run the example
if (require.main === module) {
  runCustomerServiceExample().catch(console.error);
}

export { runCustomerServiceExample };