import { Db, ObjectId } from 'mongodb';
import { 
  MongoConnection, 
  HybridSearchEngine, 
  IDataStore,
  MongoDataStore,
  HybridSearchResult 
} from '@mongodb-ai/core';
import { v4 as uuidv4 } from 'uuid';

// Interfaces for customer service domain
export interface Customer {
  _id?: ObjectId;
  customer_id: string;
  profile: {
    name: string;
    email: string;
    company?: string;
    tier: 'basic' | 'premium' | 'enterprise';
    preferences: {
      communication_style: 'professional' | 'empathetic' | 'technical';
      response_time_expectation: string;
      preferred_channel: 'email' | 'chat' | 'phone';
    };
  };
  interaction_history: CustomerInteractionSummary[];
  insights?: {
    communication_style?: string;
    escalation_triggers?: string[];
    success_patterns?: string[];
    satisfaction_trend?: number;
    last_interaction?: Date;
  };
  created_at: Date;
  updated_at: Date;
}

export interface CustomerInteractionSummary {
  timestamp: Date;
  type: string;
  issue: string;
  resolution?: string;
  satisfaction?: number;
  agent_id: string;
}

export interface CustomerInteraction {
  _id?: ObjectId;
  interaction_id: string;
  customer_id: string;
  timestamp: Date;
  channel: string;
  message: {
    content: string;
    sentiment: 'positive' | 'neutral' | 'negative' | 'concerned';
    urgency: 'low' | 'medium' | 'high' | 'critical';
    embedding?: number[];
  };
  agent_response: {
    content: string;
    strategy: string;
    confidence: number;
    response_time_ms: number;
    suggested_actions?: string[];
  };
  resolution?: {
    status: 'pending' | 'in_progress' | 'resolved' | 'escalated';
    steps_taken?: string[];
    satisfaction_score?: number;
  };
  metadata: {
    agent_id: string;
    model_used?: string;
    cost?: number;
    tokens_used?: number;
  };
}

export interface MessageAnalysis {
  sentiment: 'positive' | 'neutral' | 'negative' | 'concerned';
  urgency: 'low' | 'medium' | 'high' | 'critical';
  intent: string;
  entities: string[];
  embedding: number[];
  confidence: number;
}

export interface SimilarIssue {
  issue: string;
  resolution: string;
  satisfaction: number;
  similarity: number;
  customer_tier?: string;
}

export interface CustomerServiceResponse {
  content: string;
  strategy: string;
  confidence: number;
  suggested_actions: string[];
  escalation_needed: boolean;
  response_time_ms: number;
  reasoning?: string;
}

export interface PerformanceMetrics {
  agent_id: string;
  timestamp: Date;
  time_window: string;
  metrics: {
    total_interactions: number;
    avg_response_time_ms: number;
    avg_confidence_score: number;
    escalation_rate: number;
    resolution_rate: number;
    avg_satisfaction: number;
    cost_per_interaction: number;
  };
  breakdown_by_channel: Record<string, any>;
  breakdown_by_tier: Record<string, any>;
}

export class CustomerServiceAgent {
  private db: Db;
  private searchEngine: HybridSearchEngine;
  private customerStore: IDataStore<Customer>;
  private interactionStore: IDataStore<CustomerInteraction>;
  private agentId: string;
  private startTime: number = 0;

  constructor(mongoUri: string, dbName: string, agentId: string = 'customer_service_agent_v1') {
    const connection = MongoConnection.getInstance(mongoUri, dbName);
    this.db = connection.getDb();
    this.searchEngine = new HybridSearchEngine(this.db);
    this.customerStore = new MongoDataStore(this.db, 'customers');
    this.interactionStore = new MongoDataStore(this.db, 'customer_interactions');
    this.agentId = agentId;
  }

  /**
   * Main method to handle customer messages
   */
  async handleCustomerMessage(
    customerId: string, 
    message: string,
    channel: string = 'chat'
  ): Promise<CustomerServiceResponse> {
    this.startTime = Date.now();

    try {
      console.log(`🎧 Processing message from customer ${customerId}`);

      // 1. Load customer profile and history
      const customer = await this.loadCustomerProfile(customerId);
      
      // 2. Analyze message sentiment and urgency
      const messageAnalysis = await this.analyzeMessage(message);
      
      // 3. Search for similar issues and solutions
      const similarIssues = await this.findSimilarIssues(message, customer);
      
      // 4. Generate personalized response
      const response = await this.generateResponse(
        customer, 
        message, 
        messageAnalysis, 
        similarIssues
      );
      
      // 5. Log interaction
      await this.logInteraction(customerId, message, response, channel, messageAnalysis);
      
      // 6. Update customer insights
      await this.updateCustomerInsights(customerId, messageAnalysis, response);
      
      // 7. Track performance metrics
      await this.trackPerformance(customerId, response);

      console.log(`✅ Response generated in ${response.response_time_ms}ms with confidence ${response.confidence}`);
      return response;

    } catch (error) {
      console.error('Customer service agent error:', error);
      return this.generateFallbackResponse(customerId, message);
    }
  }

  /**
   * Load customer profile with history
   */
  private async loadCustomerProfile(customerId: string): Promise<Customer> {
    const customers = await this.customerStore.find({ customer_id: customerId });
    if (customers.length === 0) {
      // Create new customer profile
      const newCustomer: Customer = {
        customer_id: customerId,
        profile: {
          name: `Customer ${customerId}`,
          email: `${customerId}@example.com`,
          tier: 'basic',
          preferences: {
            communication_style: 'professional',
            response_time_expectation: '< 1 hour',
            preferred_channel: 'chat'
          }
        },
        interaction_history: [],
        created_at: new Date(),
        updated_at: new Date()
      };
      
      return await this.customerStore.create(newCustomer);
    }
    
    return customers[0];
  }

  /**
   * Analyze customer message for sentiment, urgency, and intent
   */
  private async analyzeMessage(message: string): Promise<MessageAnalysis> {
    // Simple rule-based analysis (in production, use OpenAI or similar)
    const sentiment = this.detectSentiment(message);
    const urgency = this.detectUrgency(message);
    const intent = this.detectIntent(message);
    const entities = this.extractEntities(message);
    const embedding = await this.generateEmbedding(message);

    return {
      sentiment,
      urgency,
      intent,
      entities,
      embedding,
      confidence: 0.85 // Mock confidence score
    };
  }

  /**
   * Simple sentiment detection
   */
  private detectSentiment(message: string): MessageAnalysis['sentiment'] {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('angry') || lowerMessage.includes('frustrated') || 
        lowerMessage.includes('terrible') || lowerMessage.includes('awful')) {
      return 'negative';
    }
    
    if (lowerMessage.includes('urgent') || lowerMessage.includes('critical') || 
        lowerMessage.includes('emergency') || lowerMessage.includes('production')) {
      return 'concerned';
    }
    
    if (lowerMessage.includes('thank') || lowerMessage.includes('great') || 
        lowerMessage.includes('excellent') || lowerMessage.includes('perfect')) {
      return 'positive';
    }
    
    return 'neutral';
  }

  /**
   * Simple urgency detection
   */
  private detectUrgency(message: string): MessageAnalysis['urgency'] {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('emergency') || lowerMessage.includes('critical') || 
        lowerMessage.includes('production down') || lowerMessage.includes('urgent')) {
      return 'critical';
    }
    
    if (lowerMessage.includes('asap') || lowerMessage.includes('soon') || 
        lowerMessage.includes('priority') || lowerMessage.includes('important')) {
      return 'high';
    }
    
    if (lowerMessage.includes('when possible') || lowerMessage.includes('no rush')) {
      return 'low';
    }
    
    return 'medium';
  }

  /**
   * Simple intent detection
   */
  private detectIntent(message: string): string {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('billing') || lowerMessage.includes('payment') || 
        lowerMessage.includes('invoice') || lowerMessage.includes('charge')) {
      return 'billing_inquiry';
    }
    
    if (lowerMessage.includes('api') || lowerMessage.includes('integration') || 
        lowerMessage.includes('code') || lowerMessage.includes('technical')) {
      return 'technical_support';
    }
    
    if (lowerMessage.includes('account') || lowerMessage.includes('login') || 
        lowerMessage.includes('password') || lowerMessage.includes('access')) {
      return 'account_support';
    }
    
    return 'general_inquiry';
  }

  /**
   * Simple entity extraction
   */
  private extractEntities(message: string): string[] {
    const entities: string[] = [];
    
    // Extract common entities (simplified)
    if (message.includes('API')) entities.push('API');
    if (message.includes('billing')) entities.push('billing');
    if (message.includes('account')) entities.push('account');
    if (message.includes('production')) entities.push('production');
    
    return entities;
  }

  /**
   * Generate embedding for message (mock implementation)
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    // In production, use OpenAI embeddings or similar
    // For now, return a mock embedding
    return Array(1024).fill(0).map(() => Math.random() * 2 - 1);
  }

  /**
   * Find similar issues using hybrid search
   */
  private async findSimilarIssues(
    message: string, 
    customer: Customer
  ): Promise<SimilarIssue[]> {
    try {
      const results = await this.searchEngine.search(message, {
        source_type: 'customer_interaction',
        metadata_filters: {
          customer_tier: customer.profile.tier,
          'resolution.status': 'resolved'
        }
      }, {
        limit: 5,
        vector_weight: 0.7,
        text_weight: 0.3
      });

      return results.map(result => ({
        issue: result.content.text || 'Similar issue found',
        resolution: result.metadata.resolution || 'Resolution applied',
        satisfaction: result.metadata.satisfaction_score || 4.0,
        similarity: result.scores.combined_score,
        customer_tier: result.metadata.customer_tier
      }));
    } catch (error) {
      console.warn('Failed to find similar issues:', error);
      return [];
    }
  }

  /**
   * Generate personalized response
   */
  private async generateResponse(
    customer: Customer,
    message: string,
    analysis: MessageAnalysis,
    similarIssues: SimilarIssue[]
  ): Promise<CustomerServiceResponse> {
    // Build context for response generation
    const context = {
      customer_name: customer.profile.name,
      customer_tier: customer.profile.tier,
      communication_style: customer.profile.preferences.communication_style,
      message_sentiment: analysis.sentiment,
      message_urgency: analysis.urgency,
      intent: analysis.intent,
      similar_solutions: similarIssues.slice(0, 3), // Top 3 similar issues
      interaction_history: customer.interaction_history.slice(-3) // Last 3 interactions
    };

    // Generate response based on context (simplified AI simulation)
    const response = await this.generateAIResponse(context, message);

    return {
      content: response.content,
      strategy: this.determineResponseStrategy(customer, analysis),
      confidence: response.confidence,
      suggested_actions: response.suggested_actions,
      escalation_needed: this.shouldEscalate(analysis, customer),
      response_time_ms: Date.now() - this.startTime,
      reasoning: response.reasoning
    };
  }

  /**
   * Generate AI response (mock implementation)
   */
  private async generateAIResponse(context: any, message: string): Promise<{
    content: string;
    confidence: number;
    suggested_actions: string[];
    reasoning: string;
  }> {
    // In production, this would call OpenAI or similar
    // For now, generate a contextual response based on analysis

    let content = '';
    const suggestedActions: string[] = [];
    let confidence = 0.8;

    // Personalize greeting
    const greeting = context.customer_tier === 'enterprise' 
      ? `Hello ${context.customer_name}, thank you for contacting our enterprise support.`
      : `Hi ${context.customer_name}, I'm here to help you today.`;

    // Handle based on intent
    switch (context.intent) {
      case 'technical_support':
        content = `${greeting} I understand you're experiencing a technical issue. Let me help you resolve this quickly.`;
        suggestedActions.push('Check system status', 'Review API logs', 'Escalate to technical team');
        break;
        
      case 'billing_inquiry':
        content = `${greeting} I can help you with your billing question. Let me look into your account details.`;
        suggestedActions.push('Review billing history', 'Check payment methods', 'Contact billing team');
        break;
        
      case 'account_support':
        content = `${greeting} I'll assist you with your account issue right away.`;
        suggestedActions.push('Verify account details', 'Reset credentials', 'Update account settings');
        break;
        
      default:
        content = `${greeting} I'm here to help with your inquiry. Could you provide a bit more detail about what you need assistance with?`;
        suggestedActions.push('Gather more information', 'Clarify requirements');
    }

    // Adjust tone based on sentiment and urgency
    if (context.message_urgency === 'critical') {
      content += ' I understand this is urgent and will prioritize your request.';
      confidence = 0.9;
    }

    if (context.message_sentiment === 'negative') {
      content += ' I apologize for any inconvenience you\'ve experienced.';
    }

    // Add similar solutions if available
    if (context.similar_solutions.length > 0) {
      content += ' Based on similar cases, I have some potential solutions to explore.';
      confidence = Math.min(confidence + 0.1, 0.95);
    }

    return {
      content,
      confidence,
      suggested_actions: suggestedActions,
      reasoning: `Response generated based on ${context.intent} intent, ${context.message_urgency} urgency, and ${context.similar_solutions.length} similar cases`
    };
  }

  /**
   * Determine response strategy
   */
  private determineResponseStrategy(customer: Customer, analysis: MessageAnalysis): string {
    if (analysis.urgency === 'critical') {
      return 'urgent_resolution';
    }
    
    if (analysis.sentiment === 'negative') {
      return 'empathetic_recovery';
    }
    
    if (customer.profile.preferences.communication_style === 'technical') {
      return 'technical_detailed';
    }
    
    return 'professional_standard';
  }

  /**
   * Determine if escalation is needed
   */
  private shouldEscalate(analysis: MessageAnalysis, customer: Customer): boolean {
    // Escalate if critical urgency
    if (analysis.urgency === 'critical') {
      return true;
    }
    
    // Escalate if enterprise customer with high urgency
    if (customer.profile.tier === 'enterprise' && analysis.urgency === 'high') {
      return true;
    }
    
    // Escalate if negative sentiment and multiple recent interactions
    if (analysis.sentiment === 'negative' && customer.interaction_history.length > 3) {
      return true;
    }
    
    return false;
  }

  /**
   * Log customer interaction
   */
  private async logInteraction(
    customerId: string,
    message: string,
    response: CustomerServiceResponse,
    channel: string,
    analysis: MessageAnalysis
  ): Promise<void> {
    const interaction: CustomerInteraction = {
      interaction_id: uuidv4(),
      customer_id: customerId,
      timestamp: new Date(),
      channel,
      message: {
        content: message,
        sentiment: analysis.sentiment,
        urgency: analysis.urgency,
        embedding: analysis.embedding
      },
      agent_response: {
        content: response.content,
        strategy: response.strategy,
        confidence: response.confidence,
        response_time_ms: response.response_time_ms,
        suggested_actions: response.suggested_actions
      },
      resolution: {
        status: response.escalation_needed ? 'escalated' : 'resolved'
      },
      metadata: {
        agent_id: this.agentId,
        model_used: 'mock_ai_v1',
        cost: 0.01, // Mock cost
        tokens_used: message.length + response.content.length
      }
    };

    await this.interactionStore.create(interaction);

    // Also store as vector embedding for future similarity search
    await this.storeInteractionEmbedding(interaction);
  }

  /**
   * Store interaction as vector embedding for future search
   */
  private async storeInteractionEmbedding(interaction: CustomerInteraction): Promise<void> {
    try {
      const embeddingDoc = {
        embedding_id: `interaction_${interaction.interaction_id}`,
        source_type: 'customer_interaction',
        source_id: interaction.interaction_id,
        agent_id: this.agentId,
        created_at: new Date(),
        embedding: {
          values: interaction.message.embedding || [],
          model: 'mock_embedding_v1',
          dimensions: 1024
        },
        content: {
          text: interaction.message.content,
          summary: `${interaction.message.sentiment} ${interaction.message.urgency} inquiry`,
          confidence: interaction.agent_response.confidence
        },
        metadata: {
          customer_tier: 'unknown', // Would be filled from customer data
          resolution_status: interaction.resolution?.status,
          satisfaction_score: 4.0, // Mock satisfaction
          agent_strategy: interaction.agent_response.strategy,
          response_time_ms: interaction.agent_response.response_time_ms
        }
      };

      await this.db.collection('vector_embeddings').insertOne(embeddingDoc);
    } catch (error) {
      console.warn('Failed to store interaction embedding:', error);
    }
  }

  /**
   * Update customer insights based on interaction
   */
  private async updateCustomerInsights(
    customerId: string,
    analysis: MessageAnalysis,
    response: CustomerServiceResponse
  ): Promise<void> {
    try {
      const insights = {
        last_interaction: new Date(),
        communication_patterns: {
          recent_sentiment: analysis.sentiment,
          recent_urgency: analysis.urgency,
          preferred_strategy: response.strategy
        },
        agent_effectiveness: {
          last_confidence: response.confidence,
          escalation_needed: response.escalation_needed
        }
      };

      // Update customer with new insights
      const customers = await this.customerStore.find({ customer_id: customerId });
      if (customers.length > 0 && customers[0]._id) {
        await this.customerStore.update(customers[0]._id.toString(), {
          insights: insights,
          updated_at: new Date()
        } as Partial<Customer>);
      }
    } catch (error) {
      console.warn('Failed to update customer insights:', error);
    }
  }

  /**
   * Track performance metrics
   */
  private async trackPerformance(
    customerId: string,
    response: CustomerServiceResponse
  ): Promise<void> {
    try {
      const metrics = {
        agent_id: this.agentId,
        timestamp: new Date(),
        customer_id: customerId,
        interaction_id: uuidv4(),
        metrics: {
          response_time_ms: response.response_time_ms,
          confidence_score: response.confidence,
          escalation_needed: response.escalation_needed,
          strategy_used: response.strategy
        },
        metadata: {
          channel: 'chat',
          customer_tier: 'unknown' // Would be filled from customer data
        }
      };

      await this.db.collection('agent_performance_metrics').insertOne(metrics);
    } catch (error) {
      console.warn('Failed to track performance metrics:', error);
    }
  }

  /**
   * Generate fallback response for errors
   */
  private generateFallbackResponse(customerId: string, message: string): CustomerServiceResponse {
    return {
      content: "I apologize, but I'm experiencing some technical difficulties. Let me connect you with a human agent who can assist you right away.",
      strategy: 'fallback_escalation',
      confidence: 0.5,
      suggested_actions: ['Escalate to human agent', 'Log technical error'],
      escalation_needed: true,
      response_time_ms: Date.now() - this.startTime,
      reasoning: 'Fallback response due to system error'
    };
  }

  /**
   * Get performance metrics for the agent
   */
  async getPerformanceMetrics(timeWindow: string = 'last_24_hours'): Promise<{
    avg_response_time: number;
    avg_satisfaction: number;
    escalation_rate: number;
    resolution_rate: number;
    total_interactions: number;
  }> {
    try {
      const since = new Date();
      since.setHours(since.getHours() - 24); // Last 24 hours

      const metrics = await this.db.collection('agent_performance_metrics').aggregate([
        {
          $match: {
            agent_id: this.agentId,
            timestamp: { $gte: since }
          }
        },
        {
          $group: {
            _id: null,
            avg_response_time: { $avg: '$metrics.response_time_ms' },
            total_interactions: { $sum: 1 },
            escalations: { $sum: { $cond: ['$metrics.escalation_needed', 1, 0] } },
            total_confidence: { $avg: '$metrics.confidence_score' }
          }
        }
      ]).toArray();

      if (metrics.length === 0) {
        return {
          avg_response_time: 0,
          avg_satisfaction: 0,
          escalation_rate: 0,
          resolution_rate: 0,
          total_interactions: 0
        };
      }

      const result = metrics[0];
      return {
        avg_response_time: Math.round(result.avg_response_time || 0),
        avg_satisfaction: 4.0, // Mock satisfaction score
        escalation_rate: Math.round((result.escalations / result.total_interactions) * 100),
        resolution_rate: Math.round((1 - (result.escalations / result.total_interactions)) * 100),
        total_interactions: result.total_interactions
      };
    } catch (error) {
      console.error('Failed to get performance metrics:', error);
      return {
        avg_response_time: 0,
        avg_satisfaction: 0,
        escalation_rate: 0,
        resolution_rate: 0,
        total_interactions: 0
      };
    }
  }

  /**
   * Initialize agent with sample data
   */
  async initializeWithSampleData(): Promise<void> {
    console.log('🔄 Initializing customer service agent with sample data...');

    // Create sample customers
    const sampleCustomers: Customer[] = [
      {
        customer_id: 'cust_001',
        profile: {
          name: 'Sarah Chen',
          email: '<EMAIL>',
          company: 'TechFlow AI',
          tier: 'enterprise',
          preferences: {
            communication_style: 'technical',
            response_time_expectation: '< 30 minutes',
            preferred_channel: 'chat'
          }
        },
        interaction_history: [],
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        customer_id: 'cust_002',
        profile: {
          name: 'Marcus Rodriguez',
          email: '<EMAIL>',
          tier: 'premium',
          preferences: {
            communication_style: 'professional',
            response_time_expectation: '< 2 hours',
            preferred_channel: 'email'
          }
        },
        interaction_history: [],
        created_at: new Date(),
        updated_at: new Date()
      }
    ];

    for (const customer of sampleCustomers) {
      await this.customerStore.create(customer);
    }

    console.log('✅ Sample data initialized successfully');
  }
}