{"name": "customer-service-agent-example", "version": "1.0.0", "description": "Customer Service AI Agent example using MongoDB AI Agent Boilerplate", "main": "dist/example.js", "scripts": {"build": "tsc", "start": "node dist/example.js", "dev": "ts-node src/example.ts", "test": "jest", "clean": "rm -rf dist"}, "dependencies": {"@mongodb-ai/core": "workspace:*", "mongodb": "^6.5.0", "dotenv": "^16.4.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.11.24", "@types/uuid": "^9.0.8", "typescript": "^5.3.3", "ts-node": "^10.9.2", "jest": "^29.7.0", "@types/jest": "^29.5.12"}, "keywords": ["mongodb", "ai", "agent", "customer-service", "chatbot", "vector-search", "machine-learning"], "author": "MongoDB AI Team", "license": "Apache-2.0"}