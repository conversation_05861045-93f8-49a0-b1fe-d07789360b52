# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/
DATABASE_NAME=ai_agents

# AI Service Configuration (Optional - for production AI integration)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Agent Configuration
AGENT_ID=customer_service_agent_v1
AGENT_CONFIDENCE_THRESHOLD=0.7
ESCALATION_THRESHOLD=0.3
MAX_RESPONSE_TIME_MS=5000

# Performance Monitoring
ENABLE_METRICS=true
METRICS_COLLECTION_INTERVAL=60000

# Logging
LOG_LEVEL=info
ENABLE_DEBUG_LOGS=false