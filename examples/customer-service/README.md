# 🎧 Customer Service AI Agent Example

This example demonstrates a complete customer service AI agent built on the MongoDB AI Agent Boilerplate. The agent can:

- 🔍 **Research customer history** and previous interactions
- 🧠 **Understand context** using semantic search
- 💬 **Generate personalized responses** based on customer profile
- 📊 **Track performance metrics** and satisfaction scores
- 🔄 **Escalate to human agents** when needed

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your MongoDB URI and API keys

# Run the example
npm run start
```

## 🏗️ Architecture

```mermaid
graph TD
    A[Customer Message] --> B[Customer Service Agent]
    B --> C[Research Customer History]
    C --> D[Semantic Search for Similar Issues]
    D --> E[Generate Personalized Response]
    E --> F[Update Customer Profile]
    F --> G[Log Interaction]
    G --> H[Monitor Performance]
    
    subgraph "MongoDB Collections"
        I[customers]
        J[customer_interactions]
        K[vector_embeddings]
        L[agent_performance_metrics]
    end
    
    C --> I
    D --> K
    F --> J
    H --> L
```

## 📊 Data Flow

### 1. Customer Profile
```typescript
{
  customer_id: "cust_12345",
  profile: {
    name: "<PERSON>",
    email: "<EMAIL>",
    company: "TechFlow AI",
    tier: "enterprise",
    preferences: {
      communication_style: "technical",
      response_time_expectation: "< 2 hours",
      preferred_channel: "email"
    }
  },
  interaction_history: [
    {
      timestamp: "2024-01-20T10:30:00Z",
      type: "support_ticket",
      issue: "API rate limiting",
      resolution: "increased_limits",
      satisfaction: 4.2,
      agent_id: "support_agent_v1"
    }
  ],
  insights: {
    communication_style: "prefers_technical_details",
    escalation_triggers: ["billing_issues", "downtime"],
    success_patterns: ["detailed_explanations", "follow_up"]
  }
}
```

### 2. Interaction Processing
```typescript
{
  interaction_id: "int_67890",
  customer_id: "cust_12345",
  timestamp: "2024-01-20T15:30:00Z",
  channel: "chat",
  message: {
    content: "I'm experiencing API timeouts on production",
    sentiment: "concerned",
    urgency: "high",
    embedding: [0.1, 0.3, -0.2, ...] // Semantic vector
  },
  agent_response: {
    content: "I understand this is urgent for your production environment...",
    strategy: "empathetic_technical",
    confidence: 0.89,
    response_time_ms: 1200
  },
  resolution: {
    status: "resolved",
    steps_taken: ["checked_api_logs", "increased_timeout", "provided_monitoring"],
    satisfaction_score: 4.5
  }
}
```

## 🛠️ Implementation

### Customer Service Agent Class

```typescript
import { 
  MongoConnection, 
  HybridSearchEngine, 
  IDataStore,
  MongoDataStore 
} from '@mongodb-ai/core';

export class CustomerServiceAgent {
  private db: Db;
  private searchEngine: HybridSearchEngine;
  private customerStore: IDataStore<Customer>;
  private interactionStore: IDataStore<CustomerInteraction>;

  constructor(mongoUri: string, dbName: string) {
    const connection = MongoConnection.getInstance(mongoUri, dbName);
    this.db = connection.getDb();
    this.searchEngine = new HybridSearchEngine(this.db);
    this.customerStore = new MongoDataStore(this.db, 'customers');
    this.interactionStore = new MongoDataStore(this.db, 'customer_interactions');
  }

  async handleCustomerMessage(
    customerId: string, 
    message: string,
    channel: string = 'chat'
  ): Promise<CustomerServiceResponse> {
    const startTime = Date.now();

    try {
      // 1. Load customer profile and history
      const customer = await this.loadCustomerProfile(customerId);
      
      // 2. Analyze message sentiment and urgency
      const messageAnalysis = await this.analyzeMessage(message);
      
      // 3. Search for similar issues and solutions
      const similarIssues = await this.findSimilarIssues(message, customer);
      
      // 4. Generate personalized response
      const response = await this.generateResponse(
        customer, 
        message, 
        messageAnalysis, 
        similarIssues
      );
      
      // 5. Log interaction
      await this.logInteraction(customerId, message, response, channel);
      
      // 6. Update customer insights
      await this.updateCustomerInsights(customerId, messageAnalysis, response);
      
      // 7. Track performance metrics
      await this.trackPerformance(customerId, startTime, response);

      return response;

    } catch (error) {
      console.error('Customer service agent error:', error);
      return this.generateFallbackResponse(customerId, message);
    }
  }

  private async loadCustomerProfile(customerId: string): Promise<Customer> {
    const customer = await this.customerStore.findById(customerId);
    if (!customer) {
      throw new Error(`Customer ${customerId} not found`);
    }
    return customer;
  }

  private async analyzeMessage(message: string): Promise<MessageAnalysis> {
    // Use OpenAI or similar for sentiment analysis
    return {
      sentiment: this.detectSentiment(message),
      urgency: this.detectUrgency(message),
      intent: this.detectIntent(message),
      entities: this.extractEntities(message),
      embedding: await this.generateEmbedding(message)
    };
  }

  private async findSimilarIssues(
    message: string, 
    customer: Customer
  ): Promise<SimilarIssue[]> {
    const results = await this.searchEngine.search(message, {
      source_type: 'customer_interaction',
      metadata_filters: {
        customer_tier: customer.profile.tier,
        resolution_status: 'resolved'
      }
    }, {
      limit: 5,
      vector_weight: 0.7,
      text_weight: 0.3
    });

    return results.map(result => ({
      issue: result.content.text,
      resolution: result.metadata.resolution,
      satisfaction: result.metadata.satisfaction_score,
      similarity: result.scores.combined_score
    }));
  }

  private async generateResponse(
    customer: Customer,
    message: string,
    analysis: MessageAnalysis,
    similarIssues: SimilarIssue[]
  ): Promise<CustomerServiceResponse> {
    // Build context for AI response generation
    const context = {
      customer_profile: customer.profile,
      customer_history: customer.interaction_history.slice(-5), // Last 5 interactions
      current_message: message,
      message_analysis: analysis,
      similar_solutions: similarIssues,
      communication_preferences: customer.insights?.communication_style || 'professional'
    };

    // Generate response using OpenAI or similar
    const aiResponse = await this.callAIService(context);

    return {
      content: aiResponse.content,
      strategy: this.determineResponseStrategy(customer, analysis),
      confidence: aiResponse.confidence,
      suggested_actions: aiResponse.suggested_actions,
      escalation_needed: this.shouldEscalate(analysis, customer),
      response_time_ms: Date.now() - this.startTime
    };
  }

  private async logInteraction(
    customerId: string,
    message: string,
    response: CustomerServiceResponse,
    channel: string
  ): Promise<void> {
    const interaction: CustomerInteraction = {
      interaction_id: uuidv4(),
      customer_id: customerId,
      timestamp: new Date(),
      channel,
      message: {
        content: message,
        sentiment: response.message_analysis?.sentiment || 'neutral',
        urgency: response.message_analysis?.urgency || 'medium'
      },
      agent_response: {
        content: response.content,
        strategy: response.strategy,
        confidence: response.confidence,
        response_time_ms: response.response_time_ms
      },
      status: response.escalation_needed ? 'escalated' : 'handled'
    };

    await this.interactionStore.create(interaction);
  }

  private async updateCustomerInsights(
    customerId: string,
    analysis: MessageAnalysis,
    response: CustomerServiceResponse
  ): Promise<void> {
    // Update customer profile with new insights
    const insights = {
      last_interaction: new Date(),
      communication_patterns: {
        preferred_sentiment: analysis.sentiment,
        typical_urgency: analysis.urgency,
        response_satisfaction: response.confidence
      },
      agent_effectiveness: {
        strategy: response.strategy,
        success_rate: response.confidence
      }
    };

    await this.customerStore.update(customerId, {
      $set: { 'insights.latest': insights },
      $push: { 'interaction_history': { $slice: -10 } } // Keep last 10
    });
  }

  private async trackPerformance(
    customerId: string,
    startTime: number,
    response: CustomerServiceResponse
  ): Promise<void> {
    const metrics = {
      agent_id: 'customer_service_agent_v1',
      timestamp: new Date(),
      customer_id: customerId,
      metrics: {
        response_time_ms: Date.now() - startTime,
        confidence_score: response.confidence,
        escalation_needed: response.escalation_needed,
        strategy_used: response.strategy
      }
    };

    // Store in performance metrics collection
    await this.db.collection('agent_performance_metrics').insertOne(metrics);
  }
}
```

## 🧪 Usage Examples

### Basic Customer Interaction

```typescript
import { CustomerServiceAgent } from './CustomerServiceAgent';

const agent = new CustomerServiceAgent(
  process.env.MONGODB_URI!,
  'ai_agents'
);

// Handle customer message
const response = await agent.handleCustomerMessage(
  'cust_12345',
  'I\'m having trouble with API rate limits on my production environment',
  'chat'
);

console.log('Agent Response:', response.content);
console.log('Confidence:', response.confidence);
console.log('Strategy:', response.strategy);
```

### Batch Processing

```typescript
// Process multiple customer messages
const messages = [
  { customerId: 'cust_001', message: 'Billing question about overages' },
  { customerId: 'cust_002', message: 'API returning 500 errors' },
  { customerId: 'cust_003', message: 'Need help with integration' }
];

const responses = await Promise.all(
  messages.map(({ customerId, message }) => 
    agent.handleCustomerMessage(customerId, message)
  )
);

console.log(`Processed ${responses.length} customer messages`);
```

### Performance Analytics

```typescript
// Get agent performance metrics
const performance = await agent.getPerformanceMetrics('last_24_hours');

console.log('Performance Summary:');
console.log(`- Average Response Time: ${performance.avg_response_time}ms`);
console.log(`- Customer Satisfaction: ${performance.avg_satisfaction}/5`);
console.log(`- Escalation Rate: ${performance.escalation_rate}%`);
console.log(`- Resolution Rate: ${performance.resolution_rate}%`);
```

## 📊 Performance Metrics

The agent automatically tracks:

- **Response Time**: Time to generate response
- **Confidence Score**: AI confidence in response quality
- **Customer Satisfaction**: Predicted satisfaction based on interaction
- **Escalation Rate**: Percentage of issues requiring human intervention
- **Resolution Rate**: Percentage of issues resolved in first interaction

## 🔧 Configuration

### Environment Variables

```bash
# MongoDB Configuration
MONGODB_URI=mongodb+srv://user:<EMAIL>/
DATABASE_NAME=ai_agents

# AI Service Configuration
OPENAI_API_KEY=your_openai_key
OPENAI_MODEL=gpt-4

# Agent Configuration
AGENT_CONFIDENCE_THRESHOLD=0.7
ESCALATION_THRESHOLD=0.3
MAX_RESPONSE_TIME_MS=5000
```

### Agent Configuration

```typescript
const agentConfig = {
  name: 'Customer Service Agent',
  version: '1.0',
  capabilities: [
    'customer_support',
    'issue_resolution',
    'escalation_management'
  ],
  performance_targets: {
    max_response_time_seconds: 5,
    min_confidence_score: 0.7,
    max_escalation_rate: 0.15
  },
  communication_styles: {
    professional: 'Clear, concise, solution-focused',
    empathetic: 'Understanding, supportive, reassuring',
    technical: 'Detailed, precise, technical depth'
  }
};
```

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run performance tests
npm run test:performance

# Test with sample data
npm run test:sample-data
```

## 📈 Monitoring & Analytics

### Real-time Dashboard

The example includes a real-time dashboard showing:

- Active conversations
- Response time trends
- Customer satisfaction scores
- Escalation patterns
- Agent performance metrics

### Alerts & Notifications

Automatic alerts for:

- Response time > 5 seconds
- Confidence score < 0.7
- Escalation rate > 15%
- Customer satisfaction < 3.0

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-service-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: customer-service-agent
  template:
    metadata:
      labels:
        app: customer-service-agent
    spec:
      containers:
      - name: agent
        image: customer-service-agent:latest
        ports:
        - containerPort: 3000
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: uri
```

## 🎯 Next Steps

1. **Customize the agent** for your specific use case
2. **Add more tools** (CRM integration, knowledge base, etc.)
3. **Implement human handoff** workflows
4. **Set up monitoring** and alerting
5. **Scale to production** with load balancing

## 📚 Learn More

- [MongoDB AI Agent Boilerplate Documentation](../../docs/)
- [Vector Search Best Practices](../../docs/vector-search.md)
- [Production Deployment Guide](../../docs/deployment/production.md)
- [Performance Optimization](../../docs/performance.md)