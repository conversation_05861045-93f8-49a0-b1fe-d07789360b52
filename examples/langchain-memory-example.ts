import { BufferMemory } from 'langchain/memory';
import { MongoDBChatMessageHistory } from '@langchain/mongodb';
import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';

dotenv.config();

async function run() {
  if (!process.env.MONGODB_URI) {
    throw new Error('Missing MONGODB_URI environment variable');
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();

  const collection = client.db('ai_agents').collection('agent_working_memory');

  const sessionId = 'test-session';
  const memory = new BufferMemory({
    chatHistory: new MongoDBChatMessageHistory({
      collection,
      sessionId,
    }),
  });

  await memory.saveContext(
    { input: 'who is the best dog?' },
    { output: 'the best dog is the one that is happy and loved' }
  );

  const history = await memory.loadMemoryVariables({});
  console.log(history);

  await client.close();
}

run().catch(console.error);