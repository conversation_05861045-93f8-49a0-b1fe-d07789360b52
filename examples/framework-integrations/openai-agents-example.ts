/**
 * @file OpenAI Agents JS Integration Example
 * 
 * This example demonstrates how to integrate the Universal AI Brain with OpenAI Agents JS,
 * providing MongoDB-powered intelligence to OpenAI Agents applications.
 */

import { UniversalAIBrain, OpenAIAgentsAdapter } from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * Example: Basic OpenAI Agents Integration with MongoDB Superpowers
 */
async function basicOpenAIAgentsExample() {
  console.log('🚀 Starting OpenAI Agents JS + MongoDB AI Brain Integration Example\n');

  // 1. Initialize the Universal AI Brain
  console.log('1️⃣ Initializing Universal AI Brain...');
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'openai_agents_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'openai_vector_index',
      collectionName: 'openai_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized!\n');

  // 2. Create OpenAI Agents adapter
  console.log('2️⃣ Creating OpenAI Agents adapter...');
  const openaiAdapter = new OpenAIAgentsAdapter({
    enableAgentEnhancement: true,
    enableToolIntegration: true,
    enableMemoryPersistence: true
  });

  // 3. Integrate with OpenAI Agents
  console.log('3️⃣ Integrating with OpenAI Agents JS...');
  const enhancedOpenAI = await openaiAdapter.integrate(brain);
  console.log('✅ OpenAI Agents integration complete!\n');

  // 4. Store some knowledge in MongoDB for context
  console.log('4️⃣ Storing knowledge in MongoDB...');
  await brain.storeInteraction({
    conversationId: 'openai_knowledge',
    userMessage: 'What is OpenAI Agents JS?',
    assistantResponse: 'OpenAI Agents JS is a lightweight TypeScript SDK for building agentic AI applications. It provides primitives like Agents, Handoffs, and Guardrails to express complex relationships between tools and agents. The SDK includes built-in tracing for visualization, debugging, and evaluation.',
    context: [],
    framework: 'openai-agents',
    metadata: {
      type: 'knowledge_base',
      topic: 'openai_agents_sdk'
    }
  });

  await brain.storeInteraction({
    conversationId: 'openai_knowledge',
    userMessage: 'What are handoffs in OpenAI Agents?',
    assistantResponse: 'Handoffs in OpenAI Agents enable task delegation from one agent to another. They are represented as tools to the LLM and can be customized using the handoff() helper function. This is useful for applications where different agents specialize in specific areas, such as customer support scenarios.',
    context: [],
    framework: 'openai-agents',
    metadata: {
      type: 'knowledge_base',
      topic: 'agent_handoffs'
    }
  });

  console.log('✅ Knowledge stored in MongoDB!\n');

  // 5. Create enhanced OpenAI agent
  console.log('5️⃣ Creating enhanced OpenAI agent...');
  const researchAgent = enhancedOpenAI.createEnhancedAgent({
    name: 'Research Assistant',
    instructions: 'You are a research assistant specialized in AI and technology topics. Use your MongoDB knowledge base to provide accurate, contextual responses.',
    model: 'gpt-4',
    conversationId: 'research_session'
  });

  console.log('✅ Enhanced OpenAI agent created!\n');

  // 6. Test the enhanced agent with context injection
  console.log('6️⃣ Testing enhanced agent with MongoDB context...');
  
  const result1 = await researchAgent.run('Tell me about OpenAI Agents and how handoffs work', {
    conversationId: 'openai_demo_session'
  });

  console.log('🤖 Agent Response (with MongoDB context):');
  console.log(result1.finalOutput);
  console.log('\n📊 Enhanced Context:', result1.enhancedContext?.length, 'items injected');
  console.log('\n');

  // 7. Test memory persistence
  console.log('7️⃣ Testing memory persistence...');
  
  const result2 = await researchAgent.run('What did we just discuss about OpenAI Agents?', {
    conversationId: 'openai_demo_session'
  });

  console.log('🧠 Agent Response (with memory):');
  console.log(result2.finalOutput);
  console.log('\n');

  // 8. Test streaming with context
  console.log('8️⃣ Testing streaming with MongoDB context...');
  
  const stream = await researchAgent.stream('Explain the benefits of using MongoDB with AI agents', {
    conversationId: 'openai_demo_session'
  });

  console.log('🌊 Streaming Response:');
  for await (const chunk of stream) {
    process.stdout.write(chunk.content || '');
  }
  console.log('\n\n');

  console.log('🎉 OpenAI Agents + MongoDB AI Brain integration example completed successfully!');
}

/**
 * Example: Multi-Agent System with MongoDB Coordination
 */
async function multiAgentExample() {
  console.log('👥 Multi-Agent System with MongoDB Coordination Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'openai_multiagent_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'multiagent_vector_index',
      collectionName: 'multiagent_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const openaiAdapter = new OpenAIAgentsAdapter();
  const enhancedOpenAI = await openaiAdapter.integrate(brain);

  // Create specialized agents
  console.log('1️⃣ Creating specialized agents...');
  
  const researchAgent = enhancedOpenAI.createEnhancedAgent({
    name: 'Research Specialist',
    instructions: 'You are a research specialist focused on gathering comprehensive information about companies and technologies.',
    model: 'gpt-4',
    conversationId: 'research_agent'
  });

  const analysisAgent = enhancedOpenAI.createEnhancedAgent({
    name: 'Analysis Expert',
    instructions: 'You are an analysis expert who processes research data and extracts key insights and recommendations.',
    model: 'gpt-4',
    conversationId: 'analysis_agent'
  });

  const reportAgent = enhancedOpenAI.createEnhancedAgent({
    name: 'Report Writer',
    instructions: 'You are a professional report writer who creates well-structured, comprehensive reports based on research and analysis.',
    model: 'gpt-4',
    conversationId: 'report_agent'
  });

  console.log('✅ Specialized agents created');

  // Simulate multi-agent workflow
  const workflowId = 'multiagent_workflow_001';
  
  console.log('\n2️⃣ Research Phase...');
  const researchResult = await researchAgent.run(
    'Research MongoDB Atlas and its vector search capabilities for AI applications',
    { conversationId: workflowId }
  );
  console.log('📊 Research completed');

  console.log('\n3️⃣ Analysis Phase...');
  const analysisResult = await analysisAgent.run(
    `Analyze this research data and identify key benefits and use cases: ${researchResult.finalOutput}`,
    { conversationId: workflowId }
  );
  console.log('🔍 Analysis completed');

  console.log('\n4️⃣ Report Generation Phase...');
  const reportResult = await reportAgent.run(
    `Create a professional executive summary based on this analysis: ${analysisResult.finalOutput}`,
    { conversationId: workflowId }
  );
  console.log('📝 Report completed');

  console.log('\n📋 Final Executive Summary:');
  console.log(reportResult.finalOutput);

  console.log('\n🎉 Multi-agent workflow completed with MongoDB coordination!');
}

/**
 * Example: OpenAI Agents with MongoDB Tools
 */
async function agentToolsExample() {
  console.log('🛠️ OpenAI Agents with MongoDB Tools Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'openai_tools_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'tools_vector_index',
      collectionName: 'tools_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const openaiAdapter = new OpenAIAgentsAdapter();
  const enhancedOpenAI = await openaiAdapter.integrate(brain);

  // Get MongoDB tools
  const mongoDBTools = enhancedOpenAI.createMongoDBTools();

  console.log('1️⃣ Available MongoDB Tools:');
  mongoDBTools.forEach((tool, index) => {
    console.log(`${index + 1}. ${tool.name}: ${tool.description}`);
  });

  // Create agent with MongoDB tools
  console.log('\n2️⃣ Creating agent with MongoDB tools...');
  const toolAgent = enhancedOpenAI.createEnhancedAgent({
    name: 'Tool Agent',
    instructions: 'You are an AI assistant with access to MongoDB tools. Use these tools to search for information and store new knowledge.',
    model: 'gpt-4',
    tools: mongoDBTools,
    conversationId: 'tool_agent'
  });

  // Test tool usage
  console.log('\n3️⃣ Testing MongoDB tools...');
  
  // First, store some information
  console.log('Storing information in MongoDB...');
  const storeResult = await mongoDBTools[1].func({
    content: 'OpenAI Agents JS provides a simple and customizable design for building agentic AI applications with TypeScript.',
    metadata: { topic: 'openai_agents', category: 'framework' }
  });
  console.log('✅', storeResult);

  // Then search for it
  console.log('\nSearching MongoDB knowledge base...');
  const searchResult = await mongoDBTools[0].func({
    query: 'OpenAI Agents TypeScript framework',
    limit: 3
  });
  console.log('🔍 Search Results:');
  searchResult.forEach((result, index) => {
    console.log(`${index + 1}. ${result.content} (Score: ${result.relevanceScore})`);
  });

  // Test memory tool
  console.log('\n4️⃣ Testing memory tool...');
  const memoryTool = enhancedOpenAI.mongoDBMemoryTool;
  
  // Store memory
  const memoryStoreResult = await memoryTool.func({
    action: 'store',
    query: 'The user is interested in building AI agents with TypeScript and MongoDB.',
    conversationId: 'tool_demo'
  });
  console.log('💾 Memory Store:', memoryStoreResult);

  // Retrieve memory
  const memoryRetrieveResult = await memoryTool.func({
    action: 'retrieve',
    query: 'user preferences',
    conversationId: 'tool_demo'
  });
  console.log('🧠 Memory Retrieve:', memoryRetrieveResult);

  console.log('\n🎉 MongoDB tools example completed!');
}

/**
 * Example: Agent Enhancement of Existing OpenAI Agent
 */
async function agentEnhancementExample() {
  console.log('⚡ Agent Enhancement Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'openai_enhancement_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'enhancement_vector_index',
      collectionName: 'enhancement_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const openaiAdapter = new OpenAIAgentsAdapter();
  const enhancedOpenAI = await openaiAdapter.integrate(brain);

  // Simulate an existing OpenAI agent
  console.log('1️⃣ Creating existing OpenAI agent...');
  const existingAgent = {
    name: 'Basic Agent',
    instructions: 'You are a helpful assistant.',
    model: 'gpt-4',
    tools: [],
    
    async run(input: string, options?: any) {
      return {
        finalOutput: `Basic response to: ${input}`,
        history: [{ role: 'user', content: input }],
        state: { completed: true }
      };
    },
    
    async stream(input: string, options?: any) {
      return {
        async *[Symbol.asyncIterator]() {
          yield { content: 'Basic ' };
          yield { content: 'streaming ' };
          yield { content: 'response' };
        }
      };
    }
  };

  console.log('✅ Basic agent created');

  // Enhance the existing agent
  console.log('\n2️⃣ Enhancing existing agent with MongoDB superpowers...');
  const enhancedAgent = enhancedOpenAI.enhanceExistingAgent(existingAgent, 'enhanced_session');
  console.log('✅ Agent enhanced with MongoDB capabilities');

  // Store some context for the enhanced agent
  console.log('\n3️⃣ Storing context for enhanced agent...');
  await brain.storeInteraction({
    conversationId: 'enhanced_session',
    userMessage: 'What are the benefits of agent enhancement?',
    assistantResponse: 'Agent enhancement with MongoDB provides: 1) Intelligent context injection from vector search, 2) Persistent memory across conversations, 3) Access to MongoDB tools for knowledge management, 4) Improved response quality through relevant context.',
    context: [],
    framework: 'openai-agents',
    metadata: { type: 'enhancement_knowledge' }
  });

  // Test the enhanced agent
  console.log('\n4️⃣ Testing enhanced agent...');
  const enhancedResult = await enhancedAgent.run('Tell me about agent enhancement benefits');
  
  console.log('🤖 Enhanced Agent Response:');
  console.log(enhancedResult.finalOutput);

  // Compare with original agent
  console.log('\n5️⃣ Comparing with original agent...');
  const originalResult = await existingAgent.run('Tell me about agent enhancement benefits');
  
  console.log('🤖 Original Agent Response:');
  console.log(originalResult.finalOutput);

  console.log('\n📊 Enhancement Benefits:');
  console.log('- Enhanced agent has access to MongoDB context');
  console.log('- Enhanced agent has persistent memory');
  console.log('- Enhanced agent has MongoDB tools');
  console.log('- Enhanced agent provides more relevant responses');

  console.log('\n🎉 Agent enhancement example completed!');
}

// Run examples
async function runExamples() {
  try {
    await basicOpenAIAgentsExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await multiAgentExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await agentToolsExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await agentEnhancementExample();
  } catch (error) {
    console.error('❌ Error running examples:', error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runExamples();
}

export {
  basicOpenAIAgentsExample,
  multiAgentExample,
  agentToolsExample,
  agentEnhancementExample
};
