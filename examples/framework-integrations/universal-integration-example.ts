/**
 * @file Universal Integration Example
 * 
 * This example demonstrates the Universal AI Brain working with ALL frameworks
 * simultaneously, showcasing the true power of framework-agnostic AI intelligence.
 */

import { 
  UniversalAIBrain, 
  MastraAdapter, 
  VercelAIAdapter, 
  LangChainJ<PERSON>dapter, 
  OpenAIAgentsAdapter 
} from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * Example: Universal AI Brain with All Frameworks
 */
async function universalIntegrationExample() {
  console.log('🌟 UNIVERSAL AI BRAIN - ALL FRAMEWORKS INTEGRATION EXAMPLE 🌟\n');
  console.log('Demonstrating MongoDB-powered intelligence across ALL TypeScript AI frameworks!\n');

  // 1. Initialize the Universal AI Brain
  console.log('1️⃣ Initializing Universal AI Brain...');
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'universal_ai_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'universal_vector_index',
      collectionName: 'universal_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized - Ready to power ANY framework!\n');

  // 2. Create all framework adapters
  console.log('2️⃣ Creating framework adapters...');
  
  const mastraAdapter = new MastraAdapter();
  const vercelAdapter = new VercelAIAdapter();
  const langchainAdapter = new LangChainJSAdapter();
  const openaiAdapter = new OpenAIAgentsAdapter();

  console.log('✅ All framework adapters created\n');

  // 3. Integrate with all frameworks
  console.log('3️⃣ Integrating with all frameworks...');
  
  const [enhancedMastra, enhancedVercel, enhancedLangChain, enhancedOpenAI] = await Promise.all([
    mastraAdapter.integrate(brain),
    vercelAdapter.integrate(brain),
    langchainAdapter.integrate(brain),
    openaiAdapter.integrate(brain)
  ]);

  console.log('✅ All frameworks integrated with MongoDB superpowers!\n');

  // 4. Store shared knowledge that all frameworks can access
  console.log('4️⃣ Storing shared knowledge in MongoDB...');
  
  const sharedKnowledge = [
    {
      question: 'What is the Universal AI Brain?',
      answer: 'The Universal AI Brain is a MongoDB-powered intelligence layer that ANY TypeScript framework can integrate with to get superpowers. It provides semantic memory, context injection, and intelligent responses using MongoDB Atlas Vector Search.',
      topic: 'universal_ai_brain'
    },
    {
      question: 'Why use MongoDB for AI applications?',
      answer: 'MongoDB offers unified data storage for operational data and vectors, native vector search, flexible schemas, real-time change streams, and global distribution - making it perfect for AI applications.',
      topic: 'mongodb_ai_benefits'
    },
    {
      question: 'What frameworks does the Universal AI Brain support?',
      answer: 'The Universal AI Brain supports Mastra, Vercel AI SDK, LangChain.js, OpenAI Agents JS, and can be extended to support any TypeScript AI framework through custom adapters.',
      topic: 'framework_support'
    }
  ];

  for (const knowledge of sharedKnowledge) {
    await brain.storeInteraction({
      conversationId: 'shared_knowledge',
      userMessage: knowledge.question,
      assistantResponse: knowledge.answer,
      context: [],
      framework: 'universal',
      metadata: {
        type: 'shared_knowledge',
        topic: knowledge.topic
      }
    });
  }

  console.log('✅ Shared knowledge stored - accessible by all frameworks!\n');

  // 5. Test each framework with the same query
  console.log('5️⃣ Testing all frameworks with the same query...\n');
  
  const testQuery = 'Explain the benefits of using MongoDB for AI applications';
  const conversationId = 'universal_demo';

  // Test Mastra
  console.log('🔸 Testing Mastra Framework:');
  const mastraAgent = enhancedMastra.createAgent({
    name: 'Mastra Agent',
    instructions: 'You are a Mastra-powered AI agent with MongoDB intelligence.',
    model: { name: 'gpt-4', provider: 'openai' }
  });
  
  const mastraResult = await mastraAgent.generate([
    { role: 'user', content: testQuery }
  ], { conversationId });
  
  console.log('🤖 Mastra Response:', mastraResult.text.substring(0, 200) + '...');
  console.log('📊 Context Sources:', mastraResult.enhancedContext?.length || 0, 'items\n');

  // Test Vercel AI SDK
  console.log('🔸 Testing Vercel AI SDK:');
  const vercelResult = await enhancedVercel.generateText({
    model: { modelId: 'gpt-4' },
    messages: [{ role: 'user', content: testQuery }],
    conversationId
  });
  
  console.log('🤖 Vercel AI Response:', vercelResult.text.substring(0, 200) + '...');
  console.log('📊 Context Sources:', vercelResult.enhancedContext?.length || 0, 'items\n');

  // Test LangChain.js
  console.log('🔸 Testing LangChain.js:');
  const mockLangChainModel = {
    async invoke(messages: any[]) {
      return { content: 'Enhanced LangChain response with MongoDB context' };
    }
  };
  
  const enhancedLangChainModel = enhancedLangChain.enhancedChatModel(mockLangChainModel, conversationId);
  const langchainResult = await enhancedLangChainModel.invoke([
    { role: 'user', content: testQuery }
  ]);
  
  console.log('🤖 LangChain Response:', langchainResult.content);
  console.log('📊 Enhanced with MongoDB context\n');

  // Test OpenAI Agents
  console.log('🔸 Testing OpenAI Agents JS:');
  const openaiAgent = enhancedOpenAI.createEnhancedAgent({
    name: 'OpenAI Agent',
    instructions: 'You are an OpenAI agent enhanced with MongoDB intelligence.',
    model: 'gpt-4',
    conversationId
  });
  
  const openaiResult = await openaiAgent.run(testQuery, { conversationId });
  console.log('🤖 OpenAI Agents Response:', openaiResult.finalOutput.substring(0, 200) + '...');
  console.log('📊 Context Sources:', openaiResult.enhancedContext?.length || 0, 'items\n');

  console.log('🎉 ALL FRAMEWORKS SUCCESSFULLY ENHANCED WITH MONGODB INTELLIGENCE!\n');

  // 6. Demonstrate cross-framework knowledge sharing
  console.log('6️⃣ Demonstrating cross-framework knowledge sharing...\n');
  
  // Store knowledge from Mastra
  await brain.storeInteraction({
    conversationId: 'cross_framework_demo',
    userMessage: 'What is Mastra?',
    assistantResponse: 'Mastra is an open-source TypeScript agent framework that enables building AI applications quickly with workflows, agents, RAG, and evaluations.',
    context: [],
    framework: 'mastra',
    metadata: { source: 'mastra_agent', type: 'framework_info' }
  });

  // Access that knowledge from Vercel AI
  const crossFrameworkResult = await enhancedVercel.generateText({
    model: { modelId: 'gpt-4' },
    messages: [{ role: 'user', content: 'Tell me about Mastra framework' }],
    conversationId: 'cross_framework_demo'
  });
  
  console.log('🔄 Cross-Framework Knowledge Sharing:');
  console.log('📝 Knowledge stored by: Mastra');
  console.log('🔍 Knowledge accessed by: Vercel AI SDK');
  console.log('🤖 Response:', crossFrameworkResult.text.substring(0, 200) + '...\n');

  // 7. Show framework statistics
  console.log('7️⃣ Universal AI Brain Statistics...\n');
  const stats = await brain.getStats();
  
  console.log('📊 MongoDB Collections:');
  console.log(`   • Vector Store: ${stats.collections?.vectorStore?.documentCount || 0} documents`);
  console.log(`   • Interactions: ${stats.collections?.interactions || 0} stored`);
  console.log(`   • Conversations: ${stats.collections?.conversations || 0} active`);
  
  console.log('\n🧠 Embedding Provider:');
  console.log(`   • Model: ${stats.embeddingProvider?.model || 'OpenAI'}`);
  console.log(`   • Dimensions: ${stats.embeddingProvider?.dimensions || 1536}`);
  
  console.log(`\n💚 System Health: ${stats.isHealthy ? 'Healthy' : 'Issues Detected'}`);
  console.log(`📅 Last Updated: ${stats.lastUpdated}\n`);

  console.log('🌟 UNIVERSAL AI BRAIN DEMONSTRATION COMPLETE! 🌟');
  console.log('\n🎯 Key Achievements:');
  console.log('   ✅ Single MongoDB brain powering 4 different frameworks');
  console.log('   ✅ Shared knowledge accessible across all frameworks');
  console.log('   ✅ Consistent intelligent context injection');
  console.log('   ✅ Cross-framework memory and learning');
  console.log('   ✅ Framework-agnostic MongoDB superpowers');
  
  console.log('\n🚀 This is the future of AI development:');
  console.log('   • Choose ANY framework you love');
  console.log('   • Get MongoDB intelligence automatically');
  console.log('   • Share knowledge across your entire AI ecosystem');
  console.log('   • Scale with MongoDB Atlas globally');
  
  console.log('\n💡 The Universal AI Brain makes every framework smarter! 🧠⚡');
}

/**
 * Example: Performance Comparison Across Frameworks
 */
async function performanceComparisonExample() {
  console.log('⚡ Performance Comparison Across Frameworks\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'performance_test_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'performance_vector_index',
      collectionName: 'performance_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  // Create adapters
  const adapters = {
    mastra: new MastraAdapter(),
    vercel: new VercelAIAdapter(),
    langchain: new LangChainJSAdapter(),
    openai: new OpenAIAgentsAdapter()
  };

  // Integrate all frameworks
  const frameworks = {};
  for (const [name, adapter] of Object.entries(adapters)) {
    frameworks[name] = await adapter.integrate(brain);
  }

  console.log('🏁 Running performance tests...\n');

  const testQuery = 'What are the best practices for AI development?';
  const results = {};

  // Test each framework
  for (const [name, framework] of Object.entries(frameworks)) {
    console.log(`Testing ${name}...`);
    const startTime = Date.now();
    
    try {
      // Simulate framework-specific calls
      let result;
      switch (name) {
        case 'mastra':
          const agent = framework.createAgent({
            name: 'Test Agent',
            instructions: 'You are a test agent.',
            model: { name: 'gpt-4' }
          });
          result = await agent.generate([{ role: 'user', content: testQuery }]);
          break;
        case 'vercel':
          result = await framework.generateText({
            model: { modelId: 'gpt-4' },
            messages: [{ role: 'user', content: testQuery }]
          });
          break;
        case 'langchain':
          // Simulate LangChain call
          result = { text: 'LangChain enhanced response' };
          break;
        case 'openai':
          const openaiAgent = framework.createEnhancedAgent({
            name: 'Test Agent',
            instructions: 'You are a test agent.',
            model: 'gpt-4'
          });
          result = await openaiAgent.run(testQuery);
          break;
      }
      
      const endTime = Date.now();
      results[name] = {
        success: true,
        responseTime: endTime - startTime,
        responseLength: (result.text || result.finalOutput || result.content || '').length
      };
    } catch (error) {
      results[name] = {
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }

  // Display results
  console.log('\n📊 Performance Results:\n');
  console.log('Framework'.padEnd(15) + 'Status'.padEnd(10) + 'Time (ms)'.padEnd(12) + 'Response Length');
  console.log('-'.repeat(60));

  for (const [name, result] of Object.entries(results)) {
    const status = result.success ? '✅ Success' : '❌ Failed';
    const time = result.responseTime.toString();
    const length = result.responseLength?.toString() || 'N/A';
    
    console.log(
      name.padEnd(15) + 
      status.padEnd(10) + 
      time.padEnd(12) + 
      length
    );
  }

  console.log('\n🎉 Performance comparison completed!');
}

// Run examples
async function runExamples() {
  try {
    await universalIntegrationExample();
    console.log('\n' + '='.repeat(100) + '\n');
    
    await performanceComparisonExample();
  } catch (error) {
    console.error('❌ Error running universal integration examples:', error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runExamples();
}

export {
  universalIntegrationExample,
  performanceComparisonExample
};
