/**
 * @file Vercel AI SDK Integration Example
 * 
 * This example demonstrates how to integrate the Universal AI Brain with Vercel AI SDK,
 * providing MongoDB-powered intelligence to AI SDK applications.
 */

import { UniversalAIBrain, VercelAIAdapter } from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * Example: Basic Vercel AI SDK Integration with MongoDB Superpowers
 */
async function basicVercelAIExample() {
  console.log('🚀 Starting Vercel AI SDK + MongoDB AI Brain Integration Example\n');

  // 1. Initialize the Universal AI Brain
  console.log('1️⃣ Initializing Universal AI Brain...');
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'vercel_ai_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'vercel_vector_index',
      collectionName: 'vercel_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized!\n');

  // 2. Create Vercel AI adapter
  console.log('2️⃣ Creating Vercel AI SDK adapter...');
  const vercelAdapter = new VercelAIAdapter({
    enableStreamEnhancement: true,
    enableToolIntegration: true,
    enableChatMemory: true
  });

  // 3. Integrate with Vercel AI SDK
  console.log('3️⃣ Integrating with Vercel AI SDK...');
  const enhancedAI = await vercelAdapter.integrate(brain);
  console.log('✅ Vercel AI SDK integration complete!\n');

  // 4. Store some knowledge in MongoDB for context
  console.log('4️⃣ Storing knowledge in MongoDB...');
  await brain.storeInteraction({
    conversationId: 'ai_knowledge',
    userMessage: 'What is the Vercel AI SDK?',
    assistantResponse: 'The Vercel AI SDK is a library for building AI-powered streaming text and chat UIs. It provides React hooks, utilities, and adapters for popular AI providers like OpenAI, Anthropic, and more. The SDK makes it easy to build conversational AI applications with streaming responses.',
    context: [],
    framework: 'vercel-ai',
    metadata: {
      type: 'knowledge_base',
      topic: 'vercel_ai_sdk'
    }
  });

  await brain.storeInteraction({
    conversationId: 'ai_knowledge',
    userMessage: 'How does streaming work in AI applications?',
    assistantResponse: 'Streaming in AI applications allows responses to be delivered incrementally as they are generated, rather than waiting for the complete response. This provides better user experience with faster perceived response times and real-time feedback. The Vercel AI SDK handles streaming automatically with hooks like useChat and useCompletion.',
    context: [],
    framework: 'vercel-ai',
    metadata: {
      type: 'knowledge_base',
      topic: 'ai_streaming'
    }
  });

  console.log('✅ Knowledge stored in MongoDB!\n');

  // 5. Test enhanced generateText with context injection
  console.log('5️⃣ Testing enhanced generateText with MongoDB context...');
  
  const result1 = await enhancedAI.generateText({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'Explain how to use the Vercel AI SDK for streaming' }
    ],
    conversationId: 'vercel_demo_session'
  });

  console.log('🤖 Enhanced generateText Response:');
  console.log(result1.text);
  console.log('\n📊 Injected Context Sources:', result1.enhancedContext?.map(c => c.source));
  console.log('\n');

  // 6. Test enhanced streamText with context injection
  console.log('6️⃣ Testing enhanced streamText with MongoDB context...');
  
  const stream = await enhancedAI.streamText({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'What are the benefits of using AI streaming?' }
    ],
    conversationId: 'vercel_demo_session',
    onFinish: (result) => {
      console.log('\n✅ Stream finished. Total tokens:', result.usage?.totalTokens);
    }
  });

  console.log('🌊 Streaming Response:');
  for await (const chunk of stream.textStream) {
    process.stdout.write(chunk);
  }
  console.log('\n\n');

  // 7. Test enhanced generateObject with structured output
  console.log('7️⃣ Testing enhanced generateObject with structured output...');
  
  const objectResult = await enhancedAI.generateObject({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'Create a summary of AI SDK features' }
    ],
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        features: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' }
            }
          }
        },
        benefits: { type: 'array', items: { type: 'string' } }
      }
    },
    conversationId: 'vercel_demo_session'
  });

  console.log('📋 Structured Object Response:');
  console.log(JSON.stringify(objectResult.object, null, 2));
  console.log('\n');

  console.log('🎉 Vercel AI SDK + MongoDB AI Brain integration example completed successfully!');
}

/**
 * Example: Chat Application with MongoDB Memory
 */
async function chatApplicationExample() {
  console.log('💬 Chat Application with MongoDB Memory Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'vercel_chat_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'chat_vector_index',
      collectionName: 'chat_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const vercelAdapter = new VercelAIAdapter();
  const enhancedAI = await vercelAdapter.integrate(brain);

  // Simulate a chat conversation with memory
  const conversationId = 'user_alice_chat';
  
  console.log('1️⃣ First message...');
  const msg1 = await enhancedAI.generateText({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'Hi! My name is Alice and I love building AI applications.' }
    ],
    conversationId
  });
  console.log('🤖 Assistant:', msg1.text);

  console.log('\n2️⃣ Second message...');
  const msg2 = await enhancedAI.generateText({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'What did I tell you about myself?' }
    ],
    conversationId
  });
  console.log('🤖 Assistant:', msg2.text);

  console.log('\n3️⃣ Third message with context...');
  const msg3 = await enhancedAI.generateText({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'Can you recommend some AI tools for someone like me?' }
    ],
    conversationId
  });
  console.log('🤖 Assistant:', msg3.text);

  console.log('\n🎉 Chat with memory example completed!');
}

/**
 * Example: MongoDB Tools Integration
 */
async function mongoDBToolsExample() {
  console.log('🛠️ MongoDB Tools Integration Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'vercel_tools_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'tools_vector_index',
      collectionName: 'tools_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const vercelAdapter = new VercelAIAdapter();
  const enhancedAI = await vercelAdapter.integrate(brain);

  // Get MongoDB tools
  const mongoDBTools = enhancedAI.createMongoDBTools();

  // Store some knowledge first
  await mongoDBTools.storeMemory.execute({
    content: 'MongoDB Atlas is a fully managed cloud database service that provides vector search capabilities for AI applications.',
    metadata: { topic: 'mongodb_atlas', category: 'database' }
  });

  await mongoDBTools.storeMemory.execute({
    content: 'Vector search allows you to find semantically similar content by comparing high-dimensional embeddings.',
    metadata: { topic: 'vector_search', category: 'ai_technology' }
  });

  console.log('1️⃣ Testing knowledge base search...');
  const searchResult = await mongoDBTools.searchKnowledgeBase.execute({
    query: 'What is MongoDB Atlas?',
    limit: 3
  });

  console.log('🔍 Search Results:');
  searchResult.results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.content} (Score: ${result.relevanceScore})`);
  });

  console.log('\n2️⃣ Testing with AI SDK and tools...');
  const toolResult = await enhancedAI.generateText({
    model: { modelId: 'gpt-4' },
    messages: [
      { role: 'user', content: 'Search for information about vector databases and explain what you find' }
    ],
    tools: [mongoDBTools.searchKnowledgeBase],
    conversationId: 'tools_demo'
  });

  console.log('🤖 AI Response with Tools:');
  console.log(toolResult.text);

  console.log('\n🎉 MongoDB tools integration example completed!');
}

/**
 * Example: Real-time Chat with Streaming and Memory
 */
async function realtimeChatExample() {
  console.log('⚡ Real-time Chat with Streaming and Memory Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'vercel_realtime_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'realtime_vector_index',
      collectionName: 'realtime_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const vercelAdapter = new VercelAIAdapter();
  const enhancedAI = await vercelAdapter.integrate(brain);

  // Simulate real-time chat messages
  const messages = [
    'Hello! I\'m working on a new AI project.',
    'I need to implement vector search for my application.',
    'Can you help me understand how MongoDB Atlas Vector Search works?',
    'What are the key benefits compared to other vector databases?'
  ];

  const conversationId = 'realtime_chat_session';

  for (let i = 0; i < messages.length; i++) {
    console.log(`\n💬 User Message ${i + 1}: ${messages[i]}`);
    console.log('🤖 Assistant Response:');

    const stream = await enhancedAI.streamText({
      model: { modelId: 'gpt-4' },
      messages: [
        { role: 'user', content: messages[i] }
      ],
      conversationId,
      onChunk: (chunk) => {
        // Real-time processing of chunks
      },
      onFinish: (result) => {
        console.log(`\n✅ Message ${i + 1} completed (${result.usage?.totalTokens} tokens)`);
      }
    });

    // Stream the response
    for await (const chunk of stream.textStream) {
      process.stdout.write(chunk);
    }

    // Add a small delay to simulate real conversation
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n\n🎉 Real-time chat example completed!');
}

// Run examples
async function runExamples() {
  try {
    await basicVercelAIExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await chatApplicationExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await mongoDBToolsExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await realtimeChatExample();
  } catch (error) {
    console.error('❌ Error running examples:', error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runExamples();
}

export {
  basicVercelAIExample,
  chatApplicationExample,
  mongoDBToolsExample,
  realtimeChatExample
};
