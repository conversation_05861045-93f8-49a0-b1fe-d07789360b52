/**
 * @file Mastra Framework Integration Example
 * 
 * This example demonstrates how to integrate the Universal AI Brain with Mastra framework,
 * providing MongoDB-powered intelligence to Mastra agents.
 */

import { UniversalAIBrain, MastraAdapter } from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * Example: Basic Mastra Integration with MongoDB Superpowers
 */
async function basicMastraExample() {
  console.log('🚀 Starting Mastra + MongoDB AI Brain Integration Example\n');

  // 1. Initialize the Universal AI Brain
  console.log('1️⃣ Initializing Universal AI Brain...');
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'mastra_ai_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'mastra_vector_index',
      collectionName: 'mastra_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized!\n');

  // 2. Create Mastra adapter
  console.log('2️⃣ Creating Mastra adapter...');
  const mastraAdapter = new MastraAdapter({
    enableMemoryReplacement: true,
    enableWorkflowIntegration: true,
    enableToolEnhancement: true
  });

  // 3. Integrate with Mastra
  console.log('3️⃣ Integrating with Mastra framework...');
  const enhancedMastra = await mastraAdapter.integrate(brain);
  console.log('✅ Mastra integration complete!\n');

  // 4. Create an enhanced Mastra agent
  console.log('4️⃣ Creating enhanced Mastra agent...');
  const researchAgent = enhancedMastra.createAgent({
    name: 'Research Agent',
    instructions: 'You are a research agent specialized in gathering company intelligence and market insights.',
    model: {
      name: 'gpt-4',
      provider: 'openai'
    },
    tools: {
      webSearch: {
        name: 'Web Search',
        description: 'Search the web for information',
        execute: async ({ query }: { query: string }) => {
          // Mock web search - in real implementation, use Tavily or similar
          return {
            results: [
              {
                title: 'Sample Search Result',
                url: 'https://example.com',
                content: `Search results for: ${query}`
              }
            ]
          };
        }
      }
    }
  });

  console.log('✅ Enhanced Mastra agent created!\n');

  // 5. Store some knowledge in MongoDB for context
  console.log('5️⃣ Storing knowledge in MongoDB...');
  await brain.storeInteraction({
    conversationId: 'research_knowledge',
    userMessage: 'What is MongoDB Atlas Vector Search?',
    assistantResponse: 'MongoDB Atlas Vector Search is a fully managed vector database service that enables semantic search and AI applications. It provides native vector search capabilities within MongoDB, allowing you to store, index, and query high-dimensional vector embeddings alongside your operational data.',
    context: [],
    framework: 'mastra',
    metadata: {
      type: 'knowledge_base',
      topic: 'mongodb_vector_search'
    }
  });

  await brain.storeInteraction({
    conversationId: 'research_knowledge',
    userMessage: 'What are the benefits of using MongoDB for AI applications?',
    assistantResponse: 'MongoDB offers several benefits for AI applications: 1) Unified data platform - store operational data and vectors together, 2) Native vector search capabilities, 3) Flexible document model for evolving AI schemas, 4) Real-time change streams for live AI updates, 5) Built-in security and compliance features, 6) Global distribution for AI at scale.',
    context: [],
    framework: 'mastra',
    metadata: {
      type: 'knowledge_base',
      topic: 'mongodb_ai_benefits'
    }
  });

  console.log('✅ Knowledge stored in MongoDB!\n');

  // 6. Test the enhanced agent with context injection
  console.log('6️⃣ Testing enhanced agent with MongoDB context injection...');
  
  const response1 = await researchAgent.generate([
    { role: 'user', content: 'Tell me about MongoDB for AI applications' }
  ], {
    conversationId: 'mastra_demo_session'
  });

  console.log('🤖 Agent Response (with MongoDB context):');
  console.log(response1.text);
  console.log('\n📊 Injected Context Sources:', response1.enhancedContext?.map(c => c.source));
  console.log('\n');

  // 7. Test memory persistence
  console.log('7️⃣ Testing memory persistence...');
  
  const response2 = await researchAgent.generate([
    { role: 'user', content: 'What did we just discuss about MongoDB?' }
  ], {
    conversationId: 'mastra_demo_session'
  });

  console.log('🧠 Agent Response (with memory):');
  console.log(response2.text);
  console.log('\n');

  // 8. Test streaming with context
  console.log('8️⃣ Testing streaming with MongoDB context...');
  
  const stream = await researchAgent.stream([
    { role: 'user', content: 'Explain vector search in simple terms' }
  ], {
    conversationId: 'mastra_demo_session'
  });

  console.log('🌊 Streaming Response:');
  for await (const chunk of stream) {
    process.stdout.write(chunk.content || '');
  }
  console.log('\n\n');

  console.log('🎉 Mastra + MongoDB AI Brain integration example completed successfully!');
}

/**
 * Example: Advanced Mastra Workflow with MongoDB Intelligence
 */
async function advancedMastraWorkflowExample() {
  console.log('🚀 Advanced Mastra Workflow Example\n');

  // Initialize brain and adapter (same as above)
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'mastra_workflow_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'workflow_vector_index',
      collectionName: 'workflow_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const mastraAdapter = new MastraAdapter();
  const enhancedMastra = await mastraAdapter.integrate(brain);

  // Create multiple specialized agents
  const researchAgent = enhancedMastra.createAgent({
    name: 'Research Specialist',
    instructions: 'You specialize in gathering comprehensive company research and market intelligence.',
    model: { name: 'gpt-4', provider: 'openai' }
  });

  const analysisAgent = enhancedMastra.createAgent({
    name: 'Analysis Expert',
    instructions: 'You analyze research data and extract key insights, trends, and recommendations.',
    model: { name: 'gpt-4', provider: 'openai' }
  });

  const reportAgent = enhancedMastra.createAgent({
    name: 'Report Writer',
    instructions: 'You create professional, well-structured reports based on research and analysis.',
    model: { name: 'gpt-4', provider: 'openai' }
  });

  // Simulate a multi-agent workflow
  console.log('1️⃣ Research Phase...');
  const researchResult = await researchAgent.generate([
    { role: 'user', content: 'Research MongoDB Atlas and its AI capabilities' }
  ], { conversationId: 'workflow_session' });

  console.log('📊 Research completed');

  console.log('\n2️⃣ Analysis Phase...');
  const analysisResult = await analysisAgent.generate([
    { role: 'user', content: `Analyze this research data: ${researchResult.text}` }
  ], { conversationId: 'workflow_session' });

  console.log('🔍 Analysis completed');

  console.log('\n3️⃣ Report Generation Phase...');
  const reportResult = await reportAgent.generate([
    { role: 'user', content: `Create a professional report based on this analysis: ${analysisResult.text}` }
  ], { conversationId: 'workflow_session' });

  console.log('📝 Report completed');
  console.log('\n📋 Final Report:');
  console.log(reportResult.text);

  console.log('\n🎉 Advanced workflow completed with MongoDB context sharing!');
}

/**
 * Example: Mastra Memory Integration
 */
async function mastraMemoryExample() {
  console.log('🧠 Mastra Memory Integration Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'mastra_memory_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'memory_vector_index',
      collectionName: 'memory_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const mastraAdapter = new MastraAdapter({
    enableMemoryReplacement: true
  });

  const enhancedMastra = await mastraAdapter.integrate(brain);

  // Create agent with MongoDB memory
  const memoryAgent = enhancedMastra.createAgent({
    name: 'Memory Agent',
    instructions: 'You have perfect memory powered by MongoDB. Remember everything and make connections.',
    model: { name: 'gpt-4', provider: 'openai' }
  });

  // Test memory storage and retrieval
  console.log('1️⃣ Storing information...');
  await memoryAgent.memory.store({
    input: 'User preference',
    output: 'The user prefers MongoDB for AI applications because of its vector search capabilities'
  });

  console.log('2️⃣ Retrieving related information...');
  const memories = await memoryAgent.memory.retrieve('user preferences about databases');
  console.log('🧠 Retrieved memories:', memories);

  console.log('\n🎉 Memory integration example completed!');
}

// Run examples
async function runExamples() {
  try {
    await basicMastraExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await advancedMastraWorkflowExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await mastraMemoryExample();
  } catch (error) {
    console.error('❌ Error running examples:', error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runExamples();
}

export {
  basicMastraExample,
  advancedMastraWorkflowExample,
  mastraMemoryExample
};
