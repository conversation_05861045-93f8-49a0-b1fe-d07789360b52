/**
 * @file LangChain.js Integration Example
 * 
 * This example demonstrates how to integrate the Universal AI Brain with LangChain.js,
 * providing MongoDB-powered intelligence to LangChain applications.
 */

import { UniversalAIBrain, LangChainJSAdapter } from '@mongodb-ai/core';
import { config } from 'dotenv';

// Load environment variables
config();

/**
 * Example: Basic LangChain.js Integration with MongoDB Superpowers
 */
async function basicLangChainExample() {
  console.log('🚀 Starting LangChain.js + MongoDB AI Brain Integration Example\n');

  // 1. Initialize the Universal AI Brain
  console.log('1️⃣ Initializing Universal AI Brain...');
  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'langchain_ai_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'langchain_vector_index',
      collectionName: 'langchain_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();
  console.log('✅ Universal AI Brain initialized!\n');

  // 2. Create LangChain adapter
  console.log('2️⃣ Creating LangChain.js adapter...');
  const langchainAdapter = new LangChainJSAdapter({
    enableVectorStoreReplacement: true,
    enableMemoryReplacement: true,
    enableChainEnhancement: true
  });

  // 3. Integrate with LangChain.js
  console.log('3️⃣ Integrating with LangChain.js...');
  const enhancedLangChain = await langchainAdapter.integrate(brain);
  console.log('✅ LangChain.js integration complete!\n');

  // 4. Store some knowledge in MongoDB for context
  console.log('4️⃣ Storing knowledge in MongoDB...');
  await brain.storeInteraction({
    conversationId: 'langchain_knowledge',
    userMessage: 'What is LangChain?',
    assistantResponse: 'LangChain is a framework for developing applications powered by language models. It provides abstractions and tools for working with LLMs, including chains, agents, memory, and vector stores. LangChain makes it easier to build complex AI applications by providing reusable components.',
    context: [],
    framework: 'langchain',
    metadata: {
      type: 'knowledge_base',
      topic: 'langchain_framework'
    }
  });

  await brain.storeInteraction({
    conversationId: 'langchain_knowledge',
    userMessage: 'What are LangChain chains?',
    assistantResponse: 'LangChain chains are sequences of calls to components like LLMs, tools, or data preprocessing steps. They allow you to combine multiple operations into a single, more complex operation. Common types include LLMChain, SequentialChain, and RouterChain.',
    context: [],
    framework: 'langchain',
    metadata: {
      type: 'knowledge_base',
      topic: 'langchain_chains'
    }
  });

  console.log('✅ Knowledge stored in MongoDB!\n');

  // 5. Test MongoDB Vector Store
  console.log('5️⃣ Testing MongoDB Vector Store...');
  const mongoVectorStore = enhancedLangChain.MongoDBVectorStore;

  // Add documents to vector store
  await mongoVectorStore.addDocuments([
    {
      pageContent: 'LangChain provides a standard interface for chains, lots of integrations with other tools, and end-to-end chains for common applications.',
      metadata: { source: 'langchain_docs', category: 'overview' }
    },
    {
      pageContent: 'Vector stores allow you to store and search over unstructured data. They are often used in retrieval augmented generation (RAG) flows.',
      metadata: { source: 'langchain_docs', category: 'vector_stores' }
    }
  ]);

  // Search the vector store
  const searchResults = await mongoVectorStore.similaritySearch('What is LangChain used for?', 2);
  console.log('🔍 Vector Store Search Results:');
  searchResults.forEach((doc, index) => {
    console.log(`${index + 1}. ${doc.pageContent}`);
    console.log(`   Metadata: ${JSON.stringify(doc.metadata)}\n`);
  });

  // 6. Test MongoDB Memory
  console.log('6️⃣ Testing MongoDB Memory...');
  const mongoMemory = enhancedLangChain.MongoDBMemory;

  // Save context to memory
  await mongoMemory.saveContext(
    { input: 'What is retrieval augmented generation?' },
    { output: 'RAG is a technique that combines retrieval of relevant documents with generation to provide more accurate and contextual responses.' }
  );

  // Load memory variables
  const memoryVars = await mongoMemory.loadMemoryVariables({ input: 'Tell me about RAG' });
  console.log('🧠 Memory Variables:');
  console.log('History:', memoryVars.history);
  console.log('\n');

  // 7. Test Enhanced Chat Model
  console.log('7️⃣ Testing Enhanced Chat Model...');
  
  // Mock LangChain chat model
  const mockChatModel = {
    async invoke(messages: any[]) {
      return {
        content: 'Enhanced response from MongoDB-powered LangChain model',
        additional_kwargs: {}
      };
    },
    async stream(messages: any[]) {
      return {
        async *[Symbol.asyncIterator]() {
          yield { content: 'Enhanced ' };
          yield { content: 'streaming ' };
          yield { content: 'response' };
        }
      };
    }
  };

  const enhancedChatModel = enhancedLangChain.enhancedChatModel(mockChatModel, 'langchain_demo');
  
  const chatResult = await enhancedChatModel.invoke([
    { role: 'user', content: 'Explain how LangChain chains work' }
  ]);

  console.log('🤖 Enhanced Chat Model Response:');
  console.log(chatResult.content);
  console.log('\n');

  console.log('🎉 LangChain.js + MongoDB AI Brain integration example completed successfully!');
}

/**
 * Example: RAG (Retrieval Augmented Generation) with MongoDB
 */
async function ragExample() {
  console.log('📚 RAG with MongoDB Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'langchain_rag_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'rag_vector_index',
      collectionName: 'rag_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const langchainAdapter = new LangChainJSAdapter();
  const enhancedLangChain = await langchainAdapter.integrate(brain);

  // 1. Set up document store
  console.log('1️⃣ Setting up document store...');
  const vectorStore = enhancedLangChain.MongoDBVectorStore;

  // Add knowledge documents
  const documents = [
    {
      pageContent: 'MongoDB Atlas Vector Search enables semantic search and RAG applications by storing and querying high-dimensional vector embeddings alongside operational data.',
      metadata: { source: 'mongodb_docs', topic: 'vector_search' }
    },
    {
      pageContent: 'LangChain provides abstractions for building RAG applications, including document loaders, text splitters, vector stores, and retrieval chains.',
      metadata: { source: 'langchain_docs', topic: 'rag' }
    },
    {
      pageContent: 'Retrieval Augmented Generation combines the power of large language models with external knowledge retrieval to provide more accurate and up-to-date responses.',
      metadata: { source: 'ai_research', topic: 'rag_concept' }
    },
    {
      pageContent: 'Vector embeddings are numerical representations of text that capture semantic meaning, enabling similarity search and clustering operations.',
      metadata: { source: 'ai_research', topic: 'embeddings' }
    }
  ];

  await vectorStore.addDocuments(documents);
  console.log('✅ Documents added to MongoDB vector store');

  // 2. Set up retriever
  console.log('\n2️⃣ Setting up MongoDB retriever...');
  const retriever = enhancedLangChain.mongoDBRetriever;

  // 3. Test retrieval
  console.log('\n3️⃣ Testing document retrieval...');
  const query = 'How does vector search work in MongoDB?';
  const retrievedDocs = await retriever.getRelevantDocuments(query);

  console.log(`🔍 Retrieved ${retrievedDocs.length} documents for query: "${query}"`);
  retrievedDocs.forEach((doc, index) => {
    console.log(`${index + 1}. ${doc.pageContent}`);
    console.log(`   Source: ${doc.metadata.source}, Score: ${doc.metadata.score}\n`);
  });

  // 4. Simulate RAG chain
  console.log('4️⃣ Simulating RAG chain...');
  
  // In a real implementation, this would be a proper LangChain RAG chain
  const ragResponse = `Based on the retrieved documents, here's how vector search works in MongoDB:

${retrievedDocs.map(doc => `- ${doc.pageContent}`).join('\n')}

MongoDB Atlas Vector Search enables you to perform semantic search by storing vector embeddings alongside your operational data, making it easy to build RAG applications without managing separate vector databases.`;

  console.log('🤖 RAG Response:');
  console.log(ragResponse);

  console.log('\n🎉 RAG example completed!');
}

/**
 * Example: LangChain Agents with MongoDB Tools
 */
async function agentsExample() {
  console.log('🤖 LangChain Agents with MongoDB Tools Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'langchain_agents_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'agents_vector_index',
      collectionName: 'agents_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const langchainAdapter = new LangChainJSAdapter();
  const enhancedLangChain = await langchainAdapter.integrate(brain);

  // Get MongoDB tools for LangChain agents
  const mongoDBTools = enhancedLangChain.mongoDBTools;

  console.log('1️⃣ Available MongoDB Tools:');
  mongoDBTools.forEach((tool, index) => {
    console.log(`${index + 1}. ${tool.name}: ${tool.description}`);
  });

  // 2. Test MongoDB search tool
  console.log('\n2️⃣ Testing MongoDB search tool...');
  
  // First store some information
  await mongoDBTools[1].func('MongoDB Atlas is a fully managed cloud database service with built-in vector search capabilities for AI applications.');
  await mongoDBTools[1].func('LangChain agents can use tools to interact with external systems and data sources.');

  // Then search for it
  const searchResult = await mongoDBTools[0].func('What is MongoDB Atlas?');
  console.log('🔍 Search Result:');
  console.log(searchResult);

  // 3. Simulate agent execution
  console.log('\n3️⃣ Simulating agent execution with MongoDB tools...');
  
  const agentQuery = 'Find information about MongoDB and explain its AI capabilities';
  console.log(`🤖 Agent Query: ${agentQuery}`);
  
  // Agent would use the search tool
  const agentSearchResult = await mongoDBTools[0].func('MongoDB AI capabilities vector search');
  console.log('\n🔧 Tool Execution Result:');
  console.log(agentSearchResult);
  
  // Agent would then generate a response based on the tool result
  const agentResponse = `Based on my search, here's what I found about MongoDB's AI capabilities:

${agentSearchResult}

MongoDB Atlas provides native vector search capabilities that enable AI applications to store, index, and query high-dimensional embeddings alongside operational data, making it an excellent choice for building RAG applications and other AI-powered systems.`;

  console.log('\n🤖 Agent Response:');
  console.log(agentResponse);

  console.log('\n🎉 Agents example completed!');
}

/**
 * Example: Conversation Chain with MongoDB Memory
 */
async function conversationChainExample() {
  console.log('💬 Conversation Chain with MongoDB Memory Example\n');

  const brain = new UniversalAIBrain({
    mongoConfig: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
      dbName: 'langchain_conversation_brain'
    },
    embeddingConfig: {
      provider: 'openai',
      model: 'text-embedding-3-small',
      apiKey: process.env.OPENAI_API_KEY!,
      dimensions: 1536
    },
    vectorSearchConfig: {
      indexName: 'conversation_vector_index',
      collectionName: 'conversation_embeddings',
      minScore: 0.7
    }
  });

  await brain.initialize();

  const langchainAdapter = new LangChainJSAdapter();
  const enhancedLangChain = await langchainAdapter.integrate(brain);

  const memory = enhancedLangChain.MongoDBMemory;

  // Simulate a conversation
  const conversations = [
    {
      input: 'Hi, I\'m working on a new AI project using LangChain.',
      output: 'That sounds exciting! LangChain is a great framework for building AI applications. What specific aspects of your project are you working on?'
    },
    {
      input: 'I need to implement a RAG system for document search.',
      output: 'RAG (Retrieval Augmented Generation) is perfect for document search! You\'ll need a vector store to index your documents and a retrieval chain to find relevant content. Are you planning to use a specific vector database?'
    },
    {
      input: 'I\'m considering MongoDB Atlas Vector Search.',
      output: 'Excellent choice! MongoDB Atlas Vector Search is great because it lets you store vectors alongside your operational data. This means you don\'t need a separate vector database, which simplifies your architecture significantly.'
    }
  ];

  console.log('💬 Simulating conversation with MongoDB memory...\n');

  for (let i = 0; i < conversations.length; i++) {
    const { input, output } = conversations[i];
    
    console.log(`👤 User: ${input}`);
    console.log(`🤖 Assistant: ${output}`);
    
    // Save to memory
    await memory.saveContext({ input }, { output });
    
    console.log('✅ Saved to MongoDB memory\n');
  }

  // Test memory retrieval
  console.log('🧠 Testing memory retrieval...');
  const memoryVars = await memory.loadMemoryVariables({ 
    input: 'What database did I mention for my RAG system?' 
  });

  console.log('📚 Retrieved conversation history:');
  console.log(memoryVars.history);

  console.log('\n🎉 Conversation chain example completed!');
}

// Run examples
async function runExamples() {
  try {
    await basicLangChainExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await ragExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await agentsExample();
    console.log('\n' + '='.repeat(80) + '\n');
    
    await conversationChainExample();
  } catch (error) {
    console.error('❌ Error running examples:', error);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  runExamples();
}

export {
  basicLangChainExample,
  ragExample,
  agentsExample,
  conversationChainExample
};
