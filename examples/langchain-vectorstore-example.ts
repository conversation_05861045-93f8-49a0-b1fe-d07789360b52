import { Document } from '@langchain/core/documents';
import { OpenAIEmbeddings } from '@langchain/openai';
import { MongoDBAtlasVectorSearch } from '@langchain/mongodb';
import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';

dotenv.config();

async function run() {
  if (!process.env.MONGODB_URI || !process.env.OPENAI_API_KEY) {
    throw new Error('Missing MONGODB_URI or OPENAI_API_KEY environment variables');
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();

  const db = client.db('ai_agents');
  const collection = db.collection('vector_embeddings');

  const vectorStore = new MongoDBAtlasVectorSearch(new OpenAIEmbeddings(), {
    collection,
    indexName: 'vector_search_index',
    textKey: 'content.text',
    embeddingKey: 'embedding.values',
  });

  const docs = [
    new Document({
      pageContent: 'The quick brown fox jumps over the lazy dog.',
      metadata: { source: 'test' },
    }),
  ];

  await vectorStore.addDocuments(docs);

  const results = await vectorStore.similaritySearch('jumping fox', 1);
  console.log(results);

  await client.close();
}

run().catch(console.error);