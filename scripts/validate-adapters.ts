#!/usr/bin/env ts-node

/**
 * @file validate-adapters.ts - Validation script for framework adapters
 * 
 * This script validates that all 4 framework adapters can be imported and initialized
 * without errors, and checks their integration with real frameworks.
 */

import { config } from 'dotenv';

// Load environment variables
config();

interface ValidationResult {
  adapter: string;
  canImport: boolean;
  canInitialize: boolean;
  frameworkAvailable: boolean;
  realIntegration: boolean;
  errors: string[];
}

async function validateMastraAdapter(): Promise<ValidationResult> {
  const result: ValidationResult = {
    adapter: 'MastraAdapter',
    canImport: false,
    canInitialize: false,
    frameworkAvailable: false,
    realIntegration: false,
    errors: []
  };

  try {
    // Test import
    const { MastraAdapter } = await import('../packages/core/src/adapters/MastraAdapter');
    result.canImport = true;

    // Test initialization
    const adapter = new MastraAdapter();
    result.canInitialize = true;

    // Test framework availability
    try {
      require.resolve('@mastra/core');
      result.frameworkAvailable = true;
    } catch (error) {
      result.errors.push('Mastra framework not installed');
    }

    // Test real integration validation
    if (result.frameworkAvailable) {
      result.realIntegration = await adapter.validateRealIntegration();
    }

  } catch (error) {
    result.errors.push(`Import/initialization error: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
}

async function validateVercelAIAdapter(): Promise<ValidationResult> {
  const result: ValidationResult = {
    adapter: 'VercelAIAdapter',
    canImport: false,
    canInitialize: false,
    frameworkAvailable: false,
    realIntegration: false,
    errors: []
  };

  try {
    // Test import
    const { VercelAIAdapter } = await import('../packages/core/src/adapters/VercelAIAdapter');
    result.canImport = true;

    // Test initialization
    const adapter = new VercelAIAdapter();
    result.canInitialize = true;

    // Test framework availability
    try {
      require.resolve('ai');
      result.frameworkAvailable = true;
    } catch (error) {
      result.errors.push('Vercel AI SDK not installed');
    }

    // Test real integration validation
    if (result.frameworkAvailable) {
      result.realIntegration = await adapter.validateRealIntegration();
    }

  } catch (error) {
    result.errors.push(`Import/initialization error: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
}

async function validateLangChainJSAdapter(): Promise<ValidationResult> {
  const result: ValidationResult = {
    adapter: 'LangChainJSAdapter',
    canImport: false,
    canInitialize: false,
    frameworkAvailable: false,
    realIntegration: false,
    errors: []
  };

  try {
    // Test import
    const { LangChainJSAdapter } = await import('../packages/core/src/adapters/LangChainJSAdapter');
    result.canImport = true;

    // Test initialization
    const adapter = new LangChainJSAdapter();
    result.canInitialize = true;

    // Test framework availability
    try {
      require.resolve('langchain');
      result.frameworkAvailable = true;
    } catch (error) {
      result.errors.push('LangChain.js not installed');
    }

    // Test real integration validation (if method exists)
    if (result.frameworkAvailable && typeof adapter.validateRealIntegration === 'function') {
      result.realIntegration = await adapter.validateRealIntegration();
    }

  } catch (error) {
    result.errors.push(`Import/initialization error: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
}

async function validateOpenAIAgentsAdapter(): Promise<ValidationResult> {
  const result: ValidationResult = {
    adapter: 'OpenAIAgentsAdapter',
    canImport: false,
    canInitialize: false,
    frameworkAvailable: false,
    realIntegration: false,
    errors: []
  };

  try {
    // Test import
    const { OpenAIAgentsAdapter } = await import('../packages/core/src/adapters/OpenAIAgentsAdapter');
    result.canImport = true;

    // Test initialization
    const adapter = new OpenAIAgentsAdapter();
    result.canInitialize = true;

    // Test framework availability
    try {
      require.resolve('@openai/agents');
      result.frameworkAvailable = true;
    } catch (error) {
      result.errors.push('OpenAI Agents not installed');
    }

    // Test real integration validation (if method exists)
    if (result.frameworkAvailable && typeof adapter.validateRealIntegration === 'function') {
      result.realIntegration = await adapter.validateRealIntegration();
    }

  } catch (error) {
    result.errors.push(`Import/initialization error: ${error instanceof Error ? error.message : String(error)}`);
  }

  return result;
}

function printResults(results: ValidationResult[]): void {
  console.log('\n🔍 FRAMEWORK ADAPTER VALIDATION RESULTS\n');
  console.log('=' .repeat(80));

  for (const result of results) {
    console.log(`\n📦 ${result.adapter}`);
    console.log(`   Import:           ${result.canImport ? '✅' : '❌'}`);
    console.log(`   Initialize:       ${result.canInitialize ? '✅' : '❌'}`);
    console.log(`   Framework:        ${result.frameworkAvailable ? '✅' : '⚠️'}`);
    console.log(`   Real Integration: ${result.realIntegration ? '✅' : '⚠️'}`);
    
    if (result.errors.length > 0) {
      console.log(`   Errors:`);
      result.errors.forEach(error => console.log(`     - ${error}`));
    }
  }

  console.log('\n' + '=' .repeat(80));
  
  const summary = {
    total: results.length,
    canImport: results.filter(r => r.canImport).length,
    canInitialize: results.filter(r => r.canInitialize).length,
    frameworkAvailable: results.filter(r => r.frameworkAvailable).length,
    realIntegration: results.filter(r => r.realIntegration).length
  };

  console.log('\n📊 SUMMARY:');
  console.log(`   Total Adapters:        ${summary.total}`);
  console.log(`   Can Import:            ${summary.canImport}/${summary.total}`);
  console.log(`   Can Initialize:        ${summary.canInitialize}/${summary.total}`);
  console.log(`   Framework Available:   ${summary.frameworkAvailable}/${summary.total}`);
  console.log(`   Real Integration:      ${summary.realIntegration}/${summary.total}`);

  const overallStatus = summary.canImport === summary.total && summary.canInitialize === summary.total;
  console.log(`\n🎯 OVERALL STATUS: ${overallStatus ? '✅ ADAPTERS WORKING' : '❌ ISSUES FOUND'}`);
}

async function main(): Promise<void> {
  console.log('🚀 Starting Framework Adapter Validation...\n');

  const results = await Promise.all([
    validateMastraAdapter(),
    validateVercelAIAdapter(),
    validateLangChainJSAdapter(),
    validateOpenAIAgentsAdapter()
  ]);

  printResults(results);
}

// Run validation
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export { main as validateAdapters };
