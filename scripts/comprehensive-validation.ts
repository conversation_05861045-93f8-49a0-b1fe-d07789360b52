#!/usr/bin/env ts-node

/**
 * @file comprehensive-validation.ts - Complete system validation against real framework documentation
 * 
 * This script performs the most thorough analysis possible of the AI Brain project:
 * 1. Framework adapter validation against real documentation
 * 2. TypeScript compilation error analysis
 * 3. Test suite failure analysis
 * 4. MongoDB integration verification
 * 5. Feature completeness audit
 * 6. Architecture validation
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';

interface ValidationResult {
  component: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  issues: string[];
  recommendations: string[];
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

interface FrameworkValidation {
  framework: string;
  adapterFile: string;
  apiAccuracy: ValidationResult;
  typeCompatibility: ValidationResult;
  implementationCompleteness: ValidationResult;
  documentationAlignment: ValidationResult;
}

interface CompilationAnalysis {
  totalErrors: number;
  errorsByCategory: Record<string, number>;
  errorsByFile: Record<string, number>;
  criticalErrors: string[];
  fixableErrors: string[];
}

interface TestAnalysis {
  totalTests: number;
  passingTests: number;
  failingTests: number;
  failuresByCategory: Record<string, number>;
  criticalFailures: string[];
  infrastructureIssues: string[];
}

class ComprehensiveValidator {
  private results: ValidationResult[] = [];
  private frameworkValidations: FrameworkValidation[] = [];
  private compilationAnalysis: CompilationAnalysis | null = null;
  private testAnalysis: TestAnalysis | null = null;

  async runCompleteValidation(): Promise<void> {
    console.log('🔍 STARTING COMPREHENSIVE AI BRAIN VALIDATION\n');
    console.log('=' .repeat(80));

    // Phase 1: Framework Adapter Validation
    await this.validateFrameworkAdapters();

    // Phase 2: Core System Validation
    await this.validateCoreSystem();

    // Phase 3: TypeScript Compilation Analysis
    await this.analyzeCompilationErrors();

    // Phase 4: Test Suite Analysis
    await this.analyzeTestSuite();

    // Phase 5: MongoDB Integration Verification
    await this.validateMongoDBIntegration();

    // Phase 6: Architecture Validation
    await this.validateArchitecture();

    // Generate comprehensive report
    await this.generateComprehensiveReport();
  }

  private async validateFrameworkAdapters(): Promise<void> {
    console.log('\n📋 PHASE 1: FRAMEWORK ADAPTER VALIDATION');
    console.log('-' .repeat(50));

    const frameworks = [
      { name: 'Mastra', file: 'packages/core/src/adapters/MastraAdapter.ts' },
      { name: 'Vercel AI', file: 'packages/core/src/adapters/VercelAIAdapter.ts' },
      { name: 'LangChain.js', file: 'packages/core/src/adapters/LangChainJSAdapter.ts' },
      { name: 'OpenAI Agents', file: 'packages/core/src/adapters/OpenAIAgentsAdapter.ts' }
    ];

    for (const framework of frameworks) {
      console.log(`\n🔍 Validating ${framework.name} Adapter...`);
      
      const validation = await this.validateFrameworkAdapter(framework.name, framework.file);
      this.frameworkValidations.push(validation);
      
      this.logFrameworkValidation(validation);
    }
  }

  private async validateFrameworkAdapter(frameworkName: string, filePath: string): Promise<FrameworkValidation> {
    const validation: FrameworkValidation = {
      framework: frameworkName,
      adapterFile: filePath,
      apiAccuracy: { component: 'API Accuracy', status: 'PASS', issues: [], recommendations: [], severity: 'LOW' },
      typeCompatibility: { component: 'Type Compatibility', status: 'PASS', issues: [], recommendations: [], severity: 'LOW' },
      implementationCompleteness: { component: 'Implementation Completeness', status: 'PASS', issues: [], recommendations: [], severity: 'LOW' },
      documentationAlignment: { component: 'Documentation Alignment', status: 'PASS', issues: [], recommendations: [], severity: 'LOW' }
    };

    try {
      const adapterContent = fs.readFileSync(filePath, 'utf8');
      
      // Validate based on framework-specific patterns
      switch (frameworkName) {
        case 'Mastra':
          await this.validateMastraAdapter(adapterContent, validation);
          break;
        case 'Vercel AI':
          await this.validateVercelAIAdapter(adapterContent, validation);
          break;
        case 'LangChain.js':
          await this.validateLangChainAdapter(adapterContent, validation);
          break;
        case 'OpenAI Agents':
          await this.validateOpenAIAgentsAdapter(adapterContent, validation);
          break;
      }
    } catch (error) {
      validation.apiAccuracy.status = 'FAIL';
      validation.apiAccuracy.issues.push(`Failed to read adapter file: ${error instanceof Error ? error.message : String(error)}`);
      validation.apiAccuracy.severity = 'CRITICAL';
    }

    return validation;
  }

  private async validateMastraAdapter(content: string, validation: FrameworkValidation): Promise<void> {
    // Validate against REAL Mastra documentation patterns
    const requiredPatterns = [
      { pattern: /Agent.*generate/, description: 'Uses real Agent.generate() method' },
      { pattern: /Agent.*stream/, description: 'Uses real Agent.stream() method' },
      { pattern: /resourceId.*threadId/, description: 'Follows Mastra memory requirements' },
      { pattern: /@mastra\/core/, description: 'Imports from correct package' },
      { pattern: /createTool/, description: 'Uses real createTool pattern' }
    ];

    for (const { pattern, description } of requiredPatterns) {
      if (!pattern.test(content)) {
        validation.apiAccuracy.issues.push(`Missing: ${description}`);
        validation.apiAccuracy.status = 'FAIL';
        validation.apiAccuracy.severity = 'HIGH';
      }
    }

    // Check for deprecated patterns
    const deprecatedPatterns = [
      { pattern: /new Agent\(\)/, description: 'Should use Agent constructor correctly' }
    ];

    for (const { pattern, description } of deprecatedPatterns) {
      if (pattern.test(content)) {
        validation.documentationAlignment.issues.push(`Deprecated: ${description}`);
        validation.documentationAlignment.status = 'WARNING';
      }
    }
  }

  private async validateVercelAIAdapter(content: string, validation: FrameworkValidation): Promise<void> {
    // Validate against REAL Vercel AI SDK documentation patterns
    const requiredPatterns = [
      { pattern: /generateText/, description: 'Uses real generateText function' },
      { pattern: /streamText/, description: 'Uses real streamText function' },
      { pattern: /onFinish/, description: 'Uses proper onFinish callback' },
      { pattern: /textStream/, description: 'Uses correct stream response' },
      { pattern: /from ['"]ai['"]/, description: 'Imports from correct package' }
    ];

    for (const { pattern, description } of requiredPatterns) {
      if (!pattern.test(content)) {
        validation.apiAccuracy.issues.push(`Missing: ${description}`);
        validation.apiAccuracy.status = 'FAIL';
        validation.apiAccuracy.severity = 'HIGH';
      }
    }

    // Check for correct response handling
    if (!content.includes('finishReason') || !content.includes('usage')) {
      validation.implementationCompleteness.issues.push('Missing proper response handling');
      validation.implementationCompleteness.status = 'FAIL';
      validation.implementationCompleteness.severity = 'MEDIUM';
    }
  }

  private async validateLangChainAdapter(content: string, validation: FrameworkValidation): Promise<void> {
    // Validate against REAL LangChain.js documentation patterns
    const requiredPatterns = [
      { pattern: /@langchain\/core/, description: 'Uses real LangChain core package' },
      { pattern: /BaseLanguageModel/, description: 'Uses correct base model class' },
      { pattern: /ChatPromptTemplate/, description: 'Uses proper prompt templates' },
      { pattern: /RunnableSequence/, description: 'Uses LangChain runnable pattern' }
    ];

    for (const { pattern, description } of requiredPatterns) {
      if (!pattern.test(content)) {
        validation.apiAccuracy.issues.push(`Missing: ${description}`);
        validation.apiAccuracy.status = 'FAIL';
        validation.apiAccuracy.severity = 'HIGH';
      }
    }
  }

  private async validateOpenAIAgentsAdapter(content: string, validation: FrameworkValidation): Promise<void> {
    // Validate against REAL OpenAI documentation patterns
    const requiredPatterns = [
      { pattern: /openai/, description: 'Uses OpenAI client' },
      { pattern: /chat\.completions/, description: 'Uses chat completions API' },
      { pattern: /tools/, description: 'Supports function calling' },
      { pattern: /stream/, description: 'Supports streaming' }
    ];

    for (const { pattern, description } of requiredPatterns) {
      if (!pattern.test(content)) {
        validation.apiAccuracy.issues.push(`Missing: ${description}`);
        validation.apiAccuracy.status = 'FAIL';
        validation.apiAccuracy.severity = 'HIGH';
      }
    }
  }

  private logFrameworkValidation(validation: FrameworkValidation): void {
    console.log(`\n📊 ${validation.framework} Adapter Results:`);
    
    const components = [
      validation.apiAccuracy,
      validation.typeCompatibility,
      validation.implementationCompleteness,
      validation.documentationAlignment
    ];

    for (const component of components) {
      const statusIcon = component.status === 'PASS' ? '✅' : component.status === 'WARNING' ? '⚠️' : '❌';
      console.log(`   ${statusIcon} ${component.component}: ${component.status}`);
      
      if (component.issues.length > 0) {
        component.issues.forEach(issue => console.log(`      - ${issue}`));
      }
    }
  }

  private async validateCoreSystem(): Promise<void> {
    console.log('\n📋 PHASE 2: CORE SYSTEM VALIDATION');
    console.log('-' .repeat(50));

    // Validate UniversalAIBrain
    await this.validateUniversalAIBrain();
    
    // Validate MongoVectorStore
    await this.validateMongoVectorStore();
    
    // Validate Collection Management
    await this.validateCollectionManagement();
  }

  private async validateUniversalAIBrain(): Promise<void> {
    console.log('\n🧠 Validating UniversalAIBrain...');
    
    const brainFile = 'packages/core/src/brain/UniversalAIBrain.ts';
    
    try {
      const content = fs.readFileSync(brainFile, 'utf8');
      
      const result: ValidationResult = {
        component: 'UniversalAIBrain',
        status: 'PASS',
        issues: [],
        recommendations: [],
        severity: 'LOW'
      };

      // Check for required methods
      const requiredMethods = [
        'initialize',
        'enhancePrompt',
        'storeInteraction',
        'retrieveRelevantContext',
        'integrateWithFramework'
      ];

      for (const method of requiredMethods) {
        if (!content.includes(`async ${method}`) && !content.includes(`${method}(`)) {
          result.issues.push(`Missing required method: ${method}`);
          result.status = 'FAIL';
          result.severity = 'HIGH';
        }
      }

      // Check MongoDB integration
      if (!content.includes('MongoConnection') || !content.includes('MongoVectorStore')) {
        result.issues.push('Missing MongoDB integration components');
        result.status = 'FAIL';
        result.severity = 'CRITICAL';
      }

      this.results.push(result);
      this.logValidationResult(result);
    } catch (error) {
      console.log(`❌ Failed to validate UniversalAIBrain: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async validateMongoVectorStore(): Promise<void> {
    console.log('\n🔍 Validating MongoVectorStore...');
    
    const vectorFile = 'packages/core/src/vector/MongoVectorStore.ts';
    
    try {
      const content = fs.readFileSync(vectorFile, 'utf8');
      
      const result: ValidationResult = {
        component: 'MongoVectorStore',
        status: 'PASS',
        issues: [],
        recommendations: [],
        severity: 'LOW'
      };

      // Check for MongoDB Atlas Vector Search patterns
      const requiredPatterns = [
        { pattern: /\$vectorSearch/, description: 'Uses MongoDB Atlas Vector Search' },
        { pattern: /vectorSearchScore/, description: 'Uses proper scoring' },
        { pattern: /hybridSearch/, description: 'Implements hybrid search' },
        { pattern: /createIndex/, description: 'Creates proper indexes' }
      ];

      for (const { pattern, description } of requiredPatterns) {
        if (!pattern.test(content)) {
          result.issues.push(`Missing: ${description}`);
          result.status = 'FAIL';
          result.severity = 'HIGH';
        }
      }

      this.results.push(result);
      this.logValidationResult(result);
    } catch (error) {
      console.log(`❌ Failed to validate MongoVectorStore: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async validateCollectionManagement(): Promise<void> {
    console.log('\n📚 Validating Collection Management...');

    const collectionsDir = 'packages/core/src/collections';

    try {
      const files = fs.readdirSync(collectionsDir);
      const collectionFiles = files.filter(f => f.endsWith('.ts') && f !== 'index.ts');

      const result: ValidationResult = {
        component: 'Collection Management',
        status: 'PASS',
        issues: [],
        recommendations: [],
        severity: 'LOW'
      };

      if (collectionFiles.length < 3) {
        result.issues.push('Insufficient collection implementations');
        result.status = 'WARNING';
        result.severity = 'MEDIUM';
      }

      // Check each collection file
      for (const file of collectionFiles) {
        const content = fs.readFileSync(path.join(collectionsDir, file), 'utf8');

        if (!content.includes('BaseCollection')) {
          result.issues.push(`${file}: Missing BaseCollection inheritance`);
          result.status = 'FAIL';
          result.severity = 'HIGH';
        }
      }

      this.results.push(result);
      this.logValidationResult(result);
    } catch (error) {
      console.log(`❌ Failed to validate Collection Management: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private logValidationResult(result: ValidationResult): void {
    const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'WARNING' ? '⚠️' : '❌';
    console.log(`   ${statusIcon} ${result.component}: ${result.status}`);

    if (result.issues.length > 0) {
      result.issues.forEach(issue => console.log(`      - ${issue}`));
    }
  }

  private async analyzeCompilationErrors(): Promise<void> {
    console.log('\n📋 PHASE 3: TYPESCRIPT COMPILATION ANALYSIS');
    console.log('-' .repeat(50));

    try {
      console.log('\n🔍 Running TypeScript compilation...');

      // Run TypeScript compiler and capture errors
      const tscOutput = execSync('npx tsc --noEmit --pretty false', {
        encoding: 'utf8',
        cwd: process.cwd()
      });

      console.log('✅ No TypeScript compilation errors found!');

      this.compilationAnalysis = {
        totalErrors: 0,
        errorsByCategory: {},
        errorsByFile: {},
        criticalErrors: [],
        fixableErrors: []
      };
    } catch (error) {
      console.log('❌ TypeScript compilation errors detected');

      const errorOutput = (error as any).stdout || (error instanceof Error ? error.message : String(error));
      this.compilationAnalysis = this.parseCompilationErrors(errorOutput);
      this.logCompilationAnalysis();
    }
  }

  private parseCompilationErrors(output: string): CompilationAnalysis {
    const lines = output.split('\n');
    const errors: string[] = [];
    const errorsByFile: Record<string, number> = {};
    const errorsByCategory: Record<string, number> = {};

    for (const line of lines) {
      if (line.includes('error TS')) {
        errors.push(line);

        // Extract file name
        const fileMatch = line.match(/^([^(]+)\(/);
        if (fileMatch) {
          const file = fileMatch[1];
          errorsByFile[file] = (errorsByFile[file] || 0) + 1;
        }

        // Categorize error
        if (line.includes('Cannot find module')) {
          errorsByCategory['Missing Imports'] = (errorsByCategory['Missing Imports'] || 0) + 1;
        } else if (line.includes('Property') && line.includes('does not exist')) {
          errorsByCategory['Missing Properties'] = (errorsByCategory['Missing Properties'] || 0) + 1;
        } else if (line.includes('Type') && line.includes('is not assignable')) {
          errorsByCategory['Type Mismatches'] = (errorsByCategory['Type Mismatches'] || 0) + 1;
        } else {
          errorsByCategory['Other'] = (errorsByCategory['Other'] || 0) + 1;
        }
      }
    }

    return {
      totalErrors: errors.length,
      errorsByCategory,
      errorsByFile,
      criticalErrors: errors.filter(e => e.includes('Cannot find module')),
      fixableErrors: errors.filter(e => !e.includes('Cannot find module'))
    };
  }

  private logCompilationAnalysis(): void {
    if (!this.compilationAnalysis) return;

    console.log(`\n📊 Compilation Analysis Results:`);
    console.log(`   Total Errors: ${this.compilationAnalysis.totalErrors}`);

    console.log('\n📂 Errors by Category:');
    Object.entries(this.compilationAnalysis.errorsByCategory).forEach(([category, count]) => {
      console.log(`   ${category}: ${count}`);
    });

    console.log('\n📄 Top Files with Errors:');
    const sortedFiles = Object.entries(this.compilationAnalysis.errorsByFile)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);

    sortedFiles.forEach(([file, count]) => {
      console.log(`   ${file}: ${count} errors`);
    });

    console.log(`\n🚨 Critical Errors: ${this.compilationAnalysis.criticalErrors.length}`);
    console.log(`🔧 Fixable Errors: ${this.compilationAnalysis.fixableErrors.length}`);
  }

  private async analyzeTestSuite(): Promise<void> {
    console.log('\n📋 PHASE 4: TEST SUITE ANALYSIS');
    console.log('-' .repeat(50));

    try {
      console.log('\n🧪 Running test suite...');

      const testOutput = execSync('npm test -- --verbose --no-coverage', {
        encoding: 'utf8',
        cwd: process.cwd(),
        timeout: 60000
      });

      this.testAnalysis = this.parseTestResults(testOutput);
      this.logTestAnalysis();
    } catch (error) {
      console.log('❌ Test suite execution failed');

      const testOutput = (error as any).stdout || (error instanceof Error ? error.message : String(error));
      this.testAnalysis = this.parseTestResults(testOutput);
      this.logTestAnalysis();
    }
  }

  private parseTestResults(output: string): TestAnalysis {
    const lines = output.split('\n');
    let totalTests = 0;
    let passingTests = 0;
    let failingTests = 0;
    const failuresByCategory: Record<string, number> = {};
    const criticalFailures: string[] = [];
    const infrastructureIssues: string[] = [];

    for (const line of lines) {
      // Parse Jest output
      if (line.includes('Tests:')) {
        const match = line.match(/(\d+) failed.*?(\d+) passed.*?(\d+) total/);
        if (match) {
          failingTests = parseInt(match[1]);
          passingTests = parseInt(match[2]);
          totalTests = parseInt(match[3]);
        }
      }

      // Categorize failures
      if (line.includes('FAIL') || line.includes('✕')) {
        if (line.includes('Cannot find module')) {
          failuresByCategory['Missing Dependencies'] = (failuresByCategory['Missing Dependencies'] || 0) + 1;
          infrastructureIssues.push(line);
        } else if (line.includes('TypeError')) {
          failuresByCategory['Type Errors'] = (failuresByCategory['Type Errors'] || 0) + 1;
        } else if (line.includes('ReferenceError')) {
          failuresByCategory['Reference Errors'] = (failuresByCategory['Reference Errors'] || 0) + 1;
          criticalFailures.push(line);
        } else {
          failuresByCategory['Other Failures'] = (failuresByCategory['Other Failures'] || 0) + 1;
        }
      }
    }

    return {
      totalTests,
      passingTests,
      failingTests,
      failuresByCategory,
      criticalFailures,
      infrastructureIssues
    };
  }

  private logTestAnalysis(): void {
    if (!this.testAnalysis) return;

    console.log(`\n📊 Test Suite Analysis Results:`);
    console.log(`   Total Tests: ${this.testAnalysis.totalTests}`);
    console.log(`   Passing: ${this.testAnalysis.passingTests}`);
    console.log(`   Failing: ${this.testAnalysis.failingTests}`);

    if (this.testAnalysis.totalTests > 0) {
      const passRate = (this.testAnalysis.passingTests / this.testAnalysis.totalTests * 100).toFixed(1);
      console.log(`   Pass Rate: ${passRate}%`);
    }

    console.log('\n📂 Failures by Category:');
    Object.entries(this.testAnalysis.failuresByCategory).forEach(([category, count]) => {
      console.log(`   ${category}: ${count}`);
    });

    console.log(`\n🚨 Critical Failures: ${this.testAnalysis.criticalFailures.length}`);
    console.log(`🔧 Infrastructure Issues: ${this.testAnalysis.infrastructureIssues.length}`);
  }

  private async validateMongoDBIntegration(): Promise<void> {
    console.log('\n📋 PHASE 5: MONGODB INTEGRATION VERIFICATION');
    console.log('-' .repeat(50));

    const result: ValidationResult = {
      component: 'MongoDB Integration',
      status: 'PASS',
      issues: [],
      recommendations: [],
      severity: 'LOW'
    };

    // Check MongoDB connection implementation
    const connectionFile = 'packages/core/src/persistance/MongoConnection.ts';

    try {
      const content = fs.readFileSync(connectionFile, 'utf8');

      // Validate MongoDB patterns
      const requiredPatterns = [
        { pattern: /MongoClient/, description: 'Uses MongoDB client' },
        { pattern: /connect/, description: 'Implements connection method' },
        { pattern: /healthCheck/, description: 'Implements health check' },
        { pattern: /singleton/i, description: 'Uses singleton pattern' }
      ];

      for (const { pattern, description } of requiredPatterns) {
        if (!pattern.test(content)) {
          result.issues.push(`Missing: ${description}`);
          result.status = 'FAIL';
          result.severity = 'HIGH';
        }
      }

      // Check for Atlas-specific features
      if (!content.includes('retryWrites') || !content.includes('w: majority')) {
        result.recommendations.push('Consider adding Atlas-optimized connection options');
      }

      this.results.push(result);
      this.logValidationResult(result);
    } catch (error) {
      result.status = 'FAIL';
      result.issues.push(`Failed to read MongoDB connection file: ${error instanceof Error ? error.message : String(error)}`);
      result.severity = 'CRITICAL';
      this.results.push(result);
      this.logValidationResult(result);
    }
  }

  private async validateArchitecture(): Promise<void> {
    console.log('\n📋 PHASE 6: ARCHITECTURE VALIDATION');
    console.log('-' .repeat(50));

    const result: ValidationResult = {
      component: 'Overall Architecture',
      status: 'PASS',
      issues: [],
      recommendations: [],
      severity: 'LOW'
    };

    // Check project structure
    const requiredDirectories = [
      'packages/core/src/brain',
      'packages/core/src/adapters',
      'packages/core/src/collections',
      'packages/core/src/vector',
      'packages/core/src/persistance',
      'examples'
    ];

    for (const dir of requiredDirectories) {
      if (!fs.existsSync(dir)) {
        result.issues.push(`Missing directory: ${dir}`);
        result.status = 'FAIL';
        result.severity = 'HIGH';
      }
    }

    // Check package.json structure
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

      if (!packageJson.workspaces) {
        result.issues.push('Missing monorepo workspace configuration');
        result.status = 'WARNING';
        result.severity = 'MEDIUM';
      }

      if (!packageJson.scripts?.build || !packageJson.scripts?.test) {
        result.issues.push('Missing essential npm scripts');
        result.status = 'WARNING';
        result.severity = 'MEDIUM';
      }
    } catch (error) {
      result.issues.push('Invalid package.json structure');
      result.status = 'FAIL';
      result.severity = 'HIGH';
    }

    this.results.push(result);
    this.logValidationResult(result);
  }

  private async generateComprehensiveReport(): Promise<void> {
    console.log('\n📋 GENERATING COMPREHENSIVE REPORT');
    console.log('=' .repeat(80));

    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(),
      frameworkValidations: this.frameworkValidations,
      compilationAnalysis: this.compilationAnalysis,
      testAnalysis: this.testAnalysis,
      systemValidations: this.results,
      recommendations: this.generateRecommendations(),
      actionPlan: this.generateActionPlan()
    };

    // Save detailed report
    fs.writeFileSync(
      'validation-report.json',
      JSON.stringify(report, null, 2)
    );

    // Generate human-readable summary
    this.generateHumanReadableReport(report);

    console.log('\n✅ Comprehensive validation complete!');
    console.log('📄 Detailed report saved to: validation-report.json');
    console.log('📋 Human-readable summary saved to: validation-summary.md');
  }

  private generateSummary(): any {
    const totalIssues = this.results.reduce((sum, r) => sum + r.issues.length, 0);
    const criticalIssues = this.results.filter(r => r.severity === 'CRITICAL').length;
    const passedComponents = this.results.filter(r => r.status === 'PASS').length;

    return {
      totalComponents: this.results.length,
      passedComponents,
      totalIssues,
      criticalIssues,
      compilationErrors: this.compilationAnalysis?.totalErrors || 0,
      testFailures: this.testAnalysis?.failingTests || 0,
      overallStatus: criticalIssues === 0 && totalIssues < 10 ? 'GOOD' : 'NEEDS_WORK'
    };
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    // Framework-specific recommendations
    this.frameworkValidations.forEach(fv => {
      if (fv.apiAccuracy.status === 'FAIL') {
        recommendations.push(`Fix ${fv.framework} adapter API compatibility issues`);
      }
    });

    // Compilation recommendations
    if (this.compilationAnalysis && this.compilationAnalysis.totalErrors > 0) {
      recommendations.push('Resolve TypeScript compilation errors');
      recommendations.push('Update import statements and type definitions');
    }

    // Test recommendations
    if (this.testAnalysis && this.testAnalysis.failingTests > 0) {
      recommendations.push('Fix failing test cases');
      recommendations.push('Update test infrastructure and mocks');
    }

    return recommendations;
  }

  private generateActionPlan(): any {
    return {
      phase1: 'Fix critical TypeScript compilation errors',
      phase2: 'Resolve framework adapter compatibility issues',
      phase3: 'Fix failing test cases',
      phase4: 'Optimize MongoDB integration',
      phase5: 'Enhance documentation and examples'
    };
  }

  private generateHumanReadableReport(report: any): void {
    const markdown = `# AI Brain Comprehensive Validation Report

Generated: ${report.timestamp}

## Executive Summary

- **Total Components Validated**: ${report.summary.totalComponents}
- **Components Passing**: ${report.summary.passedComponents}
- **Total Issues Found**: ${report.summary.totalIssues}
- **Critical Issues**: ${report.summary.criticalIssues}
- **Compilation Errors**: ${report.summary.compilationErrors}
- **Test Failures**: ${report.summary.testFailures}
- **Overall Status**: ${report.summary.overallStatus}

## Framework Adapter Validation

${this.frameworkValidations.map(fv => `
### ${fv.framework} Adapter
- **API Accuracy**: ${fv.apiAccuracy.status}
- **Type Compatibility**: ${fv.typeCompatibility.status}
- **Implementation Completeness**: ${fv.implementationCompleteness.status}
- **Documentation Alignment**: ${fv.documentationAlignment.status}
`).join('')}

## Key Recommendations

${report.recommendations.map((r: string) => `- ${r}`).join('\n')}

## Action Plan

1. **Phase 1**: ${report.actionPlan.phase1}
2. **Phase 2**: ${report.actionPlan.phase2}
3. **Phase 3**: ${report.actionPlan.phase3}
4. **Phase 4**: ${report.actionPlan.phase4}
5. **Phase 5**: ${report.actionPlan.phase5}

## Detailed Findings

See validation-report.json for complete technical details.
`;

    fs.writeFileSync('validation-summary.md', markdown);
  }
}

// Main execution
async function main(): Promise<void> {
  const validator = new ComprehensiveValidator();
  await validator.runCompleteValidation();
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export { ComprehensiveValidator };
