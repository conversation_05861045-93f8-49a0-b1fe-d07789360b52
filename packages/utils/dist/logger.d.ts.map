{"version": 3, "file": "logger.d.ts", "sourceRoot": "", "sources": ["../src/logger.ts"], "names": [], "mappings": "AAIA,oBAAY,QAAQ;IAClB,KAAK,UAAU;IACf,KAAK,UAAU;IACf,IAAI,SAAS;IACb,IAAI,SAAS;IACb,KAAK,UAAU;IACf,KAAK,UAAU;CAChB;AAGD,MAAM,WAAW,UAAU;IACzB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAGD,MAAM,WAAW,qBAAqB;IACpC,cAAc,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,WAAW,GAAG,cAAc,CAAC;IACvF,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CACvC;AAGD,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,cAAM,gBAAgB;IACpB,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,cAAc,CAAa;gBAEvB,OAAO,GAAE;QACnB,KAAK,CAAC,EAAE,QAAQ,CAAC;QACjB,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,OAAO,CAAC,EAAE,MAAM,CAAC;KACb;IAuCN;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAmB1B;;OAEG;IACH,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI;IAI9C;;OAEG;IACH,UAAU,IAAI,MAAM;IAIpB;;OAEG;IACH,QAAQ,IAAI,MAAM;IAMlB;;OAEG;IACH,OAAO,CAAC,cAAc;IAetB;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,IAAI;IAItD;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,IAAI;IAItD;;OAEG;IACH,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,IAAI;IAIrD;;OAEG;IACH,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI;IAIpE;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI;IAIrE;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI;IAIrE;;OAEG;IACH,cAAc,CAAC,OAAO,EAAE,qBAAqB,EAAE,OAAO,GAAE,UAAe,GAAG,IAAI;IAY9E;;OAEG;IACH,YAAY,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,GAAE,UAAe,GAAG,IAAI;IA8BnE;;OAEG;IACH,YAAY,CACV,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,SAAS,GAAG,WAAW,GAAG,QAAQ,EAC1C,OAAO,GAAE,UAAe,EACxB,KAAK,CAAC,EAAE,KAAK,GACZ,IAAI;IAgBP;;OAEG;IACH,aAAa,CACX,MAAM,EAAE,MAAM,EACd,MAAM,EAAE,SAAS,GAAG,WAAW,GAAG,QAAQ,EAC1C,OAAO,GAAE,UAAe,EACxB,KAAK,CAAC,EAAE,KAAK,GACZ,IAAI;IAgBP;;OAEG;IACH,YAAY,CACV,KAAK,EAAE,MAAM,EACb,YAAY,EAAE,MAAM,EACpB,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,UAAe,GACvB,IAAI;IAcP;;OAEG;IACH,YAAY,CACV,KAAK,EAAE,MAAM,EACb,aAAa,EAAE,MAAM,EACrB,WAAW,EAAE,MAAM,EACnB,eAAe,EAAE,MAAM,EACvB,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,UAAe,GACvB,IAAI;IAgBP;;OAEG;IACH,mBAAmB,CACjB,UAAU,EAAE,MAAM,EAClB,eAAe,EAAE,MAAM,EACvB,OAAO,EAAE,OAAO,EAChB,OAAO,GAAE,UAAe,GACvB,IAAI;IAaP;;OAEG;IACH,iBAAiB,CACf,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,MAAM,EACrB,UAAU,EAAE,MAAM,EAClB,OAAO,GAAE,UAAe,GACvB,IAAI;IAaP;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,UAAU,GAAG,gBAAgB;IAO5C;;OAEG;IACG,WAAW,CAAC,CAAC,EACjB,SAAS,EAAE,MAAM,EACjB,EAAE,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACpB,OAAO,GAAE,UAAe,GACvB,OAAO,CAAC,CAAC,CAAC;IA4Cb;;OAEG;IACH,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,GAAE,UAAe,GAAG,MAAM,IAAI;CAoBpE;AAGD,eAAO,MAAM,MAAM,kBAKjB,CAAC;AAGH,OAAO,EAAE,gBAAgB,EAAE,CAAC;AAC5B,eAAe,MAAM,CAAC"}