"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StructuredLogger = exports.logger = exports.LogLevel = void 0;
const pino_1 = __importDefault(require("pino"));
const uuid_1 = require("uuid");
// Log levels following MongoDB conventions
var LogLevel;
(function (LogLevel) {
    LogLevel["TRACE"] = "trace";
    LogLevel["DEBUG"] = "debug";
    LogLevel["INFO"] = "info";
    LogLevel["WARN"] = "warn";
    LogLevel["ERROR"] = "error";
    LogLevel["FATAL"] = "fatal";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class StructuredLogger {
    constructor(options = {}) {
        const { level = LogLevel.INFO, service_name = 'mongodb-ai-agent', environment = process.env.NODE_ENV || 'development', version = process.env.npm_package_version || '1.0.0' } = options;
        this.defaultContext = {
            trace_id: (0, uuid_1.v4)()
        };
        // Configure Pino with MongoDB-friendly structured logging
        this.logger = (0, pino_1.default)({
            level,
            base: {
                service: service_name,
                environment,
                version,
                hostname: process.env.HOSTNAME || 'unknown',
                pid: process.pid
            },
            timestamp: pino_1.default.stdTimeFunctions.isoTime,
            formatters: {
                level: (label) => ({ level: label }),
                log: (object) => {
                    // Ensure MongoDB-compatible field names (no dots in keys)
                    const sanitized = this.sanitizeForMongoDB(object);
                    return sanitized;
                }
            },
            serializers: {
                error: pino_1.default.stdSerializers.err,
                req: pino_1.default.stdSerializers.req,
                res: pino_1.default.stdSerializers.res
            }
        });
    }
    /**
     * Sanitize log object for MongoDB storage (remove dots from keys)
     */
    sanitizeForMongoDB(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        if (Array.isArray(obj)) {
            return obj.map(item => this.sanitizeForMongoDB(item));
        }
        const sanitized = {};
        for (const [key, value] of Object.entries(obj)) {
            // Replace dots with underscores for MongoDB compatibility
            const sanitizedKey = key.replace(/\./g, '_');
            sanitized[sanitizedKey] = this.sanitizeForMongoDB(value);
        }
        return sanitized;
    }
    /**
     * Set default context for all subsequent logs
     */
    setContext(context) {
        this.defaultContext = { ...this.defaultContext, ...context };
    }
    /**
     * Get current trace ID
     */
    getTraceId() {
        return this.defaultContext.trace_id || (0, uuid_1.v4)();
    }
    /**
     * Create a new trace ID
     */
    newTrace() {
        const traceId = (0, uuid_1.v4)();
        this.defaultContext.trace_id = traceId;
        return traceId;
    }
    /**
     * Log with structured context
     */
    logWithContext(level, message, context = {}, error) {
        const logContext = {
            ...this.defaultContext,
            ...context,
            timestamp: new Date().toISOString(),
            message
        };
        if (error) {
            this.logger[level]({ ...logContext, err: error }, message);
        }
        else {
            this.logger[level](logContext, message);
        }
    }
    /**
     * Log trace level message
     */
    trace(message, context = {}) {
        this.logWithContext(LogLevel.TRACE, message, context);
    }
    /**
     * Log debug level message
     */
    debug(message, context = {}) {
        this.logWithContext(LogLevel.DEBUG, message, context);
    }
    /**
     * Log info level message
     */
    info(message, context = {}) {
        this.logWithContext(LogLevel.INFO, message, context);
    }
    /**
     * Log warning level message
     */
    warn(message, context = {}, error) {
        this.logWithContext(LogLevel.WARN, message, context, error);
    }
    /**
     * Log error level message
     */
    error(message, context = {}, error) {
        this.logWithContext(LogLevel.ERROR, message, context, error);
    }
    /**
     * Log fatal level message
     */
    fatal(message, context = {}, error) {
        this.logWithContext(LogLevel.FATAL, message, context, error);
    }
    /**
     * Log MongoDB operation metrics
     */
    mongoOperation(metrics, context = {}) {
        this.info('MongoDB operation completed', {
            ...context,
            operation: 'mongodb_operation',
            ...metrics,
            metadata: {
                mongo_metrics: metrics,
                ...context.metadata
            }
        });
    }
    /**
     * Log agent performance metrics
     */
    agentMetrics(metrics, context = {}) {
        this.info('Agent operation completed', {
            ...context,
            agent_id: metrics.agent_id,
            operation: metrics.operation,
            duration_ms: metrics.duration_ms,
            cost_usd: metrics.cost_usd,
            tokens_used: metrics.tokens_used,
            confidence_score: metrics.confidence_score,
            metadata: {
                agent_metrics: metrics,
                success: metrics.success,
                ...context.metadata
            }
        });
        if (!metrics.success && metrics.error_message) {
            this.error('Agent operation failed', {
                ...context,
                agent_id: metrics.agent_id,
                operation: metrics.operation,
                error_code: 'AGENT_OPERATION_FAILED',
                metadata: {
                    error_message: metrics.error_message,
                    ...context.metadata
                }
            });
        }
    }
    /**
     * Log workflow step execution
     */
    workflowStep(stepId, status, context = {}, error) {
        const message = `Workflow step ${status}`;
        const stepContext = {
            ...context,
            step_id: stepId,
            operation: 'workflow_step',
            step_status: status
        };
        if (status === 'failed' && error) {
            this.error(message, stepContext, error);
        }
        else {
            this.info(message, stepContext);
        }
    }
    /**
     * Log tool execution
     */
    toolExecution(toolId, status, context = {}, error) {
        const message = `Tool execution ${status}`;
        const toolContext = {
            ...context,
            tool_id: toolId,
            operation: 'tool_execution',
            tool_status: status
        };
        if (status === 'failed' && error) {
            this.error(message, toolContext, error);
        }
        else {
            this.info(message, toolContext);
        }
    }
    /**
     * Log vector search operation
     */
    vectorSearch(query, resultsCount, duration, context = {}) {
        this.info('Vector search completed', {
            ...context,
            operation: 'vector_search',
            duration_ms: duration,
            metadata: {
                query_length: query.length,
                results_count: resultsCount,
                search_type: 'vector',
                ...context.metadata
            }
        });
    }
    /**
     * Log hybrid search operation
     */
    hybridSearch(query, vectorResults, textResults, combinedResults, duration, context = {}) {
        this.info('Hybrid search completed', {
            ...context,
            operation: 'hybrid_search',
            duration_ms: duration,
            metadata: {
                query_length: query.length,
                vector_results: vectorResults,
                text_results: textResults,
                combined_results: combinedResults,
                search_type: 'hybrid',
                ...context.metadata
            }
        });
    }
    /**
     * Log customer interaction
     */
    customerInteraction(customerId, interactionType, success, context = {}) {
        this.info('Customer interaction logged', {
            ...context,
            customer_id: customerId,
            operation: 'customer_interaction',
            metadata: {
                interaction_type: interactionType,
                success,
                ...context.metadata
            }
        });
    }
    /**
     * Log change stream event
     */
    changeStreamEvent(collection, operationType, documentId, context = {}) {
        this.info('Change stream event processed', {
            ...context,
            operation: 'change_stream',
            metadata: {
                collection,
                operation_type: operationType,
                document_id: documentId,
                ...context.metadata
            }
        });
    }
    /**
     * Create a child logger with additional context
     */
    child(context) {
        const childLogger = new StructuredLogger();
        childLogger.logger = this.logger.child(context);
        childLogger.defaultContext = { ...this.defaultContext, ...context };
        return childLogger;
    }
    /**
     * Measure and log execution time
     */
    async measureTime(operation, fn, context = {}) {
        const startTime = Date.now();
        const traceId = this.getTraceId();
        this.debug(`Starting ${operation}`, {
            ...context,
            trace_id: traceId,
            operation
        });
        try {
            const result = await fn();
            const duration = Date.now() - startTime;
            this.info(`Completed ${operation}`, {
                ...context,
                trace_id: traceId,
                operation,
                duration_ms: duration,
                metadata: {
                    ...context.metadata,
                    success: true
                }
            });
            return result;
        }
        catch (error) {
            const duration = Date.now() - startTime;
            this.error(`Failed ${operation}`, {
                ...context,
                trace_id: traceId,
                operation,
                duration_ms: duration,
                metadata: {
                    ...context.metadata,
                    success: false
                }
            }, error);
            throw error;
        }
    }
    /**
     * Create a performance timer
     */
    startTimer(operation, context = {}) {
        const startTime = Date.now();
        const traceId = this.getTraceId();
        this.debug(`Timer started for ${operation}`, {
            ...context,
            trace_id: traceId,
            operation
        });
        return () => {
            const duration = Date.now() - startTime;
            this.info(`Timer completed for ${operation}`, {
                ...context,
                trace_id: traceId,
                operation,
                duration_ms: duration
            });
        };
    }
}
exports.StructuredLogger = StructuredLogger;
// Global logger instance
exports.logger = new StructuredLogger({
    level: process.env.LOG_LEVEL || LogLevel.INFO,
    service_name: process.env.SERVICE_NAME || 'mongodb-ai-agent',
    environment: process.env.NODE_ENV || 'development',
    version: process.env.npm_package_version || '1.0.0'
});
exports.default = exports.logger;
