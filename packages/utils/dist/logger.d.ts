export declare enum LogLevel {
    TRACE = "trace",
    DEBUG = "debug",
    INFO = "info",
    WARN = "warn",
    ERROR = "error",
    FATAL = "fatal"
}
export interface LogContext {
    trace_id?: string;
    workflow_id?: string;
    agent_id?: string;
    session_id?: string;
    tool_id?: string;
    step_id?: string;
    customer_id?: string;
    operation?: string;
    duration_ms?: number;
    cost_usd?: number;
    tokens_used?: number;
    confidence_score?: number;
    error_code?: string;
    metadata?: Record<string, any>;
}
export interface MongoOperationMetrics {
    operation_type: 'find' | 'insert' | 'update' | 'delete' | 'aggregate' | 'vectorSearch';
    collection: string;
    duration_ms: number;
    documents_examined?: number;
    documents_returned?: number;
    index_used?: string;
    execution_stats?: Record<string, any>;
}
export interface AgentMetrics {
    agent_id: string;
    operation: string;
    success: boolean;
    duration_ms: number;
    cost_usd?: number;
    tokens_used?: number;
    confidence_score?: number;
    error_message?: string;
}
declare class StructuredLogger {
    private logger;
    private defaultContext;
    constructor(options?: {
        level?: LogLevel;
        service_name?: string;
        environment?: string;
        version?: string;
    });
    /**
     * Sanitize log object for MongoDB storage (remove dots from keys)
     */
    private sanitizeForMongoDB;
    /**
     * Set default context for all subsequent logs
     */
    setContext(context: Partial<LogContext>): void;
    /**
     * Get current trace ID
     */
    getTraceId(): string;
    /**
     * Create a new trace ID
     */
    newTrace(): string;
    /**
     * Log with structured context
     */
    private logWithContext;
    /**
     * Log trace level message
     */
    trace(message: string, context?: LogContext): void;
    /**
     * Log debug level message
     */
    debug(message: string, context?: LogContext): void;
    /**
     * Log info level message
     */
    info(message: string, context?: LogContext): void;
    /**
     * Log warning level message
     */
    warn(message: string, context?: LogContext, error?: Error): void;
    /**
     * Log error level message
     */
    error(message: string, context?: LogContext, error?: Error): void;
    /**
     * Log fatal level message
     */
    fatal(message: string, context?: LogContext, error?: Error): void;
    /**
     * Log MongoDB operation metrics
     */
    mongoOperation(metrics: MongoOperationMetrics, context?: LogContext): void;
    /**
     * Log agent performance metrics
     */
    agentMetrics(metrics: AgentMetrics, context?: LogContext): void;
    /**
     * Log workflow step execution
     */
    workflowStep(stepId: string, status: 'started' | 'completed' | 'failed', context?: LogContext, error?: Error): void;
    /**
     * Log tool execution
     */
    toolExecution(toolId: string, status: 'started' | 'completed' | 'failed', context?: LogContext, error?: Error): void;
    /**
     * Log vector search operation
     */
    vectorSearch(query: string, resultsCount: number, duration: number, context?: LogContext): void;
    /**
     * Log hybrid search operation
     */
    hybridSearch(query: string, vectorResults: number, textResults: number, combinedResults: number, duration: number, context?: LogContext): void;
    /**
     * Log customer interaction
     */
    customerInteraction(customerId: string, interactionType: string, success: boolean, context?: LogContext): void;
    /**
     * Log change stream event
     */
    changeStreamEvent(collection: string, operationType: string, documentId: string, context?: LogContext): void;
    /**
     * Create a child logger with additional context
     */
    child(context: LogContext): StructuredLogger;
    /**
     * Measure and log execution time
     */
    measureTime<T>(operation: string, fn: () => Promise<T>, context?: LogContext): Promise<T>;
    /**
     * Create a performance timer
     */
    startTimer(operation: string, context?: LogContext): () => void;
}
export declare const logger: StructuredLogger;
export { StructuredLogger };
export default logger;
//# sourceMappingURL=logger.d.ts.map