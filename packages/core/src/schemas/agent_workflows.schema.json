{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent Workflows", "description": "Schema for the 'agent_workflows' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "workflow_id": {"type": "string"}, "workflow_name": {"type": "string"}, "status": {"type": "string", "enum": ["pending", "in_progress", "completed", "failed", "cancelled"]}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "workflow_definition": {"type": "object"}, "current_step": {"type": "number"}, "execution_log": {"type": "array", "items": {"type": "object"}}, "shared_context": {"type": "object"}, "error_log": {"type": "array", "items": {"type": "object"}}, "retry_attempts": {"type": "number"}, "max_retries": {"type": "number"}}, "required": ["workflow_id", "workflow_name", "status", "created_at", "updated_at", "workflow_definition"]}