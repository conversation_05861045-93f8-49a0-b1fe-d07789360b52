{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent Events", "description": "Schema for the 'agent_events' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "event_id": {"type": "string"}, "agent_id": {"type": "string"}, "event_type": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "data": {"type": "object"}}, "required": ["event_id", "agent_id", "event_type", "timestamp", "data"]}