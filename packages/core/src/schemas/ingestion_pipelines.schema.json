{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Ingestion Pipelines", "description": "Schema for the 'ingestion_pipelines' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "pipeline_id": {"type": "string"}, "source_type": {"type": "string"}, "source_config": {"type": "object"}, "trigger": {"type": "string", "enum": ["on_new_file", "scheduled"]}, "processing_steps": {"type": "array", "items": {"type": "object"}}, "status": {"type": "string", "enum": ["active", "paused"]}, "last_run": {"type": "object"}}, "required": ["pipeline_id", "source_type", "source_config", "trigger", "processing_steps", "status"]}