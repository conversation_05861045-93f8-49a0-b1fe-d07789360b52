{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Dynamic Plans", "description": "Schema for the 'dynamic_plans' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "plan_id": {"type": "string"}, "agent_id": {"type": "string"}, "goal": {"type": "string"}, "status": {"type": "string", "enum": ["planning", "ready_for_execution", "executing", "completed", "failed"]}, "plan": {"type": "array", "items": {"type": "object"}}, "validation_critera": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}, "required": ["plan_id", "agent_id", "goal", "status", "plan", "created_at"]}