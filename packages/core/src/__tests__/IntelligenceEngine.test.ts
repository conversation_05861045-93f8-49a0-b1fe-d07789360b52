/**
 * @file IntelligenceEngine.test.ts - Comprehensive tests for the Universal AI Brain Intelligence Engine
 * 
 * Tests the core intelligence features including context injection, prompt enhancement,
 * semantic memory, and real-time learning capabilities.
 */

import { setupTestDb, teardownTestDb } from './setup';
import { UniversalAIBrain, BrainConfig } from '../brain/UniversalAIBrain';

// Mock OpenAI API for testing
jest.mock('../embeddings/OpenAIEmbeddingProvider', () => {
  return {
    OpenAIEmbeddingProvider: jest.fn().mockImplementation(() => ({
      generateEmbedding: jest.fn().mockResolvedValue(
        Array(1536).fill(0).map(() => Math.random() - 0.5)
      ),
      getModel: jest.fn().mockReturnValue('text-embedding-ada-002'),
      getDimensions: jest.fn().mockReturnValue(1536),
      vectorSearchWithMetadata: jest.fn().mockResolvedValue([])
    }))
  };
});

describe('Intelligence Engine Core', () => {
  let db: any;
  let brain: UniversalAIBrain;
  let testConfig: BrainConfig;

  beforeAll(async () => {
    db = await setupTestDb();
    
    testConfig = {
      mongoConfig: {
        uri: 'mongodb://localhost:27017', // Will be overridden by test setup
        dbName: 'test_intelligence_db'
      },
      embeddingConfig: {
        provider: 'openai',
        model: 'text-embedding-ada-002',
        apiKey: 'test-api-key',
        dimensions: 1536
      },
      vectorSearchConfig: {
        indexName: 'test_vector_index',
        collectionName: 'test_vector_collection',
        minScore: 0.7
      }
    };

    brain = new UniversalAIBrain(testConfig);
    
    // Override the MongoDB connection to use test database
    (brain as any).mongoConnection = {
      connect: jest.fn().mockResolvedValue(undefined),
      getDb: jest.fn().mockReturnValue(db),
      healthCheck: jest.fn().mockResolvedValue(true),
      getInstance: jest.fn().mockReturnThis()
    };
  }, 60000);

  afterAll(async () => {
    await teardownTestDb();
  }, 30000);

  beforeEach(async () => {
    // Clear all collections before each test
    await Promise.all([
      db.collection('conversations').deleteMany({}),
      db.collection('interactions').deleteMany({}),
      db.collection('test_vector_collection').deleteMany({}),
      db.collection('agents').deleteMany({}),
      db.collection('agent_memory').deleteMany({}),
      db.collection('agent_workflows').deleteMany({}),
      db.collection('agent_tools').deleteMany({}),
      db.collection('agent_performance_metrics').deleteMany({})
    ]);
  });

  describe('Brain Initialization', () => {
    it('should initialize the Universal AI Brain successfully', async () => {
      await brain.initialize();
      
      expect(brain.vectorStore).toBeDefined();
      expect(brain.collections).toBeDefined();
      
      const stats = await brain.getStats();
      expect(stats.isHealthy).toBe(true);
      expect(stats.embeddingProvider.model).toBe('text-embedding-ada-002');
      expect(stats.embeddingProvider.dimensions).toBe(1536);
    });

    it('should handle multiple initialization calls gracefully', async () => {
      await brain.initialize();
      await brain.initialize(); // Should not throw or reinitialize
      
      const stats = await brain.getStats();
      expect(stats.isHealthy).toBe(true);
    });
  });

  describe('Context Injection and Prompt Enhancement', () => {
    beforeEach(async () => {
      await brain.initialize();
    });

    it('should enhance prompts with semantic context', async () => {
      // Store some knowledge in the brain
      await brain.vectorStore.storeDocument(
        'MongoDB Atlas Vector Search enables semantic similarity matching for AI applications',
        { topic: 'mongodb', category: 'database' },
        'knowledge_base'
      );

      await brain.vectorStore.storeDocument(
        'TypeScript frameworks like Vercel AI, Mastra, and LangChain.js provide different approaches to building AI applications',
        { topic: 'frameworks', category: 'development' },
        'knowledge_base'
      );

      // Test semantic enhancement
      const enhanced = await brain.enhancePrompt(
        'How can I use MongoDB for AI applications?',
        {
          frameworkType: 'vercel-ai',
          enhancementStrategy: 'semantic',
          maxContextItems: 3
        }
      );

      expect(enhanced.originalPrompt).toBe('How can I use MongoDB for AI applications?');
      expect(enhanced.enhancedPrompt).toContain('MongoDB');
      expect(enhanced.enhancedPrompt).toContain('USER QUERY:');
      expect(enhanced.metadata.frameworkType).toBe('vercel-ai');
      expect(enhanced.metadata.enhancementStrategy).toBe('semantic');
    });

    it('should handle hybrid context enhancement', async () => {
      await brain.initialize();

      const enhanced = await brain.enhancePrompt(
        'What are the best practices for AI development?',
        {
          frameworkType: 'mastra',
          enhancementStrategy: 'hybrid',
          maxContextItems: 5
        }
      );

      expect(enhanced.originalPrompt).toBe('What are the best practices for AI development?');
      expect(enhanced.metadata.enhancementStrategy).toBe('hybrid');
      expect(enhanced.metadata.frameworkType).toBe('mastra');
    });

    it('should provide conversational context enhancement', async () => {
      const conversationId = 'test-conversation-123';

      // Store some conversation history
      await brain.storeInteraction({
        conversationId,
        userMessage: 'I want to build an AI chatbot',
        assistantResponse: 'I can help you build an AI chatbot. What framework would you like to use?',
        context: [],
        metadata: { topic: 'chatbot' },
        framework: 'vercel-ai'
      });

      // Test conversational enhancement
      const enhanced = await brain.enhancePrompt(
        'How do I add memory to my chatbot?',
        {
          frameworkType: 'vercel-ai',
          conversationId,
          enhancementStrategy: 'conversational',
          maxContextItems: 5
        }
      );

      expect(enhanced.originalPrompt).toBe('How do I add memory to my chatbot?');
      expect(enhanced.metadata.enhancementStrategy).toBe('conversational');
      expect(enhanced.injectedContext.length).toBeGreaterThanOrEqual(0);
    });

    it('should handle enhancement failures gracefully', async () => {
      // Mock a failure in the embedding provider
      const originalGenerateEmbedding = brain['embeddingProvider'].generateEmbedding;
      brain['embeddingProvider'].generateEmbedding = jest.fn().mockRejectedValue(new Error('API Error'));

      const enhanced = await brain.enhancePrompt('Test prompt');

      expect(enhanced.originalPrompt).toBe('Test prompt');
      expect(enhanced.enhancedPrompt).toBe('Test prompt'); // Should fallback to original
      expect(enhanced.metadata.enhancementStrategy).toBe('fallback');
      expect(enhanced.injectedContext).toHaveLength(0);

      // Restore original function
      brain['embeddingProvider'].generateEmbedding = originalGenerateEmbedding;
    });
  });

  describe('Memory and Learning', () => {
    beforeEach(async () => {
      await brain.initialize();
    });

    it('should store and retrieve interactions for learning', async () => {
      const interaction = {
        conversationId: 'learning-test-123',
        userMessage: 'What is machine learning?',
        assistantResponse: 'Machine learning is a subset of AI that enables computers to learn from data.',
        context: [],
        metadata: { topic: 'ml', difficulty: 'beginner' },
        framework: 'langchain'
      };

      const interactionId = await brain.storeInteraction(interaction);
      expect(interactionId).toBeDefined();
      expect(typeof interactionId).toBe('string');

      // Verify interaction was stored
      const storedInteraction = await db.collection('interactions').findOne({ id: interactionId });
      expect(storedInteraction).toBeDefined();
      expect(storedInteraction.userMessage).toBe(interaction.userMessage);
      expect(storedInteraction.framework).toBe('langchain');
    });

    it('should retrieve relevant context based on queries', async () => {
      await brain.initialize();

      // Store some knowledge
      await brain.vectorStore.storeDocument(
        'Artificial Intelligence is transforming software development',
        { category: 'ai', importance: 'high' },
        'knowledge_base'
      );

      const context = await brain.retrieveRelevantContext(
        'How is AI changing development?',
        {
          limit: 5,
          minRelevanceScore: 0.5
        }
      );

      expect(Array.isArray(context)).toBe(true);
      // Context might be empty due to mocked vector search, but structure should be correct
      context.forEach(item => {
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('content');
        expect(item).toHaveProperty('metadata');
        expect(item).toHaveProperty('relevanceScore');
        expect(item).toHaveProperty('source');
        expect(item).toHaveProperty('timestamp');
      });
    });

    it('should handle context retrieval errors gracefully', async () => {
      // Mock vector store to throw error
      const originalVectorSearch = brain.vectorStore.vectorSearch;
      brain.vectorStore.vectorSearch = jest.fn().mockRejectedValue(new Error('Vector search failed'));

      const context = await brain.retrieveRelevantContext('test query');
      expect(context).toEqual([]);

      // Restore original function
      brain.vectorStore.vectorSearch = originalVectorSearch;
    });
  });

  describe('Framework Integration Support', () => {
    beforeEach(async () => {
      await brain.initialize();
    });

    it('should provide framework-specific prompt instructions', async () => {
      const frameworks = ['mastra', 'vercel-ai', 'langchain', 'openai-agents', 'unknown'];

      for (const framework of frameworks) {
        const enhanced = await brain.enhancePrompt('Test prompt', {
          frameworkType: framework
        });

        // Check that framework-specific instructions are included
        expect(enhanced.enhancedPrompt).toContain('You are an');
        expect(enhanced.enhancedPrompt).toContain('USER QUERY:');
        expect(enhanced.enhancedPrompt).toContain('Test prompt');
        expect(enhanced.metadata.frameworkType).toBe(framework);
      }
    });

    it('should support framework adapter integration', async () => {
      const mockAdapter = {
        frameworkName: 'test-framework',
        integrate: jest.fn().mockResolvedValue({ enhanced: true }),
        enhanceWithBrain: jest.fn()
      };

      const result = await brain.integrateWithFramework(mockAdapter);
      
      expect(mockAdapter.integrate).toHaveBeenCalledWith(brain);
      expect(result).toEqual({ enhanced: true });
    });
  });

  describe('Performance and Health Monitoring', () => {
    beforeEach(async () => {
      await brain.initialize();
    });

    it('should provide comprehensive brain statistics', async () => {
      const stats = await brain.getStats();

      expect(stats).toHaveProperty('isHealthy');
      expect(stats).toHaveProperty('collections');
      expect(stats).toHaveProperty('embeddingProvider');
      expect(stats).toHaveProperty('lastUpdated');

      expect(stats.collections).toHaveProperty('vectorStore');
      expect(stats.collections).toHaveProperty('interactions');
      expect(stats.collections).toHaveProperty('conversations');

      expect(stats.embeddingProvider).toHaveProperty('model');
      expect(stats.embeddingProvider).toHaveProperty('dimensions');
    });

    it('should handle stats retrieval errors gracefully', async () => {
      // Mock health check to fail
      brain['mongoConnection'].healthCheck = jest.fn().mockRejectedValue(new Error('Health check failed'));

      const stats = await brain.getStats();
      expect(stats.isHealthy).toBe(false);
      expect(stats.error).toBeDefined();
    });
  });

  describe('Real-time Learning and Adaptation', () => {
    beforeEach(async () => {
      await brain.initialize();
    });

    it('should learn from interactions and improve context relevance', async () => {
      const conversationId = 'learning-conversation-456';

      // Simulate a learning sequence
      const interactions = [
        {
          conversationId,
          userMessage: 'I need help with MongoDB vector search',
          assistantResponse: 'MongoDB Atlas Vector Search allows you to perform semantic similarity searches.',
          context: [],
          metadata: { topic: 'mongodb', subtopic: 'vector-search' },
          framework: 'vercel-ai'
        },
        {
          conversationId,
          userMessage: 'How do I create vector indexes?',
          assistantResponse: 'You can create vector indexes using the createSearchIndex method in MongoDB Atlas.',
          context: [],
          metadata: { topic: 'mongodb', subtopic: 'indexing' },
          framework: 'vercel-ai'
        }
      ];

      // Store interactions
      for (const interaction of interactions) {
        await brain.storeInteraction(interaction);
      }

      // Test that subsequent queries benefit from learned context
      const enhanced = await brain.enhancePrompt(
        'What are the performance considerations for vector search?',
        {
          frameworkType: 'vercel-ai',
          conversationId,
          enhancementStrategy: 'conversational',
          maxContextItems: 5
        }
      );

      expect(enhanced.originalPrompt).toContain('performance considerations');
      expect(enhanced.metadata.enhancementStrategy).toBe('conversational');
      // The enhanced prompt should include conversational context
      expect(enhanced.injectedContext.length).toBeGreaterThanOrEqual(0);
    });
  });
});
