import { MongoConnection } from '../persistance/MongoConnection';
import { MongoDataStore } from '../persistance/MongoDataStore';
import { MongoMemoryProvider } from '../persistance/MongoMemoryProvider';
import { MongoEmbeddingProvider } from '../persistance/MongoEmbeddingProvider';
import { AgentState } from '../agent/AgentStateManager';

// This test requires a real MongoDB connection
// Set MONGODB_URI environment variable to run
describe('Integration Tests', () => {
  const mongoUri = process.env.MONGODB_URI;
  
  // Skip integration tests if no MongoDB URI is provided
  const describeOrSkip = mongoUri ? describe : describe.skip;

  describeOrSkip('MongoDB Atlas Integration', () => {
    let connection: MongoConnection;

    beforeAll(async () => {
      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable is required for integration tests');
      }
      
      connection = MongoConnection.getInstance(mongoUri, 'test_ai_agents');
      await connection.connect();
    });

    afterAll(async () => {
      if (connection) {
        await connection.disconnect();
      }
    });

    it('should connect to MongoDB Atlas', async () => {
      const db = connection.getDb();
      expect(db).toBeDefined();
      
      // Test basic database operation
      const result = await db.admin().ping();
      expect(result.ok).toBe(1);
    });

    it('should create and query collections', async () => {
      const db = connection.getDb();
      const dataStore = new MongoDataStore<AgentState>(db, 'test_agents');
      
      const testAgent = {
        agent_id: 'integration-test-agent',
        name: 'Integration Test Agent',
        version: '1.0.0',
        status: 'active' as const,
        model_config: {
          provider: 'openai',
          model: 'gpt-4'
        }
      };

      // Create agent
      const created = await dataStore.create(testAgent);
      expect(created._id).toBeDefined();

      // Find agent
      const found = await dataStore.read(created._id!.toString());
      expect(found).toBeTruthy();
      expect(found!.agent_id).toBe('integration-test-agent');

      // Clean up
      await dataStore.delete(created._id!.toString());
    });

    it('should handle working memory operations', async () => {
      const db = connection.getDb();
      const memoryProvider = new MongoMemoryProvider(db, 1);
      
      const agentId = 'test-agent';
      const sessionId = 'test-session';
      
      // Add message
      await memoryProvider.addMessage(agentId, sessionId, {
        role: 'user',
        content: 'Hello integration test',
        timestamp: new Date()
      });

      // Get history
      const history = await memoryProvider.getHistory(agentId, sessionId);
      expect(history).toHaveLength(1);
      expect(history[0].content).toBe('Hello integration test');

      // Clean up
      await memoryProvider.clearSession(agentId, sessionId);
    });

    it('should handle vector operations (if indexes exist)', async () => {
      const db = connection.getDb();
      const embeddingProvider = new MongoEmbeddingProvider(db, 'test_vectors', 'vector_search_index');
      
      const testVector = {
        id: 'integration-test-vector',
        values: Array(1024).fill(0).map(() => Math.random()),
        metadata: {
          source_type: 'test',
          source_id: 'integration-test',
          text: 'This is a test vector for integration testing',
          provider: 'test',
          model: 'test-model',
          version: '1.0'
        }
      };

      try {
        // This will only work if vector search indexes are set up
        await embeddingProvider.add({ document: testVector.metadata, embedding: { values: testVector.values, model: 'test-model' } });
        
        // Try to query (this might fail if indexes don't exist)
        const results = await embeddingProvider.findSimilar(testVector.values, { k: 5 });
        expect(Array.isArray(results)).toBe(true);
        
        console.log('✅ Vector search integration test passed');
      } catch (error) {
        console.log('⚠️ Vector search test skipped - indexes may not be configured:', (error as Error).message);
        // This is expected if vector search indexes aren't set up yet
      }
    });
  });
});