/**
 * @file UniversalAIBrain - The core orchestrator for MongoDB-powered AI intelligence
 * 
 * This is the heart of your vision: a universal MongoDB-powered intelligence layer
 * that ANY TypeScript framework can integrate with to get superpowers.
 * 
 * Key Features:
 * - MongoDB Atlas Vector Search for semantic memory
 * - Intelligent context injection and prompt enhancement
 * - Framework-agnostic adapter system
 * - Persistent conversation memory
 * - Knowledge graph capabilities
 */

import { Db, Collection, ObjectId } from 'mongodb';
import { MongoConnection } from '../persistance/MongoConnection';
import { MongoVectorStore, VectorSearchResult, EmbeddingProvider } from '../vector/MongoVectorStore';
import { OpenAIEmbeddingProvider } from '../embeddings/OpenAIEmbeddingProvider';
import { CollectionManager } from '../collections/index';

// Core interfaces for the Universal AI Brain
export interface BrainConfig {
  mongoConfig: {
    uri: string;
    dbName: string;
  };
  embeddingConfig: {
    provider: 'openai' | 'cohere' | 'huggingface';
    model: string;
    apiKey: string;
    dimensions: number;
  };
  vectorSearchConfig: {
    indexName: string;
    collectionName: string;
    minScore: number;
  };
}

export interface Context {
  id: string;
  content: string;
  metadata: Record<string, any>;
  relevanceScore: number;
  source: string;
  timestamp: Date;
}

export interface EnhancedPrompt {
  originalPrompt: string;
  enhancedPrompt: string;
  injectedContext: Context[];
  metadata: {
    frameworkType: string;
    enhancementStrategy: string;
    contextSources: string[];
  };
}

export interface Interaction {
  id: string;
  conversationId: string;
  userMessage: string;
  assistantResponse: string;
  context: Context[];
  metadata: Record<string, any>;
  timestamp: Date;
  framework: string;
}

export interface FrameworkAdapter<T> {
  frameworkName: string;
  integrate(brain: UniversalAIBrain): T;
  enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
}

export interface MemorySearchOptions {
  limit?: number;
  timeRange?: {
    start: Date;
    end: Date;
  };
  conversationId?: string;
  framework?: string;
  minRelevanceScore?: number;
}

/**
 * UniversalAIBrain - The core MongoDB-powered intelligence orchestrator
 * 
 * This class provides the central intelligence layer that any framework can integrate with.
 * It handles semantic memory, context injection, and intelligent prompt enhancement.
 */
export class UniversalAIBrain {
  private db: Db;
  private mongoConnection: MongoConnection;
  public vectorStore: MongoVectorStore;
  private embeddingProvider: EmbeddingProvider;
  public collections: CollectionManager;
  private conversationsCollection: Collection;
  private interactionsCollection: Collection;
  private config: BrainConfig;
  private isInitialized: boolean = false;
  private agentId: ObjectId;

  constructor(config: BrainConfig) {
    this.config = config;
    this.mongoConnection = MongoConnection.getInstance({
      uri: config.mongoConfig.uri,
      dbName: config.mongoConfig.dbName
    });
    this.agentId = new ObjectId(); // Generate unique agent ID
  }

  /**
   * Initialize the Universal AI Brain
   * Sets up MongoDB connections, collections, and vector search
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Connect to MongoDB Atlas
      await this.mongoConnection.connect();
      this.db = this.mongoConnection.getDb();

      // Initialize Collection Manager - The data layer that powers everything! 💾
      this.collections = new CollectionManager(this.db);
      await this.collections.initialize();

      // Initialize legacy collections (for backward compatibility)
      this.conversationsCollection = this.db.collection('conversations');
      this.interactionsCollection = this.db.collection('interactions');

      // Initialize embedding provider
      this.embeddingProvider = new OpenAIEmbeddingProvider({
        apiKey: this.config.embeddingConfig.apiKey,
        model: this.config.embeddingConfig.model
      });

      // Initialize vector store
      this.vectorStore = new MongoVectorStore(
        this.mongoConnection,
        this.config.vectorSearchConfig.collectionName,
        this.config.vectorSearchConfig.indexName
      );

      await this.vectorStore.initialize(this.embeddingProvider);

      // Ensure indexes exist
      await this.ensureIndexes();

      this.isInitialized = true;
      console.log('🧠 Universal AI Brain initialized successfully!');
    } catch (error) {
      console.error('❌ Failed to initialize Universal AI Brain:', error);
      throw error;
    }
  }

  /**
   * Enhance a prompt with relevant context from MongoDB
   * This is the core intelligence feature that frameworks will use
   */
  async enhancePrompt(
    prompt: string, 
    options?: {
      frameworkType?: string;
      conversationId?: string;
      maxContextItems?: number;
      enhancementStrategy?: 'semantic' | 'hybrid' | 'conversational';
    }
  ): Promise<EnhancedPrompt> {
    await this.ensureInitialized();

    const {
      frameworkType = 'unknown',
      conversationId,
      maxContextItems = 5,
      enhancementStrategy = 'hybrid'
    } = options || {};

    try {
      // Generate embedding for the prompt
      const promptEmbedding = await this.generateEmbedding(prompt);

      // Retrieve relevant context based on strategy
      let relevantContext: Context[] = [];

      switch (enhancementStrategy) {
        case 'semantic':
          relevantContext = await this.getSemanticContext(promptEmbedding, maxContextItems);
          break;
        case 'hybrid':
          relevantContext = await this.getHybridContext(prompt, promptEmbedding, maxContextItems);
          break;
        case 'conversational':
          relevantContext = await this.getConversationalContext(
            prompt, 
            promptEmbedding, 
            conversationId, 
            maxContextItems
          );
          break;
      }

      // Build enhanced prompt
      const enhancedPrompt = this.buildEnhancedPrompt(prompt, relevantContext, frameworkType);

      return {
        originalPrompt: prompt,
        enhancedPrompt,
        injectedContext: relevantContext,
        metadata: {
          frameworkType,
          enhancementStrategy,
          contextSources: relevantContext.map(c => c.source)
        }
      };
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      // Return original prompt if enhancement fails
      return {
        originalPrompt: prompt,
        enhancedPrompt: prompt,
        injectedContext: [],
        metadata: {
          frameworkType,
          enhancementStrategy: 'fallback',
          contextSources: []
        }
      };
    }
  }

  /**
   * Store an interaction for future context and learning
   */
  async storeInteraction(interaction: Omit<Interaction, 'id' | 'timestamp'>): Promise<string> {
    await this.ensureInitialized();

    const fullInteraction: Interaction = {
      ...interaction,
      id: new ObjectId().toString(),
      timestamp: new Date()
    };

    try {
      // Store the interaction
      await this.interactionsCollection.insertOne(fullInteraction);

      // Generate and store embeddings for future retrieval
      await this.storeInteractionEmbeddings(fullInteraction);

      return fullInteraction.id;
    } catch (error) {
      console.error('Error storing interaction:', error);
      throw error;
    }
  }

  /**
   * Retrieve relevant context based on a query
   */
  async retrieveRelevantContext(
    query: string, 
    options?: MemorySearchOptions
  ): Promise<Context[]> {
    await this.ensureInitialized();

    try {
      const queryEmbedding = await this.generateEmbedding(query);
      
      const searchOptions = {
        k: options?.limit || 10,
        filter: this.buildSearchFilter(options),
        minScore: options?.minRelevanceScore || this.config.vectorSearchConfig.minScore
      };

      const results = await this.embeddingProvider.vectorSearchWithMetadata(
        queryEmbedding, 
        searchOptions
      );

      return results.map(result => this.convertToContext(result));
    } catch (error) {
      console.error('Error retrieving context:', error);
      return [];
    }
  }

  /**
   * Integrate with a framework using an adapter
   */
  async integrateWithFramework<T>(adapter: FrameworkAdapter<T>): Promise<T> {
    await this.ensureInitialized();
    
    console.log(`🔌 Integrating Universal AI Brain with ${adapter.frameworkName}`);
    return adapter.integrate(this);
  }

  /**
   * Get the unique agent ID for this brain instance
   */
  getAgentId(): ObjectId {
    return this.agentId;
  }

  /**
   * Get brain statistics and health information
   */
  async getStats(): Promise<any> {
    await this.ensureInitialized();

    try {
      const [
        vectorStoreStats,
        interactionCount,
        conversationCount
      ] = await Promise.all([
        this.vectorStore.getStats(),
        this.interactionsCollection.countDocuments(),
        this.conversationsCollection.countDocuments()
      ]);

      return {
        isHealthy: await this.mongoConnection.healthCheck(),
        collections: {
          vectorStore: vectorStoreStats,
          interactions: interactionCount,
          conversations: conversationCount
        },
        embeddingProvider: {
          model: this.embeddingProvider.getModel(),
          dimensions: this.embeddingProvider.getDimensions()
        },
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('Error getting brain stats:', error);
      return { isHealthy: false, error: error.message };
    }
  }

  // Private helper methods will be added in the next part...
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  private async ensureIndexes(): Promise<void> {
    try {
      // Create indexes for conversations
      await this.conversationsCollection.createIndex({ id: 1 }, { unique: true });
      await this.conversationsCollection.createIndex({ framework: 1 });
      await this.conversationsCollection.createIndex({ createdAt: -1 });

      // Create indexes for interactions
      await this.interactionsCollection.createIndex({ conversationId: 1 });
      await this.interactionsCollection.createIndex({ framework: 1 });
      await this.interactionsCollection.createIndex({ timestamp: -1 });

      // Create text search index for hybrid search on vector store collection
      const vectorCollection = this.vectorStore.getCollection();
      await vectorCollection.createIndex(
        {
          "text": "text",
          "metadata.content": "text"
        },
        { name: "text_index" }
      );

      console.log('✅ MongoDB indexes created successfully');
    } catch (error) {
      console.warn('⚠️ Some indexes may already exist:', error.message);
    }
  }

  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      return await this.embeddingProvider.generateEmbedding(text);
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error.message}`);
    }
  }

  private async getSemanticContext(embedding: number[], limit: number): Promise<Context[]> {
    try {
      const results = await this.vectorStore.vectorSearch(embedding, {
        limit,
        minScore: this.config.vectorSearchConfig.minScore,
        includeEmbeddings: false
      });

      return results.map(result => this.convertToContext(result));
    } catch (error) {
      console.error('Error in semantic context retrieval:', error);
      return [];
    }
  }

  private async getHybridContext(text: string, embedding: number[], limit: number): Promise<Context[]> {
    try {
      const results = await this.vectorStore.hybridSearch(text, {
        limit,
        minScore: this.config.vectorSearchConfig.minScore,
        includeEmbeddings: false
      });

      return results.map(result => this.convertToContext(result));
    } catch (error) {
      console.error('Error in hybrid context retrieval:', error);
      // Fallback to semantic search
      return this.getSemanticContext(embedding, limit);
    }
  }

  private async getConversationalContext(
    text: string,
    embedding: number[],
    conversationId?: string,
    limit: number = 5
  ): Promise<Context[]> {
    try {
      // First get recent conversation history if conversationId provided
      let conversationContext: Context[] = [];

      if (conversationId) {
        const recentInteractions = await this.interactionsCollection
          .find({ conversationId })
          .sort({ timestamp: -1 })
          .limit(3)
          .toArray();

        conversationContext = recentInteractions.map(interaction => ({
          id: interaction.id,
          content: `User: ${interaction.userMessage}\nAssistant: ${interaction.assistantResponse}`,
          metadata: { type: 'conversation_history', ...interaction.metadata },
          relevanceScore: 0.9, // High relevance for conversation history
          source: 'conversation_memory',
          timestamp: interaction.timestamp
        }));
      }

      // Then get semantic context
      const semanticContext = await this.getHybridContext(text, embedding, limit - conversationContext.length);

      // Combine and return
      return [...conversationContext, ...semanticContext].slice(0, limit);
    } catch (error) {
      console.error('Error in conversational context retrieval:', error);
      return this.getSemanticContext(embedding, limit);
    }
  }

  private buildEnhancedPrompt(prompt: string, context: Context[], frameworkType: string): string {
    // Framework-specific prompt enhancement
    const frameworkInstructions = this.getFrameworkInstructions(frameworkType);

    // If no context, still provide framework instructions
    if (context.length === 0) {
      return `${frameworkInstructions}

USER QUERY:
${prompt}

Please provide a helpful response.`;
    }

    // Build context section
    const contextSection = context
      .map((ctx, index) => {
        return `Context ${index + 1} (${ctx.source}, relevance: ${ctx.relevanceScore.toFixed(2)}):\n${ctx.content}`;
      })
      .join('\n\n');

    return `${frameworkInstructions}

RELEVANT CONTEXT:
${contextSection}

USER QUERY:
${prompt}

Please provide a helpful response based on the context above. If the context doesn't contain relevant information, say so and provide the best answer you can.`;
  }

  private getFrameworkInstructions(frameworkType: string): string {
    const instructions = {
      'mastra': 'You are an AI assistant integrated with Mastra framework. Use the provided context to enhance your responses with relevant information from the knowledge base.',
      'vercel-ai': 'You are an AI assistant enhanced with Vercel AI SDK. Leverage the context to provide accurate, contextual responses.',
      'langchain': 'You are an AI assistant powered by LangChain.js. Use the retrieved context to provide comprehensive, well-informed responses.',
      'openai-agents': 'You are an OpenAI agent with access to a knowledge base. Use the provided context to give accurate, helpful responses.',
      'unknown': 'You are an AI assistant with access to a knowledge base. Use the provided context to enhance your responses.'
    };

    return instructions[frameworkType] || instructions['unknown'];
  }

  private async storeInteractionEmbeddings(interaction: Interaction): Promise<void> {
    try {
      // Store user message and assistant response as separate documents
      await Promise.all([
        this.vectorStore.storeDocument(
          interaction.userMessage,
          {
            type: 'user_message',
            conversationId: interaction.conversationId,
            framework: interaction.framework,
            ...interaction.metadata
          },
          'conversation_memory'
        ),
        this.vectorStore.storeDocument(
          interaction.assistantResponse,
          {
            type: 'assistant_response',
            conversationId: interaction.conversationId,
            framework: interaction.framework,
            ...interaction.metadata
          },
          'conversation_memory'
        )
      ]);
    } catch (error) {
      console.error('Error storing interaction embeddings:', error);
      // Don't throw - this is not critical for the main flow
    }
  }

  private buildSearchFilter(options?: MemorySearchOptions): any {
    const filter: any = {};

    if (options?.conversationId) {
      filter['document.conversationId'] = options.conversationId;
    }

    if (options?.framework) {
      filter['document.framework'] = options.framework;
    }

    if (options?.timeRange) {
      filter['document.timestamp'] = {
        $gte: options.timeRange.start,
        $lte: options.timeRange.end
      };
    }

    return filter;
  }

  private convertToContext(result: VectorSearchResult): Context {
    return {
      id: result._id?.toString() || new ObjectId().toString(),
      content: result.text || JSON.stringify(result),
      metadata: result.metadata || {},
      relevanceScore: result.score,
      source: result.source || 'knowledge_base',
      timestamp: result.timestamp || new Date()
    };
  }
}
