// ============================================================================
// UNIVERSAL AI BRAIN - CORE EXPORTS
// ============================================================================

// Core Universal AI Brain - The heart of your vision! 🧠⚡
export { UniversalAIBrain } from './brain/UniversalAIBrain';
export { UniversalAIBrain as UniversalAIBrainV2 } from './UniversalAIBrain';
export type { UniversalAIBrainConfig, AIBrainResponse } from './UniversalAIBrain';

// Framework adapters - The magic that connects ANY framework to MongoDB! 🔌
export { BaseFrameworkAdapter } from './adapters/BaseFrameworkAdapter';
export { MastraAdapter } from './adapters/MastraAdapter';
export { VercelAIAdapter } from './adapters/VercelAIAdapter';
export { LangChainJSAdapter } from './adapters/LangChainJSAdapter';
export { OpenAIAgentsAdapter } from './adapters/OpenAIAgentsAdapter';

// Vector Search and Embeddings
export { MongoVectorStore } from './vector/MongoVectorStore';
export type { EmbeddingProvider as VectorEmbeddingProvider } from './vector/MongoVectorStore';
export { OpenAIEmbeddingProvider } from './embeddings/OpenAIEmbeddingProvider';

// Core types and interfaces
export * from './types';

// MongoDB persistence layer (enhanced with Vector Search)
export * from './persistance';

// MongoDB collections - The data layer that powers everything! 💾
export {
  BaseCollection,
  AgentCollection,
  MemoryCollection,
  WorkflowCollection,
  ToolCollection,
  MetricsCollection,
  TracingCollection,
  CollectionManager
} from './collections/index';

// MongoDB schemas
export * from './schemas';

// Enterprise tracing and observability - Production-grade monitoring! 🔍
export { TracingEngine } from './tracing/TracingEngine';
export { ChangeStreamManager } from './tracing/ChangeStreamManager';
export { RealTimeMonitor } from './tracing/RealTimeMonitor';
export type {
  AgentTrace,
  AgentStep,
  AgentError,
  PerformanceMetrics as TracingPerformanceMetrics,
  ContextItem,
  TokenUsage,
  CostBreakdown,
  FrameworkMetadata
} from './collections/TracingCollection';

// NEW: Universal AI Brain V2 Components - Production-Ready Intelligence Layer! 🚀

// Intelligence Layer
export { SemanticMemoryEngine } from './intelligence/SemanticMemoryEngine';
export { ContextInjectionEngine } from './intelligence/ContextInjectionEngine';
export { VectorSearchEngine } from './intelligence/VectorSearchEngine';

// Safety & Guardrails
export { SafetyGuardrailsEngine } from './safety/SafetyGuardrailsEngine';
export { SafetyGuardrailsEngine as SafetyEngine } from './safety/SafetyGuardrailsEngine'; // Alias for backward compatibility
export { HallucinationDetector } from './safety/HallucinationDetector';
export { PIIDetector } from './safety/PIIDetector';
export { ComplianceAuditLogger } from './safety/ComplianceAuditLogger';
export { FrameworkSafetyIntegration } from './safety/FrameworkSafetyIntegration';

// Self-Improvement Engines
export { FailureAnalysisEngine } from './self-improvement/FailureAnalysisEngine';
export { ContextLearningEngine } from './self-improvement/ContextLearningEngine';
export { FrameworkOptimizationEngine } from './self-improvement/FrameworkOptimizationEngine';
export { SelfImprovementMetrics } from './self-improvement/SelfImprovementMetrics';

// Real-Time Monitoring
export { PerformanceAnalyticsEngine } from './monitoring/PerformanceAnalyticsEngine';
export type { PerformanceMetrics as MonitoringPerformanceMetrics } from './monitoring/PerformanceAnalyticsEngine';
export { RealTimeMonitoringDashboard } from './monitoring/RealTimeMonitoringDashboard';

// Legacy exports (keeping for backward compatibility)
export * from './agent';
export { HybridSearchEngine, DefaultEmbeddingProvider } from './features/hybridSearch';
export type { EmbeddingProvider as HybridEmbeddingProvider } from './features/hybridSearch';
// Note: ./real-time exports ChangeStreamManager which conflicts with ./tracing
// Using explicit re-export to avoid conflict
export { ChangeStreamManager as RealTimeChangeStreamManager } from './real-time/ChangeStreamManager';