/**
 * @file OpenAIEmbeddingProvider - Production-ready OpenAI embedding implementation
 * 
 * This provides OpenAI embedding generation for the Universal AI Brain.
 * Supports both OpenAI and Azure OpenAI endpoints with proper error handling,
 * rate limiting, and batch processing.
 */

import { EmbeddingProvider } from '../vector/MongoVectorStore';

export interface OpenAIConfig {
  apiKey: string;
  model: string;
  baseUrl?: string; // For Azure OpenAI or custom endpoints
  maxRetries?: number;
  timeout?: number;
  batchSize?: number;
}

export interface EmbeddingResponse {
  object: string;
  data: Array<{
    object: string;
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

/**
 * OpenAIEmbeddingProvider - Production-ready OpenAI embedding implementation
 * 
 * Features:
 * - Support for OpenAI and Azure OpenAI
 * - Automatic retry with exponential backoff
 * - Batch processing for efficiency
 * - Rate limiting and error handling
 * - Token counting and cost tracking
 */
export class OpenAIEmbeddingProvider implements EmbeddingProvider {
  private config: Required<OpenAIConfig>;
  private requestCount: number = 0;
  private totalTokens: number = 0;

  constructor(config: OpenAIConfig) {
    this.config = {
      maxRetries: 3,
      timeout: 30000,
      batchSize: 100,
      baseUrl: 'https://api.openai.com/v1',
      ...config
    };

    this.validateConfig();
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(text: string): Promise<number[]> {
    if (!text || text.trim().length === 0) {
      throw new Error('Text cannot be empty');
    }

    try {
      const embeddings = await this.generateEmbeddings([text]);
      return embeddings[0];
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate embeddings for multiple texts (batch processing)
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    if (!texts || texts.length === 0) {
      throw new Error('Texts array cannot be empty');
    }

    // Filter out empty texts
    const validTexts = texts.filter(text => text && text.trim().length > 0);
    if (validTexts.length === 0) {
      throw new Error('No valid texts provided');
    }

    try {
      // Process in batches if needed
      if (validTexts.length > this.config.batchSize) {
        return this.processBatches(validTexts);
      }

      return this.callEmbeddingAPI(validTexts);
    } catch (error) {
      console.error('Error generating embeddings:', error);
      throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get embedding dimensions for the current model
   */
  getDimensions(): number {
    const dimensionMap: Record<string, number> = {
      'text-embedding-ada-002': 1536,
      'text-embedding-3-small': 1536,
      'text-embedding-3-large': 3072,
      'text-embedding-3-large-256': 256,
      'text-embedding-3-large-1024': 1024,
      'text-embedding-3-small-512': 512
    };

    return dimensionMap[this.config.model] || 1536;
  }

  /**
   * Get the current model name
   */
  getModel(): string {
    return this.config.model;
  }

  /**
   * Get usage statistics
   */
  getUsageStats(): { requestCount: number; totalTokens: number; estimatedCost: number } {
    // Rough cost estimation (as of 2024)
    const costPerToken = this.getCostPerToken();
    const estimatedCost = this.totalTokens * costPerToken;

    return {
      requestCount: this.requestCount,
      totalTokens: this.totalTokens,
      estimatedCost
    };
  }

  /**
   * Reset usage statistics
   */
  resetUsageStats(): void {
    this.requestCount = 0;
    this.totalTokens = 0;
  }

  // Private methods

  private async processBatches(texts: string[]): Promise<number[][]> {
    const results: number[][] = [];
    
    for (let i = 0; i < texts.length; i += this.config.batchSize) {
      const batch = texts.slice(i, i + this.config.batchSize);
      const batchResults = await this.callEmbeddingAPI(batch);
      results.push(...batchResults);
    }

    return results;
  }

  private async callEmbeddingAPI(texts: string[]): Promise<number[][]> {
    const url = `${this.config.baseUrl}/embeddings`;
    
    const requestBody = {
      input: texts,
      model: this.config.model,
      encoding_format: 'float'
    };

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        const response = await this.makeRequest(url, requestBody);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(`API request failed: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
        }

        const data: EmbeddingResponse = await response.json();
        
        // Update usage statistics
        this.requestCount++;
        this.totalTokens += data.usage.total_tokens;

        // Extract embeddings in the correct order
        const embeddings = data.data
          .sort((a, b) => a.index - b.index)
          .map(item => item.embedding);

        return embeddings;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.warn(`Embedding API attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error instanceof Error ? error.message : 'Unknown error');
          await this.sleep(delay);
        }
      }
    }

    throw lastError || new Error('All embedding API attempts failed');
  }

  private async makeRequest(url: string, body: any): Promise<Response> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`
    };

    // Add Azure OpenAI specific headers if using Azure
    if (this.config.baseUrl?.includes('openai.azure.com')) {
      headers['api-key'] = this.config.apiKey;
      delete headers['Authorization'];
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(body),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private validateConfig(): void {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    if (!this.config.model) {
      throw new Error('OpenAI model is required');
    }

    const supportedModels = [
      'text-embedding-ada-002',
      'text-embedding-3-small',
      'text-embedding-3-large'
    ];

    if (!supportedModels.some(model => this.config.model.startsWith(model))) {
      console.warn(`Model ${this.config.model} may not be supported. Supported models: ${supportedModels.join(', ')}`);
    }
  }

  private getCostPerToken(): number {
    // Cost per token in USD (as of 2024, subject to change)
    const costMap: Record<string, number> = {
      'text-embedding-ada-002': 0.0000001,
      'text-embedding-3-small': 0.00000002,
      'text-embedding-3-large': 0.00000013
    };

    return costMap[this.config.model] || 0.0000001;
  }

  /**
   * Test the embedding provider with a simple query
   */
  async test(): Promise<{ success: boolean; details: any }> {
    try {
      const testText = 'This is a test embedding';
      const embedding = await this.generateEmbedding(testText);
      
      return {
        success: true,
        details: {
          model: this.config.model,
          dimensions: embedding.length,
          expectedDimensions: this.getDimensions(),
          sampleEmbedding: embedding.slice(0, 5), // First 5 values
          usageStats: this.getUsageStats()
        }
      };
    } catch (error) {
      return {
        success: false,
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Create a provider instance from environment variables
   */
  static fromEnv(): OpenAIEmbeddingProvider {
    const apiKey = process.env.OPENAI_API_KEY;
    const model = process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small';
    const baseUrl = process.env.OPENAI_BASE_URL;

    if (!apiKey) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    return new OpenAIEmbeddingProvider({
      apiKey,
      model,
      baseUrl
    });
  }

  /**
   * Create an Azure OpenAI provider instance
   */
  static forAzure(config: {
    apiKey: string;
    endpoint: string;
    deploymentName: string;
    apiVersion?: string;
  }): OpenAIEmbeddingProvider {
    const { apiKey, endpoint, deploymentName, apiVersion = '2024-02-01' } = config;
    
    const baseUrl = `${endpoint}/openai/deployments/${deploymentName}`;
    
    return new OpenAIEmbeddingProvider({
      apiKey,
      model: deploymentName,
      baseUrl: `${baseUrl}?api-version=${apiVersion}`
    });
  }
}
