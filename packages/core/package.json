{"name": "@universal-ai-brain/core", "version": "1.0.0", "description": "Universal AI Brain - MongoDB-powered intelligence layer for any TypeScript AI framework", "files": ["dist/**/*", "README.md", "LICENSE"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "build:watch": "tsc -w -p tsconfig.build.json", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "dev": "tsc -w -p tsconfig.build.json", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["mongodb", "ai", "agents", "vector-search", "mastra", "vercel-ai", "langchain", "openai-agents", "typescript", "framework-agnostic", "intelligence-layer", "semantic-search", "memory", "context-injection"], "author": "Rom Iluz <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/romiluz13/AI_Brain.git"}, "bugs": {"url": "https://github.com/romiluz13/AI_Brain/issues"}, "homepage": "https://github.com/romiluz13/AI_Brain#readme", "peerDependencies": {"@ai-sdk/openai": ">=0.0.1", "@mastra/core": ">=0.1.0", "@openai/agents": ">=0.0.1", "ai": ">=3.0.0", "langchain": ">=0.3.0"}, "peerDependenciesMeta": {"@mastra/core": {"optional": true}, "@ai-sdk/openai": {"optional": true}, "ai": {"optional": true}, "langchain": {"optional": true}, "@openai/agents": {"optional": true}}, "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1", "eventemitter3": "^5.0.1", "mongodb": "^6.5.0", "node-fetch": "^3.3.2", "openai": "^4.0.0", "pino": "^8.19.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^0.0.48", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.1", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-custom": "*", "jest": "^29.0.0", "mongodb-memory-server": "^9.1.6", "rimraf": "^5.0.0", "socket.io": "^4.8.1", "ts-jest": "^29.0.0", "tsconfig": "*", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "publishConfig": {"access": "public"}}