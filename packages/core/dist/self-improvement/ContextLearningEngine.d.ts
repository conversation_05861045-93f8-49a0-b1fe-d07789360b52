/**
 * @file ContextLearningEngine - Intelligent context learning and optimization system
 *
 * This engine learns from user feedback, optimizes vector search parameters, adapts to
 * user preferences, and improves semantic search accuracy over time using MongoDB
 * Atlas Vector Search analytics and official $vectorSearch aggregation patterns.
 *
 * Features:
 * - Vector search parameter optimization using official MongoDB patterns
 * - User feedback learning with MongoDB aggregation analytics
 * - Context relevance scoring and improvement
 * - Adaptive semantic search enhancement
 * - Real-time learning from interaction patterns
 * - Framework-agnostic optimization
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
import { MongoVectorStore } from '../vector/MongoVectorStore';
export interface ContextFeedback {
    traceId: string;
    contextItemId: string;
    relevanceScore: number;
    userRating: 'helpful' | 'somewhat_helpful' | 'not_helpful' | 'harmful';
    feedback?: string;
    timestamp: Date;
    userId?: string;
    sessionId: string;
}
export interface VectorSearchOptimization {
    indexName: string;
    currentParams: {
        numCandidates: number;
        limit: number;
        minScore: number;
    };
    optimizedParams: {
        numCandidates: number;
        limit: number;
        minScore: number;
    };
    improvementMetrics: {
        relevanceImprovement: number;
        performanceImpact: number;
        userSatisfaction: number;
    };
    lastOptimized: Date;
}
export interface ContextPattern {
    pattern: string;
    frequency: number;
    averageRelevance: number;
    successRate: number;
    frameworks: string[];
    topics: string[];
    userPreferences: {
        preferredLength: number;
        preferredSources: string[];
        preferredFormats: string[];
    };
}
export interface LearningReport {
    reportId: string;
    timestamp: Date;
    timeRange: {
        start: Date;
        end: Date;
    };
    optimizations: VectorSearchOptimization[];
    contextPatterns: ContextPattern[];
    userFeedbackSummary: {
        totalFeedback: number;
        averageRating: number;
        improvementTrends: {
            relevanceImprovement: number;
            satisfactionImprovement: number;
        };
    };
    recommendations: {
        priority: 'high' | 'medium' | 'low';
        action: string;
        expectedImpact: string;
    }[];
}
/**
 * ContextLearningEngine - Intelligent context learning and optimization
 *
 * Uses MongoDB Atlas Vector Search analytics and official aggregation patterns
 * to continuously improve context relevance and search accuracy.
 */
export declare class ContextLearningEngine {
    private tracingCollection;
    private memoryCollection;
    private vectorStore;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, vectorStore: MongoVectorStore);
    /**
     * Learn from user feedback and optimize context relevance
     */
    learnFromFeedback(feedback: ContextFeedback): Promise<void>;
    /**
     * Optimize vector search parameters using MongoDB aggregation analytics
     */
    optimizeVectorSearchParameters(timeRange: {
        start: Date;
        end: Date;
    }, indexName: string): Promise<VectorSearchOptimization>;
    /**
     * Analyze context patterns using MongoDB aggregation
     */
    analyzeContextPatterns(timeRange: {
        start: Date;
        end: Date;
    }): Promise<ContextPattern[]>;
    /**
     * Generate comprehensive learning report
     */
    generateLearningReport(timeRange: {
        start: Date;
        end: Date;
    }): Promise<LearningReport>;
    /**
     * Apply learned optimizations to vector search
     */
    applyOptimizations(optimizations: VectorSearchOptimization[]): Promise<void>;
    private storeFeedback;
    private optimizeBasedOnFeedback;
    private updateContextScoring;
    private analyzeVectorSearchPerformance;
    private findOptimalCandidates;
    private findOptimalScore;
    private findOptimalLimit;
    private getCurrentVectorSearchParams;
    private calculateRelevanceImprovement;
    private calculatePerformanceImpact;
    private calculateSatisfactionImprovement;
    private applyVectorSearchOptimization;
    private calculatePreferredLength;
    private getAllVectorSearchOptimizations;
    private generateFeedbackSummary;
    private generateRecommendations;
    private adjustContextScoring;
}
//# sourceMappingURL=ContextLearningEngine.d.ts.map