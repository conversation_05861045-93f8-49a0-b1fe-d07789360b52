/**
 * @file FrameworkOptimizationEngine - Framework-specific parameter optimization system
 *
 * This engine learns optimal model parameters, prompt patterns, and configuration
 * settings for each framework (Vercel AI, Mastra, OpenAI Agents, LangChain) while
 * maintaining API compatibility. Uses MongoDB analytics to track performance and
 * automatically optimize framework-specific settings.
 *
 * Features:
 * - Framework-specific parameter optimization (temperature, maxTokens, topP, etc.)
 * - Prompt pattern learning and optimization
 * - Model configuration tuning per framework
 * - A/B testing for parameter combinations
 * - Performance-based automatic optimization
 * - API compatibility preservation
 * - Real-time optimization analytics with MongoDB
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface FrameworkOptimization {
    frameworkName: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain';
    optimizationType: 'model_parameters' | 'prompt_patterns' | 'configuration' | 'tool_usage';
    currentSettings: FrameworkSettings;
    optimizedSettings: FrameworkSettings;
    performanceMetrics: {
        responseTime: number;
        accuracy: number;
        userSatisfaction: number;
        costEfficiency: number;
        errorRate: number;
    };
    testResults: OptimizationTestResult[];
    confidence: number;
    lastOptimized: Date;
}
export interface FrameworkSettings {
    vercelAI?: {
        temperature?: number;
        maxTokens?: number;
        topP?: number;
        frequencyPenalty?: number;
        presencePenalty?: number;
        model?: string;
        streaming?: boolean;
        tools?: string[];
    };
    mastra?: {
        model?: string;
        instructions?: string;
        runtimeContext?: Record<string, any>;
        tools?: string[];
        evals?: string[];
        memory?: boolean;
        voice?: boolean;
    };
    openaiAgents?: {
        temperature?: number;
        topP?: number;
        frequencyPenalty?: number;
        presencePenalty?: number;
        maxTokens?: number;
        toolChoice?: 'auto' | 'required' | 'none' | string;
        parallelToolCalls?: boolean;
        truncation?: 'auto' | 'disabled';
        store?: boolean;
    };
    langchain?: {
        temperature?: number;
        maxTokens?: number;
        topP?: number;
        model?: string;
        streaming?: boolean;
        callbacks?: string[];
        memory?: string;
        tools?: string[];
    };
}
export interface OptimizationTestResult {
    testId: string;
    timestamp: Date;
    settings: FrameworkSettings;
    metrics: {
        responseTime: number;
        accuracy: number;
        userSatisfaction: number;
        costPerToken: number;
        errorCount: number;
        successRate: number;
    };
    sampleSize: number;
    statisticalSignificance: number;
}
export interface OptimizationReport {
    reportId: string;
    timestamp: Date;
    timeRange: {
        start: Date;
        end: Date;
    };
    frameworkOptimizations: FrameworkOptimization[];
    crossFrameworkInsights: {
        bestPerformingFramework: string;
        optimalParameterRanges: Record<string, {
            min: number;
            max: number;
            optimal: number;
        }>;
        commonPatterns: string[];
    };
    recommendations: {
        framework: string;
        priority: 'immediate' | 'high' | 'medium' | 'low';
        optimization: string;
        expectedImprovement: string;
        implementationComplexity: 'low' | 'medium' | 'high';
    }[];
}
/**
 * FrameworkOptimizationEngine - Framework-specific parameter optimization
 *
 * Learns optimal parameters for each framework while maintaining API compatibility
 * and uses MongoDB analytics to track performance improvements.
 */
export declare class FrameworkOptimizationEngine {
    private tracingCollection;
    private memoryCollection;
    private optimizationHistory;
    private activeTests;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection);
    /**
     * Optimize framework-specific parameters based on performance data
     */
    optimizeFrameworkParameters(framework: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain', timeRange: {
        start: Date;
        end: Date;
    }, optimizationType?: 'model_parameters' | 'prompt_patterns' | 'configuration' | 'tool_usage'): Promise<FrameworkOptimization>;
    /**
     * Analyze framework performance using MongoDB aggregation
     */
    private analyzeFrameworkPerformance;
    /**
     * Generate optimization candidates based on framework-specific patterns
     */
    private generateOptimizationCandidates;
    /**
     * Generate Vercel AI specific optimizations based on official SDK patterns
     */
    private generateVercelAIOptimizations;
    /**
     * Generate Mastra specific optimizations based on official patterns
     */
    private generateMastraOptimizations;
    /**
     * Generate OpenAI Agents specific optimizations based on official patterns
     */
    private generateOpenAIAgentsOptimizations;
    /**
     * Generate LangChain specific optimizations
     */
    private generateLangChainOptimizations;
    /**
     * Run A/B tests for optimization candidates
     */
    private runOptimizationTests;
    /**
     * Select best performing optimization based on weighted metrics
     */
    private selectBestOptimization;
    /**
     * Calculate optimization confidence based on statistical significance
     */
    private calculateOptimizationConfidence;
    /**
     * Store optimization results in MongoDB
     */
    private storeOptimization;
    /**
     * Get current framework settings (would integrate with actual framework configurations)
     */
    private getCurrentFrameworkSettings;
    /**
     * Find optimal parameter range from performance data
     */
    private findOptimalParameterRange;
    /**
     * Calculate performance improvement percentage
     */
    private calculatePerformanceImprovement;
}
//# sourceMappingURL=FrameworkOptimizationEngine.d.ts.map