/**
 * @file SelfImprovementMetrics - Comprehensive metrics and feedback loop system
 *
 * This system implements comprehensive metrics collection for self-improvement tracking,
 * A/B testing framework for prompt optimization, and automated feedback loops that
 * continuously enhance the Universal AI Brain's performance using MongoDB analytics.
 *
 * Features:
 * - Comprehensive performance metrics tracking
 * - A/B testing framework for prompt optimization
 * - Automated feedback loops and improvement cycles
 * - Real-time improvement analytics with MongoDB
 * - Cross-framework performance comparison
 * - Predictive improvement modeling
 * - Automated optimization triggers
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
import { FailureAnalysisEngine } from './FailureAnalysisEngine';
import { ContextLearningEngine } from './ContextLearningEngine';
import { FrameworkOptimizationEngine } from './FrameworkOptimizationEngine';
export interface ImprovementMetrics {
    metricId: string;
    timestamp: Date;
    timeRange: {
        start: Date;
        end: Date;
    };
    overallPerformance: {
        responseTime: {
            current: number;
            baseline: number;
            improvement: number;
            trend: 'improving' | 'stable' | 'declining';
        };
        accuracy: {
            current: number;
            baseline: number;
            improvement: number;
            trend: 'improving' | 'stable' | 'declining';
        };
        userSatisfaction: {
            current: number;
            baseline: number;
            improvement: number;
            trend: 'improving' | 'stable' | 'declining';
        };
        costEfficiency: {
            current: number;
            baseline: number;
            improvement: number;
            trend: 'improving' | 'stable' | 'declining';
        };
    };
    frameworkMetrics: {
        framework: string;
        performanceScore: number;
        improvementRate: number;
        optimizationCount: number;
        lastOptimized: Date;
    }[];
    improvementAreas: {
        area: 'context_relevance' | 'prompt_optimization' | 'parameter_tuning' | 'error_reduction' | 'cost_optimization';
        currentScore: number;
        targetScore: number;
        progress: number;
        priority: 'high' | 'medium' | 'low';
        estimatedCompletion: Date;
    }[];
    feedbackLoops: {
        loopId: string;
        type: 'automated' | 'user_feedback' | 'performance_based' | 'error_triggered';
        status: 'active' | 'paused' | 'completed';
        triggerCondition: string;
        lastTriggered: Date;
        improvementGenerated: number;
    }[];
}
export interface ABTestResult {
    testId: string;
    testName: string;
    startDate: Date;
    endDate: Date;
    variants: {
        variantId: string;
        name: string;
        configuration: any;
        sampleSize: number;
        metrics: {
            responseTime: number;
            accuracy: number;
            userSatisfaction: number;
            costPerOperation: number;
            errorRate: number;
        };
        statisticalSignificance: number;
    }[];
    winner: {
        variantId: string;
        confidenceLevel: number;
        improvementPercentage: number;
    };
    status: 'running' | 'completed' | 'paused' | 'cancelled';
}
export interface FeedbackLoop {
    loopId: string;
    name: string;
    type: 'automated' | 'user_feedback' | 'performance_based' | 'error_triggered';
    triggerConditions: {
        metric: string;
        threshold: number;
        operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
        timeWindow: number;
    }[];
    actions: {
        actionType: 'optimize_parameters' | 'retrain_model' | 'update_prompts' | 'adjust_context' | 'alert_human';
        parameters: Record<string, any>;
        priority: number;
    }[];
    isActive: boolean;
    lastTriggered?: Date;
    triggerCount: number;
    successRate: number;
}
export interface ImprovementPrediction {
    predictionId: string;
    timestamp: Date;
    timeHorizon: number;
    predictedImprovements: {
        metric: string;
        currentValue: number;
        predictedValue: number;
        confidence: number;
        factors: string[];
    }[];
    recommendedActions: {
        action: string;
        priority: 'immediate' | 'high' | 'medium' | 'low';
        expectedImpact: number;
        estimatedEffort: 'low' | 'medium' | 'high';
        timeline: string;
    }[];
}
/**
 * SelfImprovementMetrics - Comprehensive metrics and feedback loop system
 *
 * Tracks improvement progress, runs A/B tests, and creates automated feedback
 * loops for continuous enhancement of the Universal AI Brain.
 */
export declare class SelfImprovementMetrics {
    private tracingCollection;
    private memoryCollection;
    private failureAnalysisEngine;
    private contextLearningEngine;
    private frameworkOptimizationEngine;
    private activeFeedbackLoops;
    private activeABTests;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, failureAnalysisEngine: FailureAnalysisEngine, contextLearningEngine: ContextLearningEngine, frameworkOptimizationEngine: FrameworkOptimizationEngine);
    /**
     * Generate comprehensive improvement metrics using MongoDB aggregation
     */
    generateImprovementMetrics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<ImprovementMetrics>;
    /**
     * Start A/B test for prompt optimization
     */
    startABTest(testName: string, variants: {
        name: string;
        configuration: any;
    }[], duration?: number): Promise<string>;
    /**
     * Create automated feedback loop
     */
    createFeedbackLoop(name: string, type: FeedbackLoop['type'], triggerConditions: FeedbackLoop['triggerConditions'], actions: FeedbackLoop['actions']): Promise<string>;
    /**
     * Generate improvement predictions using trend analysis
     */
    generateImprovementPredictions(timeHorizon?: number): Promise<ImprovementPrediction>;
    /**
     * Process feedback loop triggers
     */
    processFeedbackLoops(): Promise<void>;
    private initializeFeedbackLoops;
    private getBaselineMetrics;
    private calculatePerformanceImprovements;
    private analyzeImprovementAreas;
    private calculateImprovementRate;
    private getOptimizationCount;
    private calculatePredictions;
    private generateRecommendations;
    private getHistoricalTrends;
    private evaluateTriggerConditions;
    private executeFeedbackLoop;
}
//# sourceMappingURL=SelfImprovementMetrics.d.ts.map