/**
 * @file FailureAnalysisEngine - Intelligent failure pattern analysis and improvement suggestions
 *
 * This engine analyzes failure patterns from agent traces using MongoDB aggregation
 * pipelines to identify context gaps, detect prompt weaknesses, and suggest
 * improvements for the Universal AI Brain.
 *
 * Features:
 * - Multi-dimensional failure analysis using MongoDB $facet
 * - Pattern detection with $bucket aggregations
 * - Context gap identification
 * - Prompt weakness detection
 * - Automated improvement suggestions
 * - Framework-specific failure analysis
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface FailurePattern {
    id: string;
    type: 'context_gap' | 'prompt_weakness' | 'framework_error' | 'timeout' | 'safety_violation' | 'unknown';
    frequency: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    examples: string[];
    suggestedFix: string;
    affectedFrameworks: string[];
    firstSeen: Date;
    lastSeen: Date;
    metadata?: Record<string, any>;
}
export interface ContextGap {
    missingTopic: string;
    frequency: number;
    relatedQueries: string[];
    suggestedSources: string[];
    priority: 'low' | 'medium' | 'high';
}
export interface PromptWeakness {
    pattern: string;
    description: string;
    frequency: number;
    failureRate: number;
    suggestedImprovement: string;
    examples: string[];
}
export interface FailureAnalysisReport {
    analysisId: string;
    timestamp: Date;
    timeRange: {
        start: Date;
        end: Date;
    };
    summary: {
        totalFailures: number;
        failureRate: number;
        mostCommonType: string;
        criticalIssues: number;
    };
    patterns: FailurePattern[];
    contextGaps: ContextGap[];
    promptWeaknesses: PromptWeakness[];
    frameworkAnalysis: {
        framework: string;
        failureRate: number;
        commonErrors: string[];
    }[];
    recommendations: {
        priority: 'immediate' | 'high' | 'medium' | 'low';
        action: string;
        description: string;
        estimatedImpact: string;
    }[];
}
/**
 * FailureAnalysisEngine - Intelligent failure pattern analysis
 *
 * Uses MongoDB aggregation pipelines to analyze failure patterns and
 * provide actionable insights for improving the Universal AI Brain.
 */
export declare class FailureAnalysisEngine {
    private tracingCollection;
    private memoryCollection;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection);
    /**
     * Analyze failures over a time period using MongoDB $facet aggregation
     */
    analyzeFailures(startDate: Date, endDate: Date, options?: {
        minFrequency?: number;
        includeRecoverable?: boolean;
        frameworks?: string[];
    }): Promise<FailureAnalysisReport>;
    /**
     * Extract failure patterns from bucket analysis results
     */
    private extractFailurePatterns;
    /**
     * Identify context gaps from failed queries
     */
    private identifyContextGaps;
    /**
     * Detect prompt weaknesses from failure patterns
     */
    private detectPromptWeaknesses;
    /**
     * Analyze framework-specific failures
     */
    private analyzeFrameworkFailures;
    /**
     * Generate actionable recommendations based on analysis
     */
    private generateRecommendations;
    private getPatternDescription;
    private getSuggestedFix;
    private extractTopicFromQuery;
    private suggestContextSources;
    private extractPromptPatterns;
    private getPromptWeaknessDescription;
    private getSuggestedPromptImprovement;
    private getTotalTraces;
}
//# sourceMappingURL=FailureAnalysisEngine.d.ts.map