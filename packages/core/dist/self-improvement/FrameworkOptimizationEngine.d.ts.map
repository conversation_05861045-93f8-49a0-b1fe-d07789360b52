{"version": 3, "file": "FrameworkOptimizationEngine.d.ts", "sourceRoot": "", "sources": ["../../src/self-improvement/FrameworkOptimizationEngine.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;GAgBG;AAEH,OAAO,EAAE,iBAAiB,EAAc,MAAM,kCAAkC,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAEnE,MAAM,WAAW,qBAAqB;IACpC,aAAa,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,CAAC;IACtE,gBAAgB,EAAE,kBAAkB,GAAG,iBAAiB,GAAG,eAAe,GAAG,YAAY,CAAC;IAC1F,eAAe,EAAE,iBAAiB,CAAC;IACnC,iBAAiB,EAAE,iBAAiB,CAAC;IACrC,kBAAkB,EAAE;QAClB,YAAY,EAAE,MAAM,CAAC;QACrB,QAAQ,EAAE,MAAM,CAAC;QACjB,gBAAgB,EAAE,MAAM,CAAC;QACzB,cAAc,EAAE,MAAM,CAAC;QACvB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;IACF,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACtC,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,IAAI,CAAC;CACrB;AAED,MAAM,WAAW,iBAAiB;IAEhC,QAAQ,CAAC,EAAE;QACT,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,gBAAgB,CAAC,EAAE,MAAM,CAAC;QAC1B,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,SAAS,CAAC,EAAE,OAAO,CAAC;QACpB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;KAClB,CAAC;IAGF,MAAM,CAAC,EAAE;QACP,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,YAAY,CAAC,EAAE,MAAM,CAAC;QACtB,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QACrC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;QACjB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;QACjB,MAAM,CAAC,EAAE,OAAO,CAAC;QACjB,KAAK,CAAC,EAAE,OAAO,CAAC;KACjB,CAAC;IAGF,YAAY,CAAC,EAAE;QACb,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,gBAAgB,CAAC,EAAE,MAAM,CAAC;QAC1B,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,UAAU,CAAC,EAAE,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,MAAM,CAAC;QACnD,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAC5B,UAAU,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC;QACjC,KAAK,CAAC,EAAE,OAAO,CAAC;KACjB,CAAC;IAGF,SAAS,CAAC,EAAE;QACV,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,SAAS,CAAC,EAAE,OAAO,CAAC;QACpB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;KAClB,CAAC;CACH;AAED,MAAM,WAAW,sBAAsB;IACrC,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,EAAE,iBAAiB,CAAC;IAC5B,OAAO,EAAE;QACP,YAAY,EAAE,MAAM,CAAC;QACrB,QAAQ,EAAE,MAAM,CAAC;QACjB,gBAAgB,EAAE,MAAM,CAAC;QACzB,YAAY,EAAE,MAAM,CAAC;QACrB,UAAU,EAAE,MAAM,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,UAAU,EAAE,MAAM,CAAC;IACnB,uBAAuB,EAAE,MAAM,CAAC;CACjC;AAED,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,sBAAsB,EAAE,qBAAqB,EAAE,CAAC;IAChD,sBAAsB,EAAE;QACtB,uBAAuB,EAAE,MAAM,CAAC;QAChC,sBAAsB,EAAE,MAAM,CAAC,MAAM,EAAE;YAAE,GAAG,EAAE,MAAM,CAAC;YAAC,GAAG,EAAE,MAAM,CAAC;YAAC,OAAO,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;QACtF,cAAc,EAAE,MAAM,EAAE,CAAC;KAC1B,CAAC;IACF,eAAe,EAAE;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QAClD,YAAY,EAAE,MAAM,CAAC;QACrB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,wBAAwB,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;KACrD,EAAE,CAAC;CACL;AAED;;;;;GAKG;AACH,qBAAa,2BAA2B;IACtC,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,mBAAmB,CAAmD;IAC9E,OAAO,CAAC,WAAW,CAAoD;gBAE3D,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB;IAKpF;;OAEG;IACG,2BAA2B,CAC/B,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,EACjE,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,EACrC,gBAAgB,GAAE,kBAAkB,GAAG,iBAAiB,GAAG,eAAe,GAAG,YAAiC,GAC7G,OAAO,CAAC,qBAAqB,CAAC;IAyCjC;;OAEG;YACW,2BAA2B;IA6FzC;;OAEG;YACW,8BAA8B;IA0B5C;;OAEG;IACH,OAAO,CAAC,6BAA6B;IA2DrC;;OAEG;IACH,OAAO,CAAC,2BAA2B;IA4CnC;;OAEG;IACH,OAAO,CAAC,iCAAiC;IA6CzC;;OAEG;IACH,OAAO,CAAC,8BAA8B;IAgCtC;;OAEG;YACW,oBAAoB;IA8BlC;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAqB9B;;OAEG;IACH,OAAO,CAAC,+BAA+B;IAOvC;;OAEG;YACW,iBAAiB;IAmB/B;;OAEG;YACW,2BAA2B;IA2CzC;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAyBjC;;OAEG;IACH,OAAO,CAAC,+BAA+B;CAIxC"}