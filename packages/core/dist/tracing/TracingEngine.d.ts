/**
 * @file TracingEngine - Real-time agent tracing and observability engine
 *
 * This is the core orchestrator for enterprise-grade agent tracing, providing
 * real-time monitoring, performance analysis, and debugging capabilities across
 * all framework integrations.
 *
 * Features:
 * - MongoDB transactions for trace consistency
 * - Real-time trace lifecycle management
 * - Performance metrics collection
 * - Error tracking and recovery
 * - Framework-agnostic tracing
 */
import { ObjectId } from 'mongodb';
import { TracingCollection, AgentTrace, AgentStep, AgentError, ContextItem, TokenUsage, CostBreakdown, FrameworkMetadata } from '../collections/TracingCollection';
import { MongoConnection } from '../persistance/MongoConnection';
export interface TraceStartOptions {
    agentId: ObjectId;
    sessionId: string;
    conversationId?: string;
    operation: {
        type: AgentTrace['operation']['type'];
        description?: string;
        userInput: string;
    };
    framework: FrameworkMetadata;
    userContext?: AgentTrace['userContext'];
    tags?: string[];
    metadata?: Record<string, any>;
}
export interface StepStartOptions {
    stepType: AgentStep['stepType'];
    input?: any;
    metadata?: Record<string, any>;
}
export interface StepCompleteOptions {
    output?: any;
    error?: AgentError;
    metadata?: Record<string, any>;
}
export interface TraceCompleteOptions {
    status: 'completed' | 'failed' | 'cancelled' | 'timeout';
    finalOutput?: string;
    outputType?: AgentTrace['operation']['outputType'];
    tokensUsed?: Partial<TokenUsage>;
    cost?: Partial<CostBreakdown>;
    errors?: AgentError[];
    warnings?: string[];
    safetyChecks?: AgentTrace['safetyChecks'];
    debugInfo?: AgentTrace['debugInfo'];
}
/**
 * TracingEngine - Core orchestrator for real-time agent tracing
 *
 * This class manages the complete lifecycle of agent traces with MongoDB
 * transactions for consistency and real-time monitoring capabilities.
 */
export declare class TracingEngine {
    private tracingCollection;
    private mongoConnection;
    private activeTraces;
    constructor(tracingCollection: TracingCollection, mongoConnection: MongoConnection);
    /**
     * Start a new trace with MongoDB transaction
     */
    startTrace(options: TraceStartOptions): Promise<string>;
    /**
     * Start a new step within a trace
     */
    startStep(traceId: string, options: StepStartOptions): Promise<string>;
    /**
     * Complete a step within a trace
     */
    completeStep(traceId: string, stepId: string, options: StepCompleteOptions): Promise<void>;
    /**
     * Record context items used in a trace
     */
    recordContextUsed(traceId: string, contextItems: ContextItem[]): Promise<void>;
    /**
     * Record an error in a trace
     */
    recordError(traceId: string, error: AgentError): Promise<void>;
    /**
     * Complete a trace with final results
     */
    completeTrace(traceId: string, options: TraceCompleteOptions): Promise<void>;
    /**
     * Get active traces for real-time monitoring
     */
    getActiveTraces(): Array<{
        traceId: string;
        agentId: ObjectId;
        sessionId: string;
        startTime: Date;
        currentStep?: string;
        duration: number;
    }>;
    /**
     * Force cleanup of stale traces (for error recovery)
     */
    cleanupStaleTraces(maxAgeMinutes?: number): Promise<number>;
    /**
     * Get trace statistics
     */
    getStats(): {
        activeTraces: number;
        averageTraceAge: number;
        oldestTraceAge: number;
    };
}
//# sourceMappingURL=TracingEngine.d.ts.map