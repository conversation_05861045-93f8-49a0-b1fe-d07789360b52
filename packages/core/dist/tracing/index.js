"use strict";
/**
 * @file Tracing Module - Enterprise-grade agent tracing and observability
 *
 * This module provides comprehensive tracing capabilities for the Universal AI Brain,
 * enabling real-time monitoring, performance analysis, and debugging across all
 * framework integrations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TracingDecorators = exports.TracingUtils = exports.TracingCollection = exports.RealTimeMonitor = exports.ChangeStreamManager = exports.TracingEngine = void 0;
// Core tracing engine
var TracingEngine_1 = require("./TracingEngine");
Object.defineProperty(exports, "TracingEngine", { enumerable: true, get: function () { return TracingEngine_1.TracingEngine; } });
// Real-time monitoring with MongoDB Change Streams
var ChangeStreamManager_1 = require("./ChangeStreamManager");
Object.defineProperty(exports, "ChangeStreamManager", { enumerable: true, get: function () { return ChangeStreamManager_1.ChangeStreamManager; } });
// Enterprise real-time monitoring service
var RealTimeMonitor_1 = require("./RealTimeMonitor");
Object.defineProperty(exports, "RealTimeMonitor", { enumerable: true, get: function () { return RealTimeMonitor_1.RealTimeMonitor; } });
// Tracing collection and types
var TracingCollection_1 = require("../collections/TracingCollection");
Object.defineProperty(exports, "TracingCollection", { enumerable: true, get: function () { return TracingCollection_1.TracingCollection; } });
// Utility functions for tracing
class TracingUtils {
    /**
     * Generate a standardized error object for tracing
     */
    static createAgentError(errorType, message, originalError, recoverable = true, context) {
        return {
            errorId: require('uuid').v4(),
            errorType,
            message,
            stack: originalError?.stack,
            code: originalError?.code,
            timestamp: new Date(),
            recoverable,
            retryCount: 0,
            context
        };
    }
    /**
     * Create performance metrics from timing data
     */
    static createPerformanceMetrics(timings) {
        const totalDuration = Object.values(timings).reduce((sum, time) => sum + (time || 0), 0);
        return {
            totalDuration,
            contextRetrievalTime: timings.contextRetrievalTime || 0,
            promptEnhancementTime: timings.promptEnhancementTime || 0,
            frameworkCallTime: timings.frameworkCallTime || 0,
            responseProcessingTime: timings.responseProcessingTime || 0,
            memoryStorageTime: timings.memoryStorageTime || 0,
            memoryUsage: process.memoryUsage ? {
                heapUsed: process.memoryUsage().heapUsed,
                heapTotal: process.memoryUsage().heapTotal,
                external: process.memoryUsage().external
            } : undefined
        };
    }
    /**
     * Create context item for tracing
     */
    static createContextItem(source, content, relevanceScore, retrievalTime, metadata) {
        return {
            contextId: require('uuid').v4(),
            source,
            content,
            relevanceScore,
            retrievalTime,
            metadata
        };
    }
    /**
     * Create token usage tracking
     */
    static createTokenUsage(promptTokens, completionTokens, embeddingTokens, frameworkTokens) {
        return {
            promptTokens,
            completionTokens,
            totalTokens: promptTokens + completionTokens,
            embeddingTokens,
            frameworkTokens
        };
    }
    /**
     * Create cost breakdown for tracing
     */
    static createCostBreakdown(costs, currency = 'USD') {
        const totalCost = (costs.embeddingCost || 0) +
            (costs.completionCost || 0) +
            (costs.promptCost || 0) +
            (costs.frameworkCosts?.modelCost || 0) +
            (costs.frameworkCosts?.apiCost || 0) +
            (costs.mongoCosts?.vectorSearchCost || 0) +
            (costs.mongoCosts?.readCost || 0) +
            (costs.mongoCosts?.writeCost || 0);
        return {
            totalCost,
            embeddingCost: costs.embeddingCost || 0,
            completionCost: costs.completionCost || 0,
            promptCost: costs.promptCost || 0,
            frameworkCosts: costs.frameworkCosts,
            mongoCosts: costs.mongoCosts,
            currency,
            calculatedAt: new Date()
        };
    }
    /**
     * Create framework metadata for different frameworks
     */
    static createFrameworkMetadata(frameworkName, frameworkData) {
        return {
            frameworkName,
            frameworkVersion: frameworkData.frameworkVersion,
            vercelAI: frameworkData.vercelAI,
            mastra: frameworkData.mastra,
            openaiAgents: frameworkData.openaiAgents,
            langchain: frameworkData.langchain
        };
    }
    /**
     * Format trace duration for display
     */
    static formatDuration(milliseconds) {
        if (milliseconds < 1000) {
            return `${milliseconds}ms`;
        }
        else if (milliseconds < 60000) {
            return `${(milliseconds / 1000).toFixed(2)}s`;
        }
        else {
            const minutes = Math.floor(milliseconds / 60000);
            const seconds = ((milliseconds % 60000) / 1000).toFixed(0);
            return `${minutes}m ${seconds}s`;
        }
    }
    /**
     * Format cost for display
     */
    static formatCost(cost, currency = 'USD') {
        if (cost < 0.01) {
            return `<$0.01 ${currency}`;
        }
        return `$${cost.toFixed(4)} ${currency}`;
    }
    /**
     * Calculate trace health score based on performance and errors
     */
    static calculateTraceHealth(trace) {
        let performanceScore = 100;
        let errorScore = 100;
        let completionScore = 100;
        // Performance scoring (based on duration)
        if (trace.performance.totalDuration > 30000) { // > 30s
            performanceScore = 20;
        }
        else if (trace.performance.totalDuration > 10000) { // > 10s
            performanceScore = 50;
        }
        else if (trace.performance.totalDuration > 5000) { // > 5s
            performanceScore = 80;
        }
        // Error scoring
        if (trace.errors.length > 0) {
            const recoverableErrors = trace.errors.filter(e => e.recoverable).length;
            const nonRecoverableErrors = trace.errors.length - recoverableErrors;
            errorScore = Math.max(0, 100 - (nonRecoverableErrors * 50) - (recoverableErrors * 20));
        }
        // Completion scoring
        if (trace.status === 'completed') {
            completionScore = 100;
        }
        else if (trace.status === 'failed') {
            completionScore = 0;
        }
        else if (trace.status === 'cancelled') {
            completionScore = 30;
        }
        else if (trace.status === 'timeout') {
            completionScore = 10;
        }
        else {
            completionScore = 50; // active
        }
        const overallScore = (performanceScore * 0.4) + (errorScore * 0.4) + (completionScore * 0.2);
        return {
            score: Math.round(overallScore),
            factors: {
                performance: performanceScore,
                errors: errorScore,
                completion: completionScore
            }
        };
    }
    /**
     * Extract key metrics from a trace for dashboard display
     */
    static extractKeyMetrics(trace) {
        const health = this.calculateTraceHealth(trace);
        return {
            duration: this.formatDuration(trace.performance.totalDuration),
            cost: this.formatCost(trace.cost.totalCost, trace.cost.currency),
            tokens: trace.tokensUsed.totalTokens,
            errors: trace.errors.length,
            health: health.score,
            framework: trace.framework.frameworkName,
            operation: trace.operation.type
        };
    }
}
exports.TracingUtils = TracingUtils;
/**
 * Tracing decorators for automatic method tracing
 */
class TracingDecorators {
    /**
     * Decorator to automatically trace method execution
     */
    static trace(stepType) {
        return function (target, propertyName, descriptor) {
            const method = descriptor.value;
            descriptor.value = async function (...args) {
                const tracingEngine = this.tracingEngine;
                const traceId = this.currentTraceId;
                if (!tracingEngine || !traceId) {
                    // No tracing available, call original method
                    return method.apply(this, args);
                }
                const stepId = await tracingEngine.startStep(traceId, {
                    stepType,
                    input: args.length > 0 ? args[0] : undefined,
                    metadata: {
                        method: propertyName,
                        className: target.constructor.name
                    }
                });
                try {
                    const result = await method.apply(this, args);
                    await tracingEngine.completeStep(traceId, stepId, {
                        output: result
                    });
                    return result;
                }
                catch (error) {
                    const agentError = TracingUtils.createAgentError('framework_error', `Error in ${propertyName}: ${error instanceof Error ? error.message : 'Unknown error'}`, error instanceof Error ? error : undefined, true, { method: propertyName, className: target.constructor.name });
                    await tracingEngine.completeStep(traceId, stepId, {
                        error: agentError
                    });
                    throw error;
                }
            };
        };
    }
}
exports.TracingDecorators = TracingDecorators;
