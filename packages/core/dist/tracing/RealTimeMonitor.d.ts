/**
 * @file RealTimeMonitor - Enterprise real-time trace monitoring service
 *
 * This service provides comprehensive real-time monitoring of agent traces
 * using MongoDB Change Streams, with alerting, metrics aggregation, and
 * dashboard-ready data streams.
 *
 * Features:
 * - Real-time trace monitoring and alerting
 * - Performance metrics aggregation
 * - Error pattern detection
 * - Cost monitoring and budget alerts
 * - Framework-specific monitoring
 * - WebSocket/SSE ready data streams
 */
import { EventEmitter } from 'events';
import { TracingEngine } from './TracingEngine';
import { ChangeStreamManager, TraceChangeEvent } from './ChangeStreamManager';
import { TracingCollection } from '../collections/TracingCollection';
export interface MonitoringAlert {
    id: string;
    type: 'performance' | 'error' | 'cost' | 'health' | 'custom';
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    message: string;
    traceId?: string;
    agentId?: string;
    sessionId?: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}
export interface PerformanceMetrics {
    timestamp: Date;
    activeTraces: number;
    avgResponseTime: number;
    errorRate: number;
    totalCost: number;
    tokensPerSecond: number;
    frameworkBreakdown: Record<string, number>;
    operationBreakdown: Record<string, number>;
}
export interface MonitoringConfig {
    maxResponseTime: number;
    maxErrorRate: number;
    maxCostPerHour: number;
    enableAlerts: boolean;
    alertWebhook?: string;
    metricsInterval: number;
    retentionPeriod: number;
    monitoredAgents?: string[];
    monitoredFrameworks?: string[];
}
export interface MonitoringSubscriber {
    id: string;
    onAlert?: (alert: MonitoringAlert) => void;
    onMetrics?: (metrics: PerformanceMetrics) => void;
    onTraceUpdate?: (event: TraceChangeEvent) => void;
}
/**
 * RealTimeMonitor - Enterprise real-time trace monitoring service
 *
 * This service provides comprehensive monitoring capabilities for the
 * Universal AI Brain with real-time alerting and metrics collection.
 */
export declare class RealTimeMonitor extends EventEmitter {
    private tracingEngine;
    private changeStreamManager;
    private tracingCollection;
    private config;
    private subscribers;
    private metricsHistory;
    private metricsInterval?;
    private isActive;
    private currentMetrics;
    constructor(tracingEngine: TracingEngine, changeStreamManager: ChangeStreamManager, tracingCollection: TracingCollection, config?: Partial<MonitoringConfig>);
    /**
     * Start real-time monitoring
     */
    start(): Promise<void>;
    /**
     * Stop real-time monitoring
     */
    stop(): Promise<void>;
    /**
     * Subscribe to monitoring events
     */
    subscribe(subscriber: MonitoringSubscriber): void;
    /**
     * Unsubscribe from monitoring events
     */
    unsubscribe(subscriberId: string): void;
    /**
     * Handle trace change events from change stream
     */
    private handleTraceChange;
    /**
     * Update real-time counters based on trace changes
     */
    private updateCounters;
    /**
     * Check for alert conditions
     */
    private checkAlerts;
    /**
     * Send an alert to subscribers and external systems
     */
    private sendAlert;
    /**
     * Send alert to webhook
     */
    private sendWebhookAlert;
    /**
     * Start periodic metrics collection
     */
    private startMetricsCollection;
    /**
     * Collect current performance metrics
     */
    private collectMetrics;
    /**
     * Get framework usage breakdown
     */
    private getFrameworkBreakdown;
    /**
     * Get operation type breakdown
     */
    private getOperationBreakdown;
    /**
     * Reset performance counters
     */
    private resetCounters;
    /**
     * Handle change stream errors
     */
    private handleChangeStreamError;
    /**
     * Notify all subscribers of an event
     */
    private notifySubscribers;
    /**
     * Get current monitoring statistics
     */
    getStats(): {
        isActive: boolean;
        subscriberCount: number;
        metricsHistoryCount: number;
        currentMetrics: typeof this.currentMetrics;
        changeStreamStats: any;
    };
    /**
     * Get recent metrics history
     */
    getMetricsHistory(hours?: number): PerformanceMetrics[];
}
//# sourceMappingURL=RealTimeMonitor.d.ts.map