/**
 * @file ChangeStreamManager - Real-time MongoDB Change Streams for tracing
 *
 * This class implements MongoDB Change Streams for real-time trace monitoring,
 * following official MongoDB patterns with proper resume token handling,
 * error recovery, and observer pattern for notifying subscribers.
 *
 * Features:
 * - Official MongoDB Change Streams API compliance
 * - Resume token handling for reliability
 * - Observer pattern for real-time notifications
 * - Automatic error recovery and reconnection
 * - Filtered change streams for performance
 */
import { ResumeToken } from 'mongodb';
import { EventEmitter } from 'events';
import { TracingCollection, AgentTrace } from '../collections/TracingCollection';
import { MongoConnection } from '../persistance/MongoConnection';
export interface TraceChangeEvent {
    operationType: 'insert' | 'update' | 'delete' | 'replace';
    traceId: string;
    agentId: string;
    sessionId: string;
    fullDocument?: AgentTrace;
    updateDescription?: {
        updatedFields: Record<string, any>;
        removedFields: string[];
    };
    timestamp: Date;
    resumeToken: ResumeToken;
}
export interface ChangeStreamOptions {
    agentId?: string;
    sessionId?: string;
    status?: AgentTrace['status'];
    framework?: string;
    fullDocument?: 'default' | 'updateLookup' | 'whenAvailable' | 'required';
    maxAwaitTimeMS?: number;
    batchSize?: number;
    resumeAfter?: ResumeToken;
    startAfter?: ResumeToken;
    startAtOperationTime?: Date;
}
export interface ChangeStreamSubscriber {
    id: string;
    filter?: (event: TraceChangeEvent) => boolean;
    onTraceChange: (event: TraceChangeEvent) => void;
    onError?: (error: Error) => void;
}
/**
 * ChangeStreamManager - Real-time MongoDB Change Streams for tracing
 *
 * This class provides enterprise-grade real-time monitoring of trace changes
 * using MongoDB Change Streams with proper error handling and recovery.
 */
export declare class ChangeStreamManager extends EventEmitter {
    private tracingCollection;
    private mongoConnection;
    private changeStream?;
    private subscribers;
    private isActive;
    private resumeToken?;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private options;
    constructor(tracingCollection: TracingCollection, mongoConnection: MongoConnection, options?: ChangeStreamOptions);
    /**
     * Start the change stream with proper MongoDB patterns
     */
    start(): Promise<void>;
    /**
     * Stop the change stream
     */
    stop(): Promise<void>;
    /**
     * Subscribe to trace changes with optional filtering
     */
    subscribe(subscriber: ChangeStreamSubscriber): void;
    /**
     * Unsubscribe from trace changes
     */
    unsubscribe(subscriberId: string): void;
    /**
     * Get current resume token for external storage
     */
    getResumeToken(): ResumeToken | undefined;
    /**
     * Set resume token for recovery
     */
    setResumeToken(token: ResumeToken): void;
    /**
     * Create and configure the MongoDB Change Stream
     */
    private createChangeStream;
    /**
     * Build aggregation pipeline for filtering changes
     */
    private buildPipeline;
    /**
     * Set up event handlers for the change stream
     */
    private setupEventHandlers;
    /**
     * Handle individual change events
     */
    private handleChange;
    /**
     * Notify all subscribers of a change event
     */
    private notifySubscribers;
    /**
     * Handle change stream errors
     */
    private handleError;
    /**
     * Attempt to reconnect the change stream
     */
    private attemptReconnect;
    /**
     * Get statistics about the change stream
     */
    getStats(): {
        isActive: boolean;
        subscriberCount: number;
        reconnectAttempts: number;
        hasResumeToken: boolean;
    };
}
//# sourceMappingURL=ChangeStreamManager.d.ts.map