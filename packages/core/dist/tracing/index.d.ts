/**
 * @file Tracing Module - Enterprise-grade agent tracing and observability
 *
 * This module provides comprehensive tracing capabilities for the Universal AI Brain,
 * enabling real-time monitoring, performance analysis, and debugging across all
 * framework integrations.
 */
export { TracingEngine } from './TracingEngine';
export { ChangeStreamManager } from './ChangeStreamManager';
export type { TraceChangeEvent, ChangeStreamOptions, ChangeStreamSubscriber } from './ChangeStreamManager';
export { RealTimeMonitor } from './RealTimeMonitor';
export type { MonitoringAlert, PerformanceMetrics, MonitoringConfig, MonitoringSubscriber } from './RealTimeMonitor';
export { TracingCollection, AgentTrace, AgentStep, AgentError, PerformanceMetrics, ContextItem, TokenUsage, CostBreakdown, FrameworkMetadata } from '../collections/TracingCollection';
export type { TraceStartOptions, StepStartOptions, StepCompleteOptions, TraceCompleteOptions } from './TracingEngine';
export declare class TracingUtils {
    /**
     * Generate a standardized error object for tracing
     */
    static createAgentError(errorType: AgentError['errorType'], message: string, originalError?: Error, recoverable?: boolean, context?: Record<string, any>): AgentError;
    /**
     * Create performance metrics from timing data
     */
    static createPerformanceMetrics(timings: {
        contextRetrievalTime?: number;
        promptEnhancementTime?: number;
        frameworkCallTime?: number;
        responseProcessingTime?: number;
        memoryStorageTime?: number;
    }): PerformanceMetrics;
    /**
     * Create context item for tracing
     */
    static createContextItem(source: string, content: string, relevanceScore: number, retrievalTime: number, metadata?: Record<string, any>): ContextItem;
    /**
     * Create token usage tracking
     */
    static createTokenUsage(promptTokens: number, completionTokens: number, embeddingTokens?: number, frameworkTokens?: TokenUsage['frameworkTokens']): TokenUsage;
    /**
     * Create cost breakdown for tracing
     */
    static createCostBreakdown(costs: {
        embeddingCost?: number;
        completionCost?: number;
        promptCost?: number;
        frameworkCosts?: CostBreakdown['frameworkCosts'];
        mongoCosts?: CostBreakdown['mongoCosts'];
    }, currency?: string): CostBreakdown;
    /**
     * Create framework metadata for different frameworks
     */
    static createFrameworkMetadata(frameworkName: FrameworkMetadata['frameworkName'], frameworkData: Partial<FrameworkMetadata>): FrameworkMetadata;
    /**
     * Format trace duration for display
     */
    static formatDuration(milliseconds: number): string;
    /**
     * Format cost for display
     */
    static formatCost(cost: number, currency?: string): string;
    /**
     * Calculate trace health score based on performance and errors
     */
    static calculateTraceHealth(trace: AgentTrace): {
        score: number;
        factors: {
            performance: number;
            errors: number;
            completion: number;
        };
    };
    /**
     * Extract key metrics from a trace for dashboard display
     */
    static extractKeyMetrics(trace: AgentTrace): {
        duration: string;
        cost: string;
        tokens: number;
        errors: number;
        health: number;
        framework: string;
        operation: string;
    };
}
/**
 * Tracing decorators for automatic method tracing
 */
export declare class TracingDecorators {
    /**
     * Decorator to automatically trace method execution
     */
    static trace(stepType: AgentStep['stepType']): (target: any, propertyName: string, descriptor: PropertyDescriptor) => void;
}
//# sourceMappingURL=index.d.ts.map