"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HybridSearchEngine = exports.DefaultEmbeddingProvider = void 0;
const MongoEmbeddingProvider_1 = require("../persistance/MongoEmbeddingProvider");
const OpenAIEmbeddingProvider_1 = require("../embeddings/OpenAIEmbeddingProvider");
// Fallback embedding provider (mock implementation for development/testing)
class DefaultEmbeddingProvider {
    async generateEmbedding(text) {
        console.warn(`Using fallback mock embedding provider for: ${text.substring(0, 50)}...`);
        console.warn('WARNING: This is a mock implementation. For production, configure a real embedding provider.');
        // Mock implementation - generates consistent but meaningless embeddings
        return Array(1536).fill(0).map(() => Math.random() * 2 - 1); // Random values between -1 and 1
    }
}
exports.DefaultEmbeddingProvider = DefaultEmbeddingProvider;
/**
 * Advanced Hybrid Search Engine
 * Combines vector similarity search with full-text search for optimal relevance
 */
class HybridSearchEngine {
    constructor(db, embeddingProvider, collectionName = 'vector_embeddings') {
        this.db = db;
        // Use production-ready OpenAI embedding provider by default
        this.embeddingProvider = embeddingProvider || this.createDefaultEmbeddingProvider();
        this.embeddingStore = new MongoEmbeddingProvider_1.MongoEmbeddingProvider(db, collectionName, 'vector_search_index');
    }
    /**
     * Perform hybrid search combining vector and text search
     */
    async search(query, filters = {}, options = {}) {
        const { limit = 20, vector_weight = 0.7, text_weight = 0.3, vector_index = 'vector_search_index', text_index = 'text_search_index', include_embeddings = false, explain_relevance = true } = options;
        try {
            // Generate query embedding
            const queryEmbedding = await this.embeddingProvider.generateEmbedding(query);
            // Build filter conditions
            const filterConditions = this.buildFilterConditions(filters);
            // Execute hybrid search pipeline
            const results = await this.executeHybridSearchPipeline(query, queryEmbedding, filterConditions, {
                limit,
                vector_weight,
                text_weight,
                vector_index,
                text_index,
                include_embeddings,
                explain_relevance
            });
            return results;
        }
        catch (error) {
            console.error('Hybrid search failed:', error);
            // Fallback to text-only search
            return await this.fallbackTextSearch(query, filters, options);
        }
    }
    /**
     * Execute the hybrid search aggregation pipeline
     */
    async executeHybridSearchPipeline(query, queryEmbedding, filterConditions, options) {
        const collection = this.db.collection('vector_embeddings');
        const pipeline = [
            // Stage 1: Vector similarity search
            {
                $vectorSearch: {
                    index: options.vector_index,
                    queryVector: queryEmbedding,
                    path: 'embedding.values',
                    numCandidates: Math.max(options.limit * 10, 150),
                    limit: Math.max(options.limit * 2, 50),
                    filter: filterConditions,
                },
            },
            {
                $addFields: {
                    vector_score: { $meta: 'vectorSearchScore' },
                },
            },
            // Stage 2: Text search (if text index exists)
            {
                $search: {
                    index: options.text_index,
                    compound: {
                        must: [
                            {
                                text: {
                                    query: query,
                                    path: ['content.text', 'content.summary'],
                                },
                            },
                        ],
                        filter: [filterConditions],
                    },
                },
            },
            {
                $addFields: {
                    text_score: { $meta: 'searchScore' },
                },
            },
            // Stage 3: Combine scores with weights
            {
                $addFields: {
                    combined_score: {
                        $add: [
                            { $multiply: ['$vector_score', options.vector_weight] },
                            { $multiply: ['$text_score', options.text_weight] },
                        ],
                    },
                },
            },
            // Stage 4: Sort by combined score
            { $sort: { combined_score: -1 } },
            // Stage 5: Limit results
            { $limit: options.limit },
            // Stage 6: Project final results
            {
                $project: {
                    _id: 1,
                    embedding_id: 1,
                    content: 1,
                    metadata: 1,
                    vector_score: 1,
                    text_score: 1,
                    combined_score: 1,
                    ...(options.include_embeddings && { 'embedding.values': 1 }),
                    ...(options.explain_relevance && {
                        relevance_explanation: {
                            $concat: [
                                'Vector similarity: ', { $toString: { $round: ['$vector_score', 3] } },
                                ', Text relevance: ', { $toString: { $round: ['$text_score', 3] } },
                                ', Combined score: ', { $toString: { $round: ['$combined_score', 3] } }
                            ]
                        }
                    })
                },
            },
        ];
        const results = await collection.aggregate(pipeline).toArray();
        return results.map(doc => ({
            _id: doc._id.toString(),
            embedding_id: doc.embedding_id,
            content: doc.content,
            metadata: doc.metadata,
            scores: {
                vector_score: doc.vector_score || 0,
                text_score: doc.text_score || 0,
                combined_score: doc.combined_score || 0,
            },
            relevance_explanation: doc.relevance_explanation || 'No explanation available'
        }));
    }
    /**
     * Fallback to text-only search when vector search fails
     */
    async fallbackTextSearch(query, filters, options) {
        console.log('Falling back to text-only search');
        const collection = this.db.collection('vector_embeddings');
        const filterConditions = this.buildFilterConditions(filters);
        try {
            const pipeline = [
                {
                    $search: {
                        index: options.text_index || 'text_search_index',
                        compound: {
                            must: [
                                {
                                    text: {
                                        query: query,
                                        path: ['content.text', 'content.summary'],
                                    },
                                },
                            ],
                            filter: [filterConditions],
                        },
                    },
                },
                {
                    $addFields: {
                        text_score: { $meta: 'searchScore' },
                    },
                },
                { $sort: { text_score: -1 } },
                { $limit: options.limit || 20 },
                {
                    $project: {
                        _id: 1,
                        embedding_id: 1,
                        content: 1,
                        metadata: 1,
                        text_score: 1,
                    },
                },
            ];
            const results = await collection.aggregate(pipeline).toArray();
            return results.map(doc => ({
                _id: doc._id.toString(),
                embedding_id: doc.embedding_id,
                content: doc.content,
                metadata: doc.metadata,
                scores: {
                    vector_score: 0,
                    text_score: doc.text_score || 0,
                    combined_score: doc.text_score || 0,
                },
                relevance_explanation: `Text-only search (vector search unavailable): ${doc.text_score?.toFixed(3) || 'N/A'}`
            }));
        }
        catch (error) {
            console.error('Text search also failed:', error);
            return [];
        }
    }
    /**
     * Build MongoDB filter conditions from search filters
     */
    buildFilterConditions(filters) {
        const conditions = {};
        if (filters.source_type) {
            conditions.source_type = filters.source_type;
        }
        if (filters.agent_id) {
            conditions.agent_id = filters.agent_id;
        }
        if (filters.created_after || filters.created_before) {
            conditions.created_at = {};
            if (filters.created_after) {
                conditions.created_at.$gte = filters.created_after;
            }
            if (filters.created_before) {
                conditions.created_at.$lte = filters.created_before;
            }
        }
        if (filters.min_confidence) {
            conditions['content.confidence'] = { $gte: filters.min_confidence };
        }
        if (filters.metadata_filters) {
            for (const [key, value] of Object.entries(filters.metadata_filters)) {
                conditions[`metadata.${key}`] = value;
            }
        }
        return conditions;
    }
    /**
     * Semantic search using only vector similarity
     */
    async semanticSearch(query, filters = {}, limit = 20) {
        try {
            const queryEmbedding = await this.embeddingProvider.generateEmbedding(query);
            const filterConditions = this.buildFilterConditions(filters);
            const collection = this.db.collection('vector_embeddings');
            const pipeline = [
                {
                    $vectorSearch: {
                        index: 'vector_search_index',
                        queryVector: queryEmbedding,
                        path: 'embedding.values',
                        numCandidates: Math.max(limit * 10, 150),
                        limit,
                        filter: filterConditions,
                    },
                },
                {
                    $addFields: {
                        vector_score: { $meta: 'vectorSearchScore' },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        embedding_id: 1,
                        content: 1,
                        metadata: 1,
                        vector_score: 1,
                    },
                },
            ];
            const results = await collection.aggregate(pipeline).toArray();
            return results.map(doc => ({
                _id: doc._id.toString(),
                embedding_id: doc.embedding_id,
                content: doc.content,
                metadata: doc.metadata,
                scores: {
                    vector_score: doc.vector_score || 0,
                    text_score: 0,
                    combined_score: doc.vector_score || 0,
                },
                relevance_explanation: `Semantic similarity: ${doc.vector_score?.toFixed(3) || 'N/A'}`
            }));
        }
        catch (error) {
            console.error('Semantic search failed:', error);
            return [];
        }
    }
    /**
     * Full-text search using only text matching
     */
    async textSearch(query, filters = {}, limit = 20) {
        return await this.fallbackTextSearch(query, filters, { limit });
    }
    /**
     * Get search suggestions based on query
     */
    async getSuggestions(partialQuery, limit = 5) {
        try {
            const collection = this.db.collection('vector_embeddings');
            const pipeline = [
                {
                    $search: {
                        index: 'text_search_index',
                        autocomplete: {
                            query: partialQuery,
                            path: 'content.text',
                        },
                    },
                },
                { $limit: limit },
                {
                    $project: {
                        suggestion: { $substr: ['$content.text', 0, 100] },
                    },
                },
            ];
            const results = await collection.aggregate(pipeline).toArray();
            return results.map(doc => doc.suggestion);
        }
        catch (error) {
            console.error('Failed to get suggestions:', error);
            return [];
        }
    }
    /**
     * Analyze search performance and provide insights
     */
    async analyzeSearchPerformance(query, filters = {}) {
        const startTime = Date.now();
        try {
            const [vectorResults, textResults, hybridResults] = await Promise.all([
                this.semanticSearch(query, filters, 100),
                this.textSearch(query, filters, 100),
                this.search(query, filters, { limit: 100 })
            ]);
            const performance_ms = Date.now() - startTime;
            const recommendations = [];
            if (vectorResults.length === 0) {
                recommendations.push('Consider improving embedding quality or expanding vector index');
            }
            if (textResults.length === 0) {
                recommendations.push('Consider improving text content or expanding text index');
            }
            if (hybridResults.length < Math.max(vectorResults.length, textResults.length)) {
                recommendations.push('Hybrid search may need weight adjustment');
            }
            if (performance_ms > 1000) {
                recommendations.push('Search performance is slow - consider index optimization');
            }
            return {
                query,
                total_candidates: Math.max(vectorResults.length, textResults.length),
                vector_results: vectorResults.length,
                text_results: textResults.length,
                hybrid_results: hybridResults.length,
                performance_ms,
                recommendations
            };
        }
        catch (error) {
            console.error('Search performance analysis failed:', error);
            return {
                query,
                total_candidates: 0,
                vector_results: 0,
                text_results: 0,
                hybrid_results: 0,
                performance_ms: Date.now() - startTime,
                recommendations: ['Search analysis failed - check index configuration']
            };
        }
    }
    /**
     * Create default embedding provider with fallback to mock
     */
    createDefaultEmbeddingProvider() {
        // Try to create OpenAI embedding provider if API key is available
        const apiKey = process.env.OPENAI_API_KEY;
        if (apiKey && apiKey.trim() !== '') {
            try {
                console.log('Using OpenAI embedding provider for production-ready embeddings');
                return new OpenAIEmbeddingProvider_1.OpenAIEmbeddingProvider({
                    apiKey,
                    model: 'text-embedding-3-small'
                });
            }
            catch (error) {
                console.warn('Failed to initialize OpenAI embedding provider:', error);
                console.warn('Falling back to mock embedding provider');
            }
        }
        else {
            console.warn('No OPENAI_API_KEY found in environment variables');
            console.warn('Using mock embedding provider - not suitable for production');
        }
        // Fallback to mock provider
        return new DefaultEmbeddingProvider();
    }
}
exports.HybridSearchEngine = HybridSearchEngine;
