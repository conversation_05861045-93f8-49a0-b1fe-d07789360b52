import { Db } from 'mongodb';
export interface EmbeddingProvider {
    generateEmbedding(text: string): Promise<number[]>;
}
export declare class DefaultEmbeddingProvider implements EmbeddingProvider {
    generateEmbedding(text: string): Promise<number[]>;
}
export interface HybridSearchResult {
    _id: string;
    embedding_id: string;
    content: {
        text: string;
        summary?: string;
    };
    metadata: Record<string, any>;
    scores: {
        vector_score: number;
        text_score: number;
        combined_score: number;
    };
    relevance_explanation: string;
}
export interface SearchFilters {
    source_type?: string;
    agent_id?: string;
    created_after?: Date;
    created_before?: Date;
    metadata_filters?: Record<string, any>;
    min_confidence?: number;
}
export interface SearchOptions {
    limit?: number;
    vector_weight?: number;
    text_weight?: number;
    vector_index?: string;
    text_index?: string;
    include_embeddings?: boolean;
    explain_relevance?: boolean;
}
/**
 * Advanced Hybrid Search Engine
 * Combines vector similarity search with full-text search for optimal relevance
 */
export declare class HybridSearchEngine {
    private db;
    private embeddingProvider;
    private embeddingStore;
    constructor(db: Db, embeddingProvider?: EmbeddingProvider, collectionName?: string);
    /**
     * Perform hybrid search combining vector and text search
     */
    search(query: string, filters?: SearchFilters, options?: SearchOptions): Promise<HybridSearchResult[]>;
    /**
     * Execute the hybrid search aggregation pipeline
     */
    private executeHybridSearchPipeline;
    /**
     * Fallback to text-only search when vector search fails
     */
    private fallbackTextSearch;
    /**
     * Build MongoDB filter conditions from search filters
     */
    private buildFilterConditions;
    /**
     * Semantic search using only vector similarity
     */
    semanticSearch(query: string, filters?: SearchFilters, limit?: number): Promise<HybridSearchResult[]>;
    /**
     * Full-text search using only text matching
     */
    textSearch(query: string, filters?: SearchFilters, limit?: number): Promise<HybridSearchResult[]>;
    /**
     * Get search suggestions based on query
     */
    getSuggestions(partialQuery: string, limit?: number): Promise<string[]>;
    /**
     * Analyze search performance and provide insights
     */
    analyzeSearchPerformance(query: string, filters?: SearchFilters): Promise<{
        query: string;
        total_candidates: number;
        vector_results: number;
        text_results: number;
        hybrid_results: number;
        performance_ms: number;
        recommendations: string[];
    }>;
    /**
     * Create default embedding provider with fallback to mock
     */
    private createDefaultEmbeddingProvider;
}
//# sourceMappingURL=hybridSearch.d.ts.map