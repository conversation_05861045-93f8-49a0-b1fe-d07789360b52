"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeStreamManager = void 0;
class ChangeStreamManager {
    constructor(db, collectionName, pipeline, handler) {
        this.db = db;
        this.collectionName = collectionName;
        this.pipeline = pipeline;
        this.handler = handler;
    }
    async start() {
        const collection = this.db.collection(this.collectionName);
        this.changeStream = collection.watch(this.pipeline);
        this.changeStream.on('change', this.handler);
        this.changeStream.on('error', (error) => {
            console.error(`Change stream error on collection ${this.collectionName}:`, error);
            // In a production system, you would add reconnection logic here.
        });
        console.log(`Watching for changes on ${this.collectionName}...`);
    }
    async stop() {
        if (this.changeStream) {
            await this.changeStream.close();
            console.log(`Stopped watching for changes on ${this.collectionName}.`);
        }
    }
}
exports.ChangeStreamManager = ChangeStreamManager;
