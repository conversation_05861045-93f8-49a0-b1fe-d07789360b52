"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowChangeStream = void 0;
const ChangeStreamManager_1 = require("./ChangeStreamManager");
const utils_1 = require("@mongodb-ai/utils");
class WorkflowChangeStream {
    constructor(db) {
        const pipeline = [
            {
                $match: {
                    'updateDescription.updatedFields.status': { $exists: true }
                }
            }
        ];
        this.changeStreamManager = new ChangeStreamManager_1.ChangeStreamManager(db, 'agent_workflows', pipeline, this.handleWorkflowChange.bind(this));
    }
    async handleWorkflowChange(change) {
        utils_1.logger.info('Workflow status changed:', {
            workflow_id: change.documentKey._id,
            status: change.updateDescription.updatedFields.status
        });
    }
    async start() {
        await this.changeStreamManager.start();
    }
    async stop() {
        await this.changeStreamManager.stop();
    }
}
exports.WorkflowChangeStream = WorkflowChangeStream;
