/**
 * @file VercelAIAdapter - Integration adapter for Vercel AI SDK
 *
 * This adapter integrates the Universal AI Brain with Vercel AI SDK,
 * providing intelligent memory, context injection, and MongoDB-powered features
 * to AI SDK applications.
 */
import { BaseFrameworkAdapter } from './BaseFrameworkAdapter';
import { UniversalAIBrain } from '../brain/UniversalAIBrain';
import { AdapterConfig } from '../types';
import { TracingEngine } from '../tracing';
interface AISDKMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    id?: string;
}
interface AISDKGenerateOptions {
    model?: any;
    messages: AISDKMessage[];
    temperature?: number;
    maxTokens?: number;
    tools?: any[];
    toolChoice?: any;
}
interface AISDKStreamOptions extends AISDKGenerateOptions {
    onFinish?: (result: any) => void;
    onChunk?: (chunk: any) => void;
}
export interface VercelAIAdapterConfig extends AdapterConfig {
    enableStreamEnhancement?: boolean;
    enableToolIntegration?: boolean;
    enableChatMemory?: boolean;
}
/**
 * VercelAIAdapter - Integrates Universal AI Brain with Vercel AI SDK
 *
 * Features:
 * - Enhances generateText and streamText with MongoDB context
 * - Provides intelligent memory for chat applications
 * - Integrates MongoDB vector search as tools
 * - Supports both streaming and non-streaming responses
 */
export declare class VercelAIAdapter extends BaseFrameworkAdapter<any> {
    readonly frameworkName = "Vercel AI SDK";
    readonly version = "1.0.0";
    private originalGenerateText?;
    private originalStreamText?;
    private originalGenerateObject?;
    private tracingEngine?;
    constructor(config?: Partial<VercelAIAdapterConfig>);
    /**
     * Integrate with Vercel AI SDK
     */
    integrate(brain: UniversalAIBrain, tracingEngine?: TracingEngine): Promise<any>;
    /**
     * Create enhanced SDK with all AI SDK functions
     */
    createEnhancedSDK(): {
        generateText: (options: AISDKGenerateOptions & {
            conversationId?: string;
        }) => Promise<any>;
        streamText: (options: AISDKStreamOptions & {
            conversationId?: string;
        }) => Promise<any>;
        generateObject: (options: AISDKGenerateOptions & {
            schema: any;
            conversationId?: string;
        }) => Promise<any>;
    };
    /**
     * Check if adapter is ready
     */
    isReady(): boolean;
    /**
     * Get adapter configuration
     */
    getConfig(): VercelAIAdapterConfig;
    /**
     * Cleanup adapter resources
     */
    cleanup(): Promise<void>;
    /**
     * Create enhanced generateText function with MongoDB context injection and enterprise tracing
     */
    private createEnhancedGenerateText;
    /**
     * Create enhanced streamText function with MongoDB context injection and enterprise tracing
     */
    private createEnhancedStreamText;
    /**
     * Create enhanced generateObject function with MongoDB context injection and enterprise tracing
     */
    private createEnhancedGenerateObject;
    /**
     * Create MongoDB-powered tools for AI SDK
     */
    private createMongoDBTools;
    /**
     * Enhanced framework integration method
     */
    enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
    /**
     * Validate that the adapter is working with REAL Vercel AI SDK
     */
    validateRealIntegration(): Promise<boolean>;
    private extractLatestUserMessage;
    private calculateCost;
    private replaceUserMessage;
    private callAISDKGenerateText;
    private callAISDKStreamText;
    private callAISDKGenerateObject;
}
export {};
//# sourceMappingURL=VercelAIAdapter.d.ts.map