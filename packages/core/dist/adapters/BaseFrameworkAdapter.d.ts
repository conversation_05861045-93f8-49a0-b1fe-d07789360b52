/**
 * @file BaseFrameworkAdapter - Abstract base class for all framework adapters
 *
 * This provides the foundation that all framework-specific adapters will extend.
 * It ensures consistent behavior and provides common functionality across all
 * framework integrations.
 */
import { UniversalAIBrain } from '../brain/UniversalAIBrain';
import { TracingEngine } from '../tracing';
import { FrameworkAdapter, FrameworkCapabilities, AdapterConfig, EnhancementStrategy } from '../types';
export declare abstract class BaseFrameworkAdapter<T> implements FrameworkAdapter<T> {
    abstract readonly frameworkName: string;
    abstract readonly version: string;
    protected brain: UniversalAIBrain | null;
    protected config: AdapterConfig;
    protected isInitialized: boolean;
    constructor(config?: Partial<AdapterConfig>);
    /**
     * Abstract methods that each framework adapter must implement
     */
    abstract integrate(brain: UniversalAIBrain, tracingEngine?: TracingEngine): T;
    abstract enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
    abstract getCapabilities(): FrameworkCapabilities;
    /**
     * Validate framework compatibility
     */
    validateCompatibility(): boolean;
    /**
     * Initialize the adapter with a brain instance
     */
    protected initialize(brain: UniversalAIBrain): Promise<void>;
    /**
     * Enhanced prompt generation with brain intelligence
     */
    protected enhancePromptWithBrain(prompt: string, options?: {
        conversationId?: string;
        enhancementStrategy?: EnhancementStrategy;
        maxContextItems?: number;
    }): Promise<string>;
    /**
     * Store interaction for learning and memory
     */
    protected storeInteractionWithBrain(userMessage: string, assistantResponse: string, conversationId: string, metadata?: Record<string, any>): Promise<void>;
    /**
     * Get intelligent tools from the brain
     */
    protected getIntelligentTools(): any[];
    /**
     * Create conversation context for the brain
     */
    protected createConversationContext(conversationId: string): Record<string, any>;
    /**
     * Handle errors gracefully with fallback behavior
     */
    protected handleBrainError(error: any, fallbackValue: any): any;
    /**
     * Get adapter statistics
     */
    getAdapterStats(): any;
    /**
     * Check if the framework is available in the current environment
     */
    abstract checkFrameworkAvailability(): boolean;
    /**
     * Check if the current framework version is compatible
     */
    protected abstract checkVersionCompatibility(): boolean;
    /**
     * Setup framework-specific integration
     */
    protected abstract setupFrameworkIntegration(): Promise<void>;
    /**
     * Create framework-specific intelligent tools
     */
    protected abstract createIntelligentTools(): any[];
    /**
     * Validate configuration
     */
    protected validateConfig(): void;
    /**
     * Generate unique conversation ID
     */
    protected generateConversationId(): string;
    /**
     * Format metadata for storage
     */
    protected formatMetadata(metadata: Record<string, any>): Record<string, any>;
    /**
     * Check if brain is available and initialized
     */
    protected ensureBrainAvailable(): void;
    /**
     * Log adapter activity
     */
    protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void;
}
//# sourceMappingURL=BaseFrameworkAdapter.d.ts.map