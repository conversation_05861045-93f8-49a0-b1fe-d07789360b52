/**
 * @file LangChainJSAdapter - Integration adapter for LangChain.js framework
 *
 * This adapter integrates the Universal AI Brain with LangChain.js,
 * providing intelligent memory, context injection, and MongoDB-powered features
 * to LangChain applications.
 */
import { BaseFrameworkAdapter } from './BaseFrameworkAdapter';
import { UniversalAIBrain } from '../brain/UniversalAIBrain';
import { FrameworkCapabilities, AdapterConfig } from '../types';
export interface LangChainJSAdapterConfig extends AdapterConfig {
    enableVectorStoreReplacement?: boolean;
    enableMemoryReplacement?: boolean;
    enableChainEnhancement?: boolean;
}
/**
 * LangChainJSAdapter - Integrates Universal AI Brain with LangChain.js
 *
 * Features:
 * - Replaces LangChain memory with MongoDB-powered memory
 * - Provides MongoDB vector store implementation
 * - Enhances chains with intelligent context injection
 * - Integrates with LangChain agents and tools
 */
export declare class LangChainJSAdapter extends BaseFrameworkAdapter<any> {
    readonly frameworkName = "LangChain.js";
    readonly version = "1.0.0";
    constructor(config?: Partial<LangChainJSAdapterConfig>);
    /**
     * Integrate with LangChain.js framework
     */
    integrate(brain: UniversalAIBrain): Promise<any>;
    /**
     * Create MongoDB-powered vector store for LangChain
     */
    private createMongoDBVectorStore;
    /**
     * Create MongoDB-powered memory for LangChain
     */
    private createMongoDBMemory;
    /**
     * Create enhanced chat model with MongoDB context injection
     */
    private createEnhancedChatModel;
    /**
     * Create MongoDB retriever for LangChain
     */
    private createMongoDBRetriever;
    /**
     * Create MongoDB tools for LangChain agents
     */
    private createMongoDBTools;
    protected checkFrameworkAvailability(): boolean;
    protected checkVersionCompatibility(): boolean;
    protected setupFrameworkIntegration(): Promise<void>;
    protected createIntelligentTools(): any[];
    private extractUserMessage;
    private replaceUserMessage;
    /**
     * Get framework capabilities
     */
    getCapabilities(): FrameworkCapabilities;
    /**
     * Enhanced framework integration method
     */
    enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
    /**
     * Validate that the adapter is working with REAL LangChain.js framework
     */
    validateRealIntegration(): Promise<boolean>;
}
//# sourceMappingURL=LangChainJSAdapter.d.ts.map