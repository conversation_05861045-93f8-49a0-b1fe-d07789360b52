/**
 * @file FrameworkAdapterManager.ts - Manages multiple framework adapters
 *
 * This class provides a centralized way to manage multiple framework adapters,
 * enabling the Universal AI Brain to work with multiple TypeScript AI frameworks
 * simultaneously while maintaining optimal performance and resource usage.
 */
import { UniversalAIBrain } from '../brain/UniversalAIBrain';
import { BaseFrameworkAdapter } from './BaseFrameworkAdapter';
import { FrameworkCapabilities, AdapterConfig } from '../types';
/**
 * Configuration for the Framework Adapter Manager
 */
export interface FrameworkAdapterManagerConfig {
    /** Whether to auto-detect available frameworks */
    autoDetectFrameworks?: boolean;
    /** Maximum number of adapters to manage simultaneously */
    maxAdapters?: number;
    /** Whether to enable performance monitoring across adapters */
    enablePerformanceMonitoring?: boolean;
    /** Whether to enable cross-adapter learning */
    enableCrossAdapterLearning?: boolean;
    /** Default configuration for all adapters */
    defaultAdapterConfig?: AdapterConfig;
    /** Framework-specific configurations */
    frameworkConfigs?: {
        [frameworkName: string]: AdapterConfig;
    };
}
/**
 * Information about a managed adapter
 */
export interface ManagedAdapterInfo {
    /** The adapter instance */
    adapter: BaseFrameworkAdapter<any>;
    /** Framework name */
    frameworkName: string;
    /** Whether the adapter is currently active */
    isActive: boolean;
    /** Integration timestamp */
    integratedAt: Date;
    /** Last activity timestamp */
    lastActivity: Date;
    /** Performance metrics */
    metrics: {
        totalCalls: number;
        averageResponseTime: number;
        errorCount: number;
        successRate: number;
    };
    /** Framework capabilities */
    capabilities: FrameworkCapabilities;
}
/**
 * Manager statistics
 */
export interface ManagerStats {
    /** Total number of managed adapters */
    totalAdapters: number;
    /** Number of active adapters */
    activeAdapters: number;
    /** Supported frameworks */
    supportedFrameworks: string[];
    /** Overall performance metrics */
    overallMetrics: {
        totalCalls: number;
        averageResponseTime: number;
        totalErrors: number;
        overallSuccessRate: number;
    };
    /** Brain health status */
    brainHealth: boolean;
    /** Memory usage */
    memoryUsage: {
        adapters: number;
        brain: number;
        total: number;
    };
}
/**
 * Framework information for detection
 */
export interface FrameworkInfo {
    name: string;
    available: boolean;
    compatible: boolean;
    realIntegration: boolean;
    capabilities: FrameworkCapabilities;
    adapter: BaseFrameworkAdapter<any>;
    packageName: string;
}
/**
 * Framework detection result
 */
export interface FrameworkDetectionResult {
    detectedFrameworks: FrameworkInfo[];
    suggestions: string[];
    recommendedAdapter: string | null;
}
/**
 * Framework Adapter Manager
 *
 * Manages multiple framework adapters and provides a unified interface
 * for integrating the Universal AI Brain with multiple TypeScript AI frameworks.
 */
export declare class FrameworkAdapterManager {
    private brain;
    private adapters;
    private config;
    private isInitialized;
    constructor(config?: FrameworkAdapterManagerConfig);
    /**
     * Initialize the manager with a Universal AI Brain
     */
    initialize(brain: UniversalAIBrain): Promise<void>;
    /**
     * Register a framework adapter
     */
    registerAdapter(adapter: BaseFrameworkAdapter<any>, config?: AdapterConfig): Promise<boolean>;
    /**
     * Get an adapter by framework name
     */
    getAdapter(frameworkName: string): BaseFrameworkAdapter<any> | null;
    /**
     * Get all registered adapters
     */
    getAllAdapters(): ManagedAdapterInfo[];
    /**
     * Get adapters by capability
     */
    getAdaptersByCapability(capability: keyof FrameworkCapabilities): ManagedAdapterInfo[];
    /**
     * Auto-detect and register available frameworks
     */
    private autoDetectAndRegisterFrameworks;
    /**
     * Check if a framework package is available
     */
    private checkFrameworkAvailability;
    /**
     * Create a configured adapter instance
     */
    private createConfiguredAdapter;
    /**
     * Update adapter metrics
     */
    updateAdapterMetrics(frameworkName: string, responseTime: number, success: boolean): void;
    /**
     * Get manager statistics
     */
    getStats(): Promise<ManagerStats>;
    /**
     * Deactivate an adapter
     */
    deactivateAdapter(frameworkName: string): Promise<boolean>;
    /**
     * Cleanup all adapters and resources
     */
    cleanup(): Promise<void>;
    /**
     * Check if manager is ready
     */
    isReady(): boolean;
    /**
     * Get supported frameworks
     */
    getSupportedFrameworks(): string[];
    /**
     * Enhanced framework detection with detailed information
     */
    detectAvailableFrameworks(): Promise<FrameworkDetectionResult>;
    /**
     * Get installation command for a framework
     */
    private getInstallCommand;
    /**
     * Get recommended adapter based on detected frameworks
     */
    private getRecommendedAdapter;
}
//# sourceMappingURL=FrameworkAdapterManager.d.ts.map