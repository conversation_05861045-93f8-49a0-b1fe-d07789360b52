"use strict";
/**
 * @file BaseFrameworkAdapter - Abstract base class for all framework adapters
 *
 * This provides the foundation that all framework-specific adapters will extend.
 * It ensures consistent behavior and provides common functionality across all
 * framework integrations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseFrameworkAdapter = void 0;
const types_1 = require("../types");
class BaseFrameworkAdapter {
    constructor(config) {
        this.brain = null;
        this.isInitialized = false;
        this.config = {
            enableMemoryInjection: true,
            enableContextEnhancement: true,
            enableToolIntegration: true,
            maxContextItems: 5,
            enhancementStrategy: 'hybrid',
            ...config
        };
    }
    /**
     * Validate framework compatibility
     */
    validateCompatibility() {
        try {
            // Check if framework is available
            const isAvailable = this.checkFrameworkAvailability();
            if (!isAvailable) {
                console.warn(`⚠️ ${this.frameworkName} is not available in this environment`);
                return false;
            }
            // Check version compatibility
            const isVersionCompatible = this.checkVersionCompatibility();
            if (!isVersionCompatible) {
                console.warn(`⚠️ ${this.frameworkName} version ${this.version} may not be fully compatible`);
                return false;
            }
            return true;
        }
        catch (error) {
            console.error(`❌ Error validating ${this.frameworkName} compatibility:`, error);
            return false;
        }
    }
    /**
     * Initialize the adapter with a brain instance
     */
    async initialize(brain) {
        if (this.isInitialized) {
            return;
        }
        try {
            this.brain = brain;
            await this.setupFrameworkIntegration();
            this.isInitialized = true;
            console.log(`✅ ${this.frameworkName} adapter initialized successfully`);
        }
        catch (error) {
            throw new types_1.FrameworkIntegrationError(this.frameworkName, `Failed to initialize adapter: ${error.message}`, { error: error.message });
        }
    }
    /**
     * Enhanced prompt generation with brain intelligence
     */
    async enhancePromptWithBrain(prompt, options) {
        if (!this.brain || !this.config.enableContextEnhancement) {
            return prompt;
        }
        try {
            const enhancedPrompt = await this.brain.enhancePrompt(prompt, {
                frameworkType: this.frameworkName,
                conversationId: options?.conversationId,
                maxContextItems: options?.maxContextItems || this.config.maxContextItems,
                enhancementStrategy: options?.enhancementStrategy || this.config.enhancementStrategy
            });
            return enhancedPrompt.enhancedPrompt;
        }
        catch (error) {
            console.error(`Error enhancing prompt with brain:`, error);
            return prompt; // Fallback to original prompt
        }
    }
    /**
     * Store interaction for learning and memory
     */
    async storeInteractionWithBrain(userMessage, assistantResponse, conversationId, metadata) {
        if (!this.brain || !this.config.enableMemoryInjection) {
            return;
        }
        try {
            await this.brain.storeInteraction({
                conversationId,
                userMessage,
                assistantResponse,
                context: [], // Context will be populated by the brain
                metadata: {
                    framework: this.frameworkName,
                    adapterVersion: this.version,
                    ...metadata
                },
                framework: this.frameworkName
            });
        }
        catch (error) {
            console.error(`Error storing interaction with brain:`, error);
            // Don't throw - this shouldn't break the main flow
        }
    }
    /**
     * Get intelligent tools from the brain
     */
    getIntelligentTools() {
        if (!this.brain || !this.config.enableToolIntegration) {
            return [];
        }
        // Return framework-specific intelligent tools
        return this.createIntelligentTools();
    }
    /**
     * Create conversation context for the brain
     */
    createConversationContext(conversationId) {
        return {
            conversationId,
            framework: this.frameworkName,
            adapterVersion: this.version,
            config: this.config,
            timestamp: new Date()
        };
    }
    /**
     * Handle errors gracefully with fallback behavior
     */
    handleBrainError(error, fallbackValue) {
        if (error instanceof types_1.BrainError) {
            console.error(`Brain error in ${this.frameworkName} adapter:`, error.message);
        }
        else {
            console.error(`Unexpected error in ${this.frameworkName} adapter:`, error);
        }
        return fallbackValue;
    }
    /**
     * Get adapter statistics
     */
    getAdapterStats() {
        return {
            frameworkName: this.frameworkName,
            version: this.version,
            isInitialized: this.isInitialized,
            config: this.config,
            capabilities: this.getCapabilities(),
            brainConnected: !!this.brain
        };
    }
    // ============================================================================
    // UTILITY METHODS
    // ============================================================================
    /**
     * Validate configuration
     */
    validateConfig() {
        if (this.config.maxContextItems < 1) {
            throw new types_1.BrainError('maxContextItems must be at least 1', 'INVALID_CONFIG');
        }
        const validStrategies = ['semantic', 'hybrid', 'conversational', 'knowledge_graph'];
        if (!validStrategies.includes(this.config.enhancementStrategy)) {
            throw new types_1.BrainError(`Invalid enhancement strategy: ${this.config.enhancementStrategy}`, 'INVALID_CONFIG');
        }
    }
    /**
     * Generate unique conversation ID
     */
    generateConversationId() {
        return `${this.frameworkName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    /**
     * Format metadata for storage
     */
    formatMetadata(metadata) {
        return {
            ...metadata,
            framework: this.frameworkName,
            adapterVersion: this.version,
            timestamp: new Date(),
            source: 'framework_adapter'
        };
    }
    /**
     * Check if brain is available and initialized
     */
    ensureBrainAvailable() {
        if (!this.brain) {
            throw new types_1.FrameworkIntegrationError(this.frameworkName, 'Brain not available. Make sure to call integrate() first.');
        }
    }
    /**
     * Log adapter activity
     */
    log(level, message, data) {
        const prefix = `[${this.frameworkName}Adapter]`;
        switch (level) {
            case 'info':
                console.log(`${prefix} ${message}`, data || '');
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`, data || '');
                break;
            case 'error':
                console.error(`${prefix} ${message}`, data || '');
                break;
        }
    }
}
exports.BaseFrameworkAdapter = BaseFrameworkAdapter;
