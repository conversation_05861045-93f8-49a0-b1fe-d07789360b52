{"version": 3, "file": "FrameworkAdapterManager.d.ts", "sourceRoot": "", "sources": ["../../src/adapters/FrameworkAdapterManager.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,2BAA2B,CAAC;AAC7D,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAK9D,OAAO,EAAoB,qBAAqB,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAElF;;GAEG;AACH,MAAM,WAAW,6BAA6B;IAC5C,kDAAkD;IAClD,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAE/B,0DAA0D;IAC1D,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB,+DAA+D;IAC/D,2BAA2B,CAAC,EAAE,OAAO,CAAC;IAEtC,+CAA+C;IAC/C,0BAA0B,CAAC,EAAE,OAAO,CAAC;IAErC,6CAA6C;IAC7C,oBAAoB,CAAC,EAAE,aAAa,CAAC;IAErC,wCAAwC;IACxC,gBAAgB,CAAC,EAAE;QACjB,CAAC,aAAa,EAAE,MAAM,GAAG,aAAa,CAAC;KACxC,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,kBAAkB;IACjC,2BAA2B;IAC3B,OAAO,EAAE,oBAAoB,CAAC;IAE9B,qBAAqB;IACrB,aAAa,EAAE,MAAM,CAAC;IAEtB,8CAA8C;IAC9C,QAAQ,EAAE,OAAO,CAAC;IAElB,4BAA4B;IAC5B,YAAY,EAAE,IAAI,CAAC;IAEnB,8BAA8B;IAC9B,YAAY,EAAE,IAAI,CAAC;IAEnB,0BAA0B;IAC1B,OAAO,EAAE;QACP,UAAU,EAAE,MAAM,CAAC;QACnB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,UAAU,EAAE,MAAM,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IAEF,6BAA6B;IAC7B,YAAY,EAAE,qBAAqB,CAAC;CACrC;AAED;;GAEG;AACH,MAAM,WAAW,YAAY;IAC3B,uCAAuC;IACvC,aAAa,EAAE,MAAM,CAAC;IAEtB,gCAAgC;IAChC,cAAc,EAAE,MAAM,CAAC;IAEvB,2BAA2B;IAC3B,mBAAmB,EAAE,MAAM,EAAE,CAAC;IAE9B,kCAAkC;IAClC,cAAc,EAAE;QACd,UAAU,EAAE,MAAM,CAAC;QACnB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,WAAW,EAAE,MAAM,CAAC;QACpB,kBAAkB,EAAE,MAAM,CAAC;KAC5B,CAAC;IAEF,0BAA0B;IAC1B,WAAW,EAAE,OAAO,CAAC;IAErB,mBAAmB;IACnB,WAAW,EAAE;QACX,QAAQ,EAAE,MAAM,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;CACH;AAED;;;;;GAKG;AACH,qBAAa,uBAAuB;IAClC,OAAO,CAAC,KAAK,CAAiC;IAC9C,OAAO,CAAC,QAAQ,CAA8C;IAC9D,OAAO,CAAC,MAAM,CAAgC;IAC9C,OAAO,CAAC,aAAa,CAAS;gBAElB,MAAM,GAAE,6BAAkC;IAiBtD;;OAEG;IACG,UAAU,CAAC,KAAK,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IAUxD;;OAEG;IACG,eAAe,CACnB,OAAO,EAAE,oBAAoB,EAC7B,MAAM,CAAC,EAAE,aAAa,GACrB,OAAO,CAAC,OAAO,CAAC;IAuDnB;;OAEG;IACH,UAAU,CAAC,aAAa,EAAE,MAAM,GAAG,oBAAoB,GAAG,IAAI;IAK9D;;OAEG;IACH,cAAc,IAAI,kBAAkB,EAAE;IAItC;;OAEG;IACH,uBAAuB,CAAC,UAAU,EAAE,MAAM,qBAAqB,GAAG,kBAAkB,EAAE;IAMtF;;OAEG;YACW,+BAA+B;IAyC7C;;OAEG;YACW,0BAA0B;IASxC;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAe/B;;OAEG;IACH,oBAAoB,CAClB,aAAa,EAAE,MAAM,EACrB,YAAY,EAAE,MAAM,EACpB,OAAO,EAAE,OAAO,GACf,IAAI;IAoBP;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,YAAY,CAAC;IAqCvC;;OAEG;IACG,iBAAiB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAehE;;OAEG;IACG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAW9B;;OAEG;IACH,OAAO,IAAI,OAAO;IAIlB;;OAEG;IACH,sBAAsB,IAAI,MAAM,EAAE;IAIlC;;OAEG;IACG,yBAAyB,IAAI,OAAO,CAAC,wBAAwB,CAAC;IAmEpE;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAWzB;;OAEG;IACH,OAAO,CAAC,qBAAqB;CAkB9B"}