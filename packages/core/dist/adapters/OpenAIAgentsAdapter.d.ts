/**
 * @file OpenAIAgentsAdapter - Integration adapter for OpenAI Agents JS framework
 *
 * This adapter integrates the Universal AI Brain with OpenAI Agents JS,
 * providing intelligent memory, context injection, and MongoDB-powered features
 * to OpenAI Agents applications.
 */
import { BaseFrameworkAdapter } from './BaseFrameworkAdapter';
import { UniversalAIBrain } from '../brain/UniversalAIBrain';
import { FrameworkCapabilities, AdapterConfig } from '../types';
interface OpenAIAgent {
    name?: string;
    instructions?: string;
    model?: string;
    tools?: any[];
    run(input: string, options?: any): Promise<any>;
    stream(input: string, options?: any): Promise<any>;
}
interface OpenAITool {
    name: string;
    description: string;
    input_schema?: any;
    output_schema?: any;
    func: (args: any) => Promise<any>;
}
export interface OpenAIAgentsAdapterConfig extends AdapterConfig {
    enableAgentEnhancement?: boolean;
    enableToolIntegration?: boolean;
    enableMemoryPersistence?: boolean;
}
/**
 * OpenAIAgentsAdapter - Integrates Universal AI Brain with OpenAI Agents JS
 *
 * Features:
 * - Enhances OpenAI agents with MongoDB context injection
 * - Provides MongoDB-powered tools for agents
 * - Persists agent conversations and state in MongoDB
 * - Integrates with OpenAI Agents handoff system
 */
export declare class OpenAIAgentsAdapter extends BaseFrameworkAdapter<OpenAIAgent> {
    readonly frameworkName = "OpenAI Agents JS";
    readonly version = "1.0.0";
    private enhancedAgents;
    constructor(config?: Partial<OpenAIAgentsAdapterConfig>);
    /**
     * Integrate with OpenAI Agents JS framework
     */
    integrate(brain: UniversalAIBrain): Promise<any>;
    /**
     * Create an enhanced OpenAI agent with MongoDB superpowers
     */
    createEnhancedAgent(config: {
        name?: string;
        instructions?: string;
        model?: string;
        tools?: OpenAITool[];
        conversationId?: string;
    }): OpenAIAgent;
    /**
     * Enhance an existing OpenAI agent with MongoDB capabilities
     */
    enhanceExistingAgent(agent: OpenAIAgent, conversationId?: string): OpenAIAgent;
    /**
     * Create MongoDB-powered tools for OpenAI agents
     */
    private createMongoDBTools;
    /**
     * Create a memory tool for persistent conversations
     */
    private createMemoryTool;
    protected checkFrameworkAvailability(): boolean;
    protected checkVersionCompatibility(): boolean;
    protected setupFrameworkIntegration(): Promise<void>;
    protected createIntelligentTools(): any[];
    private callOriginalAgent;
    private callOriginalAgentStream;
    private storeStreamInteraction;
    /**
     * Get framework capabilities
     */
    getCapabilities(): FrameworkCapabilities;
    /**
     * Enhanced framework integration method
     */
    enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
    /**
     * Check if adapter is ready
     */
    isReady(): boolean;
    /**
     * Create enhanced client (alias for createEnhancedAgent for backward compatibility)
     */
    createEnhancedClient(config?: {
        name?: string;
        instructions?: string;
        model?: string;
        tools?: OpenAITool[];
        conversationId?: string;
    }): OpenAIAgent;
    /**
     * Validate that the adapter is working with REAL OpenAI Agents framework
     */
    validateRealIntegration(): Promise<boolean>;
}
export {};
//# sourceMappingURL=OpenAIAgentsAdapter.d.ts.map