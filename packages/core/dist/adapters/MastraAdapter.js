"use strict";
/**
 * @file MastraAdapter - Integration adapter for Mastra framework
 *
 * This adapter integrates the Universal AI Brain with Mastra framework,
 * providing intelligent memory, context injection, and MongoDB-powered features
 * to Mastra agents and workflows.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MastraAdapter = void 0;
const BaseFrameworkAdapter_1 = require("./BaseFrameworkAdapter");
/**
 * MastraAdapter - Integrates Universal AI Brain with Mastra framework
 *
 * Features:
 * - Replaces Mastra memory with MongoDB-powered memory
 * - Enhances agent prompts with intelligent context injection
 * - Integrates with Mastra workflows for multi-step operations
 * - Provides MongoDB vector search capabilities
 */
class MastraAdapter extends BaseFrameworkAdapter_1.BaseFrameworkAdapter {
    constructor(config) {
        super({
            enableMemoryInjection: true,
            enableContextEnhancement: true,
            enableToolIntegration: true,
            enableWorkflowIntegration: true,
            enableMemoryReplacement: true,
            enableToolEnhancement: true,
            ...config
        });
        this.frameworkName = 'Mastra';
        this.version = '1.0.0';
        this.originalMemory = new Map();
        this.enhancedAgents = new Map();
    }
    /**
     * Integrate with Mastra framework
     */
    integrate(brain, tracingEngine) {
        this.initialize(brain);
        // Return a factory function for creating enhanced Mastra agents
        return this.createEnhancedAgentFactory();
    }
    /**
     * Create an enhanced Mastra agent with MongoDB superpowers
     */
    createEnhancedAgent(config) {
        if (!this.brain) {
            throw new Error('MastraAdapter not initialized. Call integrate() first.');
        }
        const agentId = `mastra_${config.name.toLowerCase().replace(/\s+/g, '_')}`;
        // Create enhanced agent with MongoDB-powered features
        const enhancedAgent = {
            name: config.name,
            instructions: config.instructions,
            model: config.model,
            tools: config.tools,
            memory: this.createMongoDBMemory(agentId),
            // Enhanced generate method with context injection
            // Following REAL Mastra pattern: resourceId and threadId are REQUIRED for memory
            generate: async (messages, options) => {
                try {
                    // Validate REAL Mastra memory requirements
                    if (!options?.resourceId || !options?.threadId) {
                        console.warn('⚠️ Mastra memory requires both resourceId and threadId. Memory will not be used.');
                    }
                    // Extract the latest user message
                    const userMessage = this.extractUserMessage(messages);
                    // Enhance prompt with MongoDB context
                    const enhanced = await this.brain.enhancePrompt(userMessage, {
                        frameworkType: 'mastra',
                        conversationId: options?.threadId || agentId,
                        enhancementStrategy: 'hybrid'
                    });
                    // Replace user message with enhanced prompt
                    const enhancedMessages = this.replaceUserMessage(messages, enhanced.enhancedPrompt);
                    // Call REAL Mastra model with enhanced prompt
                    const result = await this.callOriginalModel(config.model, enhancedMessages, options);
                    // Store interaction for learning (following Mastra memory pattern)
                    await this.brain.storeInteraction({
                        conversationId: options?.threadId || agentId,
                        userMessage,
                        assistantResponse: result.text,
                        context: enhanced.injectedContext,
                        framework: 'mastra',
                        metadata: {
                            agentName: config.name,
                            modelUsed: config.model?.name || 'unknown',
                            enhancementStrategy: 'hybrid',
                            resourceId: options?.resourceId,
                            threadId: options?.threadId,
                            mastraMemoryEnabled: !!(options?.resourceId && options?.threadId)
                        }
                    });
                    return {
                        ...result,
                        enhancedContext: enhanced.injectedContext,
                        originalPrompt: userMessage,
                        enhancedPrompt: enhanced.enhancedPrompt
                    };
                }
                catch (error) {
                    console.error('Error in enhanced Mastra agent generate:', error);
                    // Fallback to original model
                    return this.callOriginalModel(config.model, messages, options);
                }
            },
            // Enhanced stream method with context injection
            stream: async (messages, options) => {
                try {
                    const userMessage = this.extractUserMessage(messages);
                    const enhanced = await this.brain.enhancePrompt(userMessage, {
                        frameworkType: 'mastra',
                        conversationId: options?.conversationId || agentId,
                        enhancementStrategy: 'hybrid'
                    });
                    const enhancedMessages = this.replaceUserMessage(messages, enhanced.enhancedPrompt);
                    // Call original model stream with enhanced prompt
                    const stream = await this.callOriginalModelStream(config.model, enhancedMessages, options);
                    // Store interaction (we'll collect the full response when stream completes)
                    this.storeStreamInteraction(options?.conversationId || agentId, userMessage, enhanced.injectedContext, config.name, stream);
                    return stream;
                }
                catch (error) {
                    console.error('Error in enhanced Mastra agent stream:', error);
                    return this.callOriginalModelStream(config.model, messages, options);
                }
            }
        };
        this.enhancedAgents.set(agentId, enhancedAgent);
        return enhancedAgent;
    }
    /**
     * Create MongoDB-powered memory for Mastra agents
     */
    createMongoDBMemory(agentId) {
        return {
            store: async (data) => {
                if (!this.brain)
                    return;
                try {
                    await this.brain.storeInteraction({
                        conversationId: agentId,
                        userMessage: data.input || JSON.stringify(data),
                        assistantResponse: data.output || '',
                        context: [],
                        framework: 'mastra',
                        metadata: {
                            type: 'memory_store',
                            agentId,
                            timestamp: new Date()
                        }
                    });
                }
                catch (error) {
                    console.error('Error storing Mastra memory:', error);
                }
            },
            retrieve: async (query) => {
                if (!this.brain)
                    return [];
                try {
                    const context = await this.brain.retrieveRelevantContext(query, {
                        conversationId: agentId,
                        framework: 'mastra',
                        limit: 10
                    });
                    return context.map(ctx => ({
                        content: ctx.content,
                        metadata: ctx.metadata,
                        relevanceScore: ctx.relevanceScore,
                        timestamp: ctx.timestamp
                    }));
                }
                catch (error) {
                    console.error('Error retrieving Mastra memory:', error);
                    return [];
                }
            }
        };
    }
    // Framework-specific implementation methods
    checkFrameworkAvailability() {
        try {
            // Try to import REAL Mastra framework
            require.resolve('@mastra/core');
            return true;
        }
        catch {
            console.warn('⚠️ Mastra framework not found. Install with: npm install @mastra/core');
            return false;
        }
    }
    checkVersionCompatibility() {
        try {
            const packageJson = require('@mastra/core/package.json');
            const version = packageJson.version;
            // Check if version is 0.10.0 or higher (based on docs)
            const [major, minor] = version.split('.').map(Number);
            if (major > 0 || (major === 0 && minor >= 10)) {
                return true;
            }
            console.warn(`⚠️ Mastra version ${version} detected. Version 0.10.0+ recommended.`);
            return false;
        }
        catch {
            // If we can't check version, assume it's compatible
            return true;
        }
    }
    async setupFrameworkIntegration() {
        console.log('🔌 Setting up Mastra framework integration...');
        if (!this.checkFrameworkAvailability()) {
            console.warn('⚠️ Mastra framework not available - adapter will use fallback mode');
            return;
        }
        if (!this.checkVersionCompatibility()) {
            console.warn('⚠️ Mastra framework version compatibility issue detected');
        }
        console.log('✅ Mastra integration ready');
    }
    createIntelligentTools() {
        return [
            {
                name: 'mongodb_search',
                description: 'Search MongoDB knowledge base for relevant information',
                execute: async (query) => {
                    if (!this.brain)
                        return { error: 'Brain not initialized' };
                    const results = await this.brain.retrieveRelevantContext(query, {
                        limit: 5,
                        framework: 'mastra'
                    });
                    return {
                        results: results.map(r => ({
                            content: r.content,
                            relevanceScore: r.relevanceScore,
                            source: r.source
                        }))
                    };
                }
            }
        ];
    }
    // Helper methods
    extractUserMessage(messages) {
        const userMsg = messages.find(m => m.role === 'user');
        return userMsg?.content || '';
    }
    replaceUserMessage(messages, enhancedContent) {
        return messages.map(msg => msg.role === 'user'
            ? { ...msg, content: enhancedContent }
            : msg);
    }
    async callOriginalModel(model, messages, options) {
        try {
            // Import REAL Mastra Agent class
            const { Agent } = await Promise.resolve().then(() => __importStar(require('@mastra/core')));
            // Create REAL Mastra agent
            const agent = new Agent({
                name: 'Enhanced Agent',
                instructions: 'You are an enhanced agent powered by MongoDB context.',
                model: model
            });
            // Call REAL Mastra agent.generate() method
            const result = await agent.generate(messages, options);
            return {
                text: result.text || result.content || result,
                model: model?.name || 'unknown',
                usage: result.usage || { tokens: 0 }
            };
        }
        catch (error) {
            // If Mastra not available, provide graceful fallback
            if (error.code === 'MODULE_NOT_FOUND') {
                console.warn('⚠️ Mastra framework not installed. Install with: npm install @mastra/core');
                return {
                    text: `[Fallback] Enhanced response would be generated here with MongoDB context. Install '@mastra/core' package for real integration.`,
                    model: model?.name || 'unknown',
                    usage: { tokens: 0 },
                    fallback: true
                };
            }
            throw error;
        }
    }
    async callOriginalModelStream(model, messages, options) {
        try {
            // Import REAL Mastra Agent class
            const { Agent } = await Promise.resolve().then(() => __importStar(require('@mastra/core')));
            // Create REAL Mastra agent
            const agent = new Agent({
                name: 'Enhanced Streaming Agent',
                instructions: 'You are an enhanced streaming agent powered by MongoDB context.',
                model: model
            });
            // Call REAL Mastra agent.stream() method
            const stream = await agent.stream(messages, options);
            return stream;
        }
        catch (error) {
            // If Mastra not available, provide graceful fallback
            if (error.code === 'MODULE_NOT_FOUND') {
                console.warn('⚠️ Mastra framework not installed. Install with: npm install @mastra/core');
                return {
                    async *[Symbol.asyncIterator]() {
                        yield { type: 'text', content: '[Fallback] Enhanced streaming response would be generated here with MongoDB context. ' };
                        yield { type: 'text', content: 'Install "@mastra/core" package for real integration.' };
                    }
                };
            }
            throw error;
        }
    }
    async storeStreamInteraction(conversationId, userMessage, context, agentName, stream) {
        // Collect stream response and store interaction
        // This would be implemented based on actual Mastra stream format
    }
    createEnhancedAgentFactory() {
        return {
            createAgent: (config) => this.createEnhancedAgent(config),
            adapter: this,
            brain: this.brain
        };
    }
    /**
     * Get framework capabilities
     */
    getCapabilities() {
        return {
            supportsStreaming: true,
            supportsTools: true,
            supportsMultiModal: false,
            supportsMemory: true,
            supportedModels: [
                'gpt-4o',
                'gpt-4o-mini',
                'claude-3-5-sonnet',
                'gemini-pro'
            ],
            maxContextLength: 128000
        };
    }
    /**
     * Enhanced framework integration method
     */
    enhanceWithBrain(originalFunction, brain) {
        return async (...args) => {
            try {
                // Extract agent configuration from arguments
                const config = args[0];
                if (config && typeof config === 'object') {
                    // Create enhanced agent instead of original
                    return this.createEnhancedAgent(config);
                }
                return await originalFunction(...args);
            }
            catch (error) {
                console.error('Error in enhanceWithBrain:', error);
                return await originalFunction(...args);
            }
        };
    }
    /**
     * Check if adapter is ready
     */
    isReady() {
        return this.brain !== null && this.isInitialized;
    }
    /**
     * Validate that the adapter is working with REAL Mastra framework
     */
    async validateRealIntegration() {
        try {
            // Try to import the actual Mastra classes
            const { Agent } = await Promise.resolve().then(() => __importStar(require('@mastra/core')));
            // Verify it is a constructor
            if (typeof Agent !== 'function') {
                return false;
            }
            console.log('✅ REAL Mastra framework integration validated');
            return true;
        }
        catch (error) {
            console.warn('⚠️ REAL Mastra framework not available - using fallback mode');
            return false;
        }
    }
}
exports.MastraAdapter = MastraAdapter;
