/**
 * @file MastraAdapter - Integration adapter for Mastra framework
 *
 * This adapter integrates the Universal AI Brain with Mastra framework,
 * providing intelligent memory, context injection, and MongoDB-powered features
 * to Mastra agents and workflows.
 */
import { BaseFrameworkAdapter } from './BaseFrameworkAdapter';
import { UniversalAIBrain } from '../brain/UniversalAIBrain';
import { FrameworkCapabilities, AdapterConfig } from '../types';
interface MastraAgent {
    name: string;
    instructions: string;
    model: any;
    memory?: any;
    tools?: Record<string, any>;
    generate(messages: any[], options?: any): Promise<{
        text: string;
        [key: string]: any;
    }>;
    stream(messages: any[], options?: any): Promise<any>;
}
interface MastraMemory {
    store(data: any): Promise<void>;
    retrieve(query: string): Promise<any[]>;
}
export interface MastraAdapterConfig extends AdapterConfig {
    enableWorkflowIntegration?: boolean;
    enableMemoryReplacement?: boolean;
    enableToolEnhancement?: boolean;
}
/**
 * MastraAdapter - Integrates Universal AI Brain with Mastra framework
 *
 * Features:
 * - Replaces Mastra memory with MongoDB-powered memory
 * - Enhances agent prompts with intelligent context injection
 * - Integrates with Mastra workflows for multi-step operations
 * - Provides MongoDB vector search capabilities
 */
export declare class MastraAdapter extends BaseFrameworkAdapter<MastraAgent> {
    readonly frameworkName = "Mastra";
    readonly version = "1.0.0";
    private originalMemory;
    private enhancedAgents;
    constructor(config?: Partial<MastraAdapterConfig>);
    /**
     * Integrate with Mastra framework
     */
    integrate(brain: UniversalAIBrain): Promise<MastraAgent>;
    /**
     * Create an enhanced Mastra agent with MongoDB superpowers
     */
    createEnhancedAgent(config: {
        name: string;
        instructions: string;
        model: any;
        tools?: Record<string, any>;
        memory?: MastraMemory;
    }): MastraAgent;
    /**
     * Create MongoDB-powered memory for Mastra agents
     */
    private createMongoDBMemory;
    protected checkFrameworkAvailability(): boolean;
    protected checkVersionCompatibility(): boolean;
    protected setupFrameworkIntegration(): Promise<void>;
    protected createIntelligentTools(): any[];
    private extractUserMessage;
    private replaceUserMessage;
    private callOriginalModel;
    private callOriginalModelStream;
    private storeStreamInteraction;
    private createEnhancedAgentFactory;
    /**
     * Get framework capabilities
     */
    getCapabilities(): FrameworkCapabilities;
    /**
     * Enhanced framework integration method
     */
    enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
    /**
     * Validate that the adapter is working with REAL Mastra framework
     */
    validateRealIntegration(): Promise<boolean>;
}
export {};
//# sourceMappingURL=MastraAdapter.d.ts.map