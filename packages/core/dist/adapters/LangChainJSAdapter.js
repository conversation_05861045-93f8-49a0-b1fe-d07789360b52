"use strict";
/**
 * @file LangChainJSAdapter - Integration adapter for LangChain.js framework
 *
 * This adapter integrates the Universal AI Brain with LangChain.js,
 * providing intelligent memory, context injection, and MongoDB-powered features
 * to LangChain applications.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LangChainJSAdapter = void 0;
const BaseFrameworkAdapter_1 = require("./BaseFrameworkAdapter");
/**
 * LangChainJSAdapter - Integrates Universal AI Brain with LangChain.js
 *
 * Features:
 * - Replaces LangChain memory with MongoDB-powered memory
 * - Provides MongoDB vector store implementation
 * - Enhances chains with intelligent context injection
 * - Integrates with LangChain agents and tools
 */
class LangChainJSAdapter extends BaseFrameworkAdapter_1.BaseFrameworkAdapter {
    constructor(config) {
        super({
            enableMemoryInjection: true,
            enableContextEnhancement: true,
            enableToolIntegration: true,
            enableVectorStoreReplacement: true,
            enableMemoryReplacement: true,
            enableChainEnhancement: true,
            ...config
        });
        this.frameworkName = 'LangChain.js';
        this.version = '1.0.0';
    }
    /**
     * Integrate with LangChain.js framework
     */
    async integrate(brain) {
        await this.initialize(brain);
        return {
            MongoDBVectorStore: this.createMongoDBVectorStore(),
            MongoDBMemory: this.createMongoDBMemory(),
            enhancedChatModel: this.createEnhancedChatModel(),
            mongoDBRetriever: this.createMongoDBRetriever(),
            mongoDBTools: this.createMongoDBTools(),
            adapter: this
        };
    }
    /**
     * Create MongoDB-powered vector store for LangChain
     */
    createMongoDBVectorStore() {
        return class MongoDBVectorStore {
            constructor(brain) {
                this.brain = brain;
            }
            async addDocuments(documents) {
                try {
                    const vectorDocs = documents.map(doc => ({
                        text: doc.pageContent || doc.content || JSON.stringify(doc),
                        metadata: doc.metadata || {},
                        source: 'langchain_document'
                    }));
                    await Promise.all(vectorDocs.map(doc => this.brain.vectorStore?.storeDocument(doc.text, doc.metadata, doc.source)));
                }
                catch (error) {
                    console.error('Error adding documents to MongoDB vector store:', error);
                    throw error;
                }
            }
            async similaritySearch(query, k = 4) {
                try {
                    const results = await this.brain.retrieveRelevantContext(query, {
                        limit: k,
                        framework: 'langchain'
                    });
                    return results.map(result => ({
                        pageContent: result.content,
                        metadata: {
                            ...result.metadata,
                            score: result.relevanceScore,
                            source: result.source
                        }
                    }));
                }
                catch (error) {
                    console.error('Error in MongoDB vector store similarity search:', error);
                    return [];
                }
            }
            async similaritySearchWithScore(query, k = 4) {
                const results = await this.similaritySearch(query, k);
                return results.map(doc => [doc, doc.metadata.score || 0]);
            }
        }(this.brain);
    }
    /**
     * Create MongoDB-powered memory for LangChain
     */
    createMongoDBMemory() {
        return class MongoDBMemory {
            constructor(brain, conversationId = 'langchain_session') {
                this.brain = brain;
                this.conversationId = conversationId;
            }
            async saveContext(inputs, outputs) {
                try {
                    const userMessage = inputs.input || inputs.question || JSON.stringify(inputs);
                    const assistantResponse = outputs.output || outputs.answer || JSON.stringify(outputs);
                    await this.brain.storeInteraction({
                        conversationId: this.conversationId,
                        userMessage,
                        assistantResponse,
                        context: [],
                        framework: 'langchain',
                        metadata: {
                            type: 'memory_context',
                            inputs,
                            outputs
                        }
                    });
                }
                catch (error) {
                    console.error('Error saving LangChain context to MongoDB:', error);
                }
            }
            async loadMemoryVariables(inputs) {
                try {
                    const query = inputs.input || inputs.question || JSON.stringify(inputs);
                    const context = await this.brain.retrieveRelevantContext(query, {
                        conversationId: this.conversationId,
                        framework: 'langchain',
                        limit: 5
                    });
                    const history = context
                        .map(ctx => `Human: ${ctx.metadata.userMessage || ''}\nAI: ${ctx.content}`)
                        .join('\n\n');
                    return {
                        history,
                        chat_history: context.map(ctx => ({
                            human: ctx.metadata.userMessage || '',
                            ai: ctx.content
                        }))
                    };
                }
                catch (error) {
                    console.error('Error loading LangChain memory from MongoDB:', error);
                    return { history: '', chat_history: [] };
                }
            }
            async clear() {
                // In a real implementation, we might want to mark conversation as cleared
                console.log('MongoDB memory cleared for conversation:', this.conversationId);
            }
        }(this.brain, 'langchain_session');
    }
    /**
     * Create enhanced chat model with MongoDB context injection
     */
    createEnhancedChatModel() {
        return (originalModel, conversationId) => {
            return {
                async invoke(messages) {
                    if (!this.brain) {
                        return originalModel.invoke(messages);
                    }
                    try {
                        // Extract user message for enhancement
                        const userMessage = this.extractUserMessage(messages);
                        if (userMessage) {
                            // Enhance with MongoDB context
                            const enhanced = await this.brain.enhancePrompt(userMessage, {
                                frameworkType: 'langchain',
                                conversationId: conversationId || 'langchain_session',
                                enhancementStrategy: 'hybrid'
                            });
                            // Replace user message with enhanced prompt
                            const enhancedMessages = this.replaceUserMessage(messages, enhanced.enhancedPrompt);
                            // Call original model
                            const result = await originalModel.invoke(enhancedMessages);
                            // Store interaction
                            await this.brain.storeInteraction({
                                conversationId: conversationId || 'langchain_session',
                                userMessage,
                                assistantResponse: result.content || JSON.stringify(result),
                                context: enhanced.injectedContext,
                                framework: 'langchain',
                                metadata: {
                                    enhancementStrategy: 'hybrid',
                                    originalModel: originalModel.constructor.name
                                }
                            });
                            return {
                                ...result,
                                enhancedContext: enhanced.injectedContext
                            };
                        }
                        return originalModel.invoke(messages);
                    }
                    catch (error) {
                        console.error('Error in enhanced LangChain chat model:', error);
                        return originalModel.invoke(messages);
                    }
                },
                async stream(messages) {
                    // Similar enhancement for streaming
                    return originalModel.stream(messages);
                }
            };
        };
    }
    /**
     * Create MongoDB retriever for LangChain
     */
    createMongoDBRetriever() {
        return {
            getRelevantDocuments: async (query) => {
                if (!this.brain)
                    return [];
                const results = await this.brain.retrieveRelevantContext(query, {
                    framework: 'langchain',
                    limit: 10
                });
                return results.map(result => ({
                    pageContent: result.content,
                    metadata: {
                        ...result.metadata,
                        score: result.relevanceScore,
                        source: result.source
                    }
                }));
            }
        };
    }
    /**
     * Create MongoDB tools for LangChain agents
     */
    createMongoDBTools() {
        return [
            {
                name: 'mongodb_search',
                description: 'Search MongoDB knowledge base for relevant information',
                func: async (query) => {
                    if (!this.brain)
                        return 'MongoDB brain not available';
                    const results = await this.brain.retrieveRelevantContext(query, {
                        framework: 'langchain',
                        limit: 5
                    });
                    return results.map(r => `Content: ${r.content}\nRelevance: ${r.relevanceScore}\nSource: ${r.source}`).join('\n\n');
                }
            },
            {
                name: 'mongodb_store',
                description: 'Store information in MongoDB for future retrieval',
                func: async (content) => {
                    if (!this.brain)
                        return 'MongoDB brain not available';
                    try {
                        await this.brain.storeInteraction({
                            conversationId: 'langchain_storage',
                            userMessage: 'Store information',
                            assistantResponse: content,
                            context: [],
                            framework: 'langchain',
                            metadata: { type: 'tool_storage' }
                        });
                        return 'Information stored successfully in MongoDB';
                    }
                    catch (error) {
                        return `Error storing information: ${error.message}`;
                    }
                }
            }
        ];
    }
    // Framework-specific implementation methods
    checkFrameworkAvailability() {
        try {
            // Try to import REAL LangChain.js framework
            require.resolve('@langchain/core');
            return true;
        }
        catch {
            console.warn('⚠️ LangChain.js not found. Install with: npm install @langchain/core');
            return false;
        }
    }
    checkVersionCompatibility() {
        try {
            const packageJson = require('@langchain/core/package.json');
            const version = packageJson.version;
            // Check if version is 0.1.0 or higher (based on docs)
            const [major, minor] = version.split('.').map(Number);
            if (major > 0 || (major === 0 && minor >= 1)) {
                return true;
            }
            console.warn(`⚠️ LangChain.js version ${version} detected. Version 0.1.0+ recommended.`);
            return false;
        }
        catch {
            // If we can't check version, assume it's compatible
            return true;
        }
    }
    async setupFrameworkIntegration() {
        console.log('🔌 Setting up LangChain.js integration...');
        if (!this.checkFrameworkAvailability()) {
            console.warn('⚠️ LangChain.js not available - adapter will use fallback mode');
            return;
        }
        if (!this.checkVersionCompatibility()) {
            console.warn('⚠️ LangChain.js version compatibility issue detected');
        }
        console.log('✅ LangChain.js integration ready');
    }
    createIntelligentTools() {
        return this.createMongoDBTools();
    }
    // Helper methods
    extractUserMessage(messages) {
        // Find the last human/user message
        for (let i = messages.length - 1; i >= 0; i--) {
            const msg = messages[i];
            if (msg.role === 'human' || msg.role === 'user' || !msg.role) {
                return msg.content;
            }
        }
        return '';
    }
    replaceUserMessage(messages, enhancedContent) {
        const result = [...messages];
        for (let i = result.length - 1; i >= 0; i--) {
            if (result[i].role === 'human' || result[i].role === 'user' || !result[i].role) {
                result[i] = { ...result[i], content: enhancedContent };
                break;
            }
        }
        return result;
    }
    /**
     * Get framework capabilities
     */
    getCapabilities() {
        return {
            supportsStreaming: true,
            supportsTools: true,
            supportsMultiModal: true,
            supportsMemory: true,
            supportedModels: [
                'gpt-4o',
                'gpt-4o-mini',
                'claude-3-5-sonnet',
                'gemini-pro',
                'llama-3.1-70b'
            ],
            maxContextLength: 128000
        };
    }
    /**
     * Enhanced framework integration method
     */
    enhanceWithBrain(originalFunction, brain) {
        return async (...args) => {
            try {
                // For LangChain, we typically enhance the model or chain
                const enhanced = this.createEnhancedChatModel();
                // If it's a model, wrap it with enhancement
                if (args[0] && typeof args[0].invoke === 'function') {
                    return enhanced(args[0]);
                }
                return await originalFunction(...args);
            }
            catch (error) {
                console.error('Error in enhanceWithBrain:', error);
                return await originalFunction(...args);
            }
        };
    }
    /**
     * Check if adapter is ready
     */
    isReady() {
        return this.brain !== null && this.isInitialized;
    }
    /**
     * Create enhanced components (alias for integrate for backward compatibility)
     */
    createEnhancedComponents() {
        if (!this.brain) {
            throw new Error('LangChainJSAdapter not initialized. Call integrate() first.');
        }
        return {
            MongoDBVectorStore: this.createMongoDBVectorStore(),
            MongoDBMemory: this.createMongoDBMemory(),
            enhancedChatModel: this.createEnhancedChatModel(),
            mongoDBRetriever: this.createMongoDBRetriever(),
            mongoDBTools: this.createMongoDBTools(),
            adapter: this
        };
    }
    /**
     * Validate that the adapter is working with REAL LangChain.js framework
     */
    async validateRealIntegration() {
        try {
            // Try to import the actual LangChain classes
            const { ChatOpenAI } = await Promise.resolve().then(() => __importStar(require('@langchain/openai')));
            const { LLMChain } = await Promise.resolve().then(() => __importStar(require('langchain/chains')));
            const { PromptTemplate } = await Promise.resolve().then(() => __importStar(require('@langchain/core/prompts')));
            // Verify they are constructors
            if (typeof ChatOpenAI !== 'function' ||
                typeof LLMChain !== 'function' ||
                typeof PromptTemplate !== 'function') {
                return false;
            }
            console.log('✅ REAL LangChain.js integration validated');
            return true;
        }
        catch (error) {
            console.warn('⚠️ REAL LangChain.js not available - using fallback mode');
            return false;
        }
    }
}
exports.LangChainJSAdapter = LangChainJSAdapter;
