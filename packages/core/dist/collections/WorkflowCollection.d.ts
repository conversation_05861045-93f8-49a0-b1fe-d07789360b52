/**
 * @file WorkflowCollection - MongoDB collection operations for agent workflows
 *
 * This class provides CRUD operations and specialized queries for agent workflows,
 * implementing workflow state management and execution tracking.
 */
import { Db, ObjectId } from 'mongodb';
import { BaseCollection } from './BaseCollection';
export type WorkflowStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled' | 'paused';
export interface WorkflowStep {
    stepId: string;
    name: string;
    type: string;
    parameters: Record<string, any>;
    result?: any;
    error?: string;
    startedAt?: Date;
    completedAt?: Date;
}
export interface AgentWorkflow {
    _id: ObjectId;
    agentId: ObjectId;
    name: string;
    description?: string;
    framework: string;
    status: WorkflowStatus;
    currentStepIndex: number;
    steps: WorkflowStep[];
    variables: Record<string, any>;
    metadata: Record<string, any>;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
    startedAt?: Date;
    completedAt?: Date;
    error?: string;
}
export interface WorkflowFilter {
    agentId?: string | ObjectId;
    status?: WorkflowStatus;
    framework?: string;
    tags?: string[];
    createdAfter?: Date;
    createdBefore?: Date;
    startedAfter?: Date;
    startedBefore?: Date;
    completedAfter?: Date;
    completedBefore?: Date;
}
export interface WorkflowUpdateData {
    name?: string;
    description?: string;
    status?: WorkflowStatus;
    currentStepIndex?: number;
    steps?: WorkflowStep[];
    variables?: Record<string, any>;
    metadata?: Record<string, any>;
    tags?: string[];
    startedAt?: Date;
    completedAt?: Date;
    error?: string;
}
export interface WorkflowExecutionOptions {
    resumeFromStep?: number;
    variables?: Record<string, any>;
    timeout?: number;
}
/**
 * WorkflowCollection - Complete CRUD operations for agent workflows
 *
 * Features:
 * - Workflow state management
 * - Step-by-step execution tracking
 * - Error handling and recovery
 * - Variable management
 * - Performance monitoring
 */
export declare class WorkflowCollection extends BaseCollection<AgentWorkflow> {
    protected collectionName: string;
    constructor(db: Db);
    /**
     * Create a new workflow
     */
    createWorkflow(workflowData: Omit<AgentWorkflow, '_id' | 'createdAt' | 'updatedAt'>): Promise<AgentWorkflow>;
    /**
     * Get workflow by ID
     */
    getWorkflow(workflowId: string | ObjectId): Promise<AgentWorkflow | null>;
    /**
     * Update workflow
     */
    updateWorkflow(workflowId: string | ObjectId, updateData: WorkflowUpdateData): Promise<AgentWorkflow | null>;
    /**
     * Update workflow status
     */
    updateWorkflowStatus(workflowId: string | ObjectId, status: WorkflowStatus, error?: string): Promise<boolean>;
    /**
     * Update current step
     */
    updateCurrentStep(workflowId: string | ObjectId, stepIndex: number, stepResult?: any): Promise<boolean>;
    /**
     * Update workflow variables
     */
    updateWorkflowVariables(workflowId: string | ObjectId, variables: Record<string, any>): Promise<boolean>;
    /**
     * Add step result
     */
    addStepResult(workflowId: string | ObjectId, stepIndex: number, result: any, error?: string): Promise<boolean>;
    /**
     * Delete workflow
     */
    deleteWorkflow(workflowId: string | ObjectId): Promise<boolean>;
    /**
     * List workflows with filtering and pagination
     */
    listWorkflows(filter?: WorkflowFilter, options?: {
        limit?: number;
        skip?: number;
        sort?: Record<string, 1 | -1>;
    }): Promise<{
        workflows: AgentWorkflow[];
        total: number;
    }>;
    /**
     * Get workflows by agent
     */
    getAgentWorkflows(agentId: string | ObjectId, status?: WorkflowStatus): Promise<AgentWorkflow[]>;
    /**
     * Get running workflows
     */
    getRunningWorkflows(): Promise<AgentWorkflow[]>;
    /**
     * Get workflows by status
     */
    getWorkflowsByStatus(status: WorkflowStatus): Promise<AgentWorkflow[]>;
    /**
     * Search workflows
     */
    searchWorkflows(query: string, limit?: number): Promise<AgentWorkflow[]>;
    /**
     * Get workflow statistics
     */
    getWorkflowStats(agentId?: string | ObjectId): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byFramework: Record<string, number>;
        averageExecutionTime: number;
        successRate: number;
    }>;
    /**
     * Cleanup old workflows
     */
    cleanupOldWorkflows(olderThanDays?: number): Promise<number>;
    /**
     * Check if workflow has started
     */
    private hasStartedAt;
    /**
     * Build MongoDB filter from WorkflowFilter
     */
    private buildMongoFilter;
    /**
     * Create indexes for optimal performance
     */
    createIndexes(): Promise<void>;
}
//# sourceMappingURL=WorkflowCollection.d.ts.map