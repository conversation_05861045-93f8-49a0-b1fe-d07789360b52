{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/collections/index.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAGlD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAGxD,YAAY,EACV,WAAW,EACX,eAAe,EAChB,MAAM,mBAAmB,CAAC;AAE3B,YAAY,EACV,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACpB,MAAM,oBAAoB,CAAC;AAE5B,YAAY,EACV,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,oBAAoB,EACrB,MAAM,qBAAqB,CAAC;AAE7B,YAAY,EACV,cAAc,EACd,kBAAkB,EAClB,wBAAwB,EACzB,MAAM,sBAAsB,CAAC;AAE9B,YAAY,EACV,UAAU,EACV,cAAc,EACd,mBAAmB,EACpB,MAAM,kBAAkB,CAAC;AAE1B,YAAY,EACV,aAAa,EACb,yBAAyB,EACzB,eAAe,EAChB,MAAM,qBAAqB,CAAC;AAE7B,YAAY,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,kBAAkB,EAClB,WAAW,EACX,UAAU,EACV,aAAa,EACb,iBAAiB,EAClB,MAAM,qBAAqB,CAAC;AAG7B,YAAY,EACV,YAAY,EACZ,iBAAiB,EACjB,eAAe,EAChB,MAAM,kBAAkB,CAAC;AAE1B;;GAEG;AACH,OAAO,EAAE,EAAE,EAAE,MAAM,SAAS,CAAC;AAC7B,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAExD,qBAAa,iBAAiB;IAC5B,OAAO,CAAC,EAAE,CAAK;IAGR,MAAM,EAAE,eAAe,CAAC;IACxB,MAAM,EAAE,gBAAgB,CAAC;IACzB,OAAO,EAAE,iBAAiB,CAAC;IAC3B,SAAS,EAAE,kBAAkB,CAAC;IAC9B,KAAK,EAAE,cAAc,CAAC;IACtB,OAAO,EAAE,iBAAiB,CAAC;IAC3B,OAAO,EAAE,iBAAiB,CAAC;gBAEtB,EAAE,EAAE,EAAE;IAalB;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAsBjC;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC;QAClC,MAAM,EAAE,GAAG,CAAC;QACZ,MAAM,EAAE,GAAG,CAAC;QACZ,OAAO,EAAE,GAAG,CAAC;QACb,SAAS,EAAE,GAAG,CAAC;QACf,KAAK,EAAE,GAAG,CAAC;QACX,OAAO,EAAE,GAAG,CAAC;QACb,OAAO,EAAE,GAAG,CAAC;KACd,CAAC;IAsBF;;OAEG;IACG,cAAc,CAAC,OAAO,GAAE;QAC5B,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAC3B,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,qBAAqB,CAAC,EAAE,MAAM,CAAC;QAC/B,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAC3B,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,oBAAoB,CAAC,EAAE,MAAM,CAAC;KAC1B,GAAG,OAAO,CAAC;QACf,aAAa,EAAE,MAAM,CAAC;QACtB,eAAe,EAAE,MAAM,CAAC;QACxB,gBAAgB,EAAE,MAAM,CAAC;QACzB,qBAAqB,EAAE,MAAM,CAAC;QAC9B,cAAc,EAAE,MAAM,CAAC;QACvB,aAAa,EAAE,MAAM,CAAC;KACvB,CAAC;IA2CF;;OAEG;IACG,WAAW,IAAI,OAAO,CAAC;QAC3B,OAAO,EAAE,OAAO,CAAC;QACjB,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE;YAAE,UAAU,EAAE,OAAO,CAAC;YAAC,aAAa,EAAE,MAAM,CAAC;YAAC,KAAK,CAAC,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;KAC7F,CAAC;IA6BF;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC,IAAI,CAAC;IAgBzC;;OAEG;IACH,WAAW,IAAI,EAAE;IAIjB;;OAEG;IACH,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG;CAoBjC"}