"use strict";
/**
 * @file BaseCollection - Base class for MongoDB collection operations
 *
 * This class provides common functionality for all MongoDB collections,
 * including validation, error handling, and basic CRUD operations.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseCollection = void 0;
const mongodb_1 = require("mongodb");
const ajv_1 = __importDefault(require("ajv"));
const ajv_formats_1 = __importDefault(require("ajv-formats"));
/**
 * BaseCollection - Abstract base class for MongoDB collections
 *
 * Features:
 * - JSON Schema validation
 * - Common CRUD operations
 * - Error handling and logging
 * - Pagination utilities
 * - Index management
 */
class BaseCollection {
    constructor(db) {
        this.db = db;
        // Initialize AJV for schema validation
        this.ajv = new ajv_1.default({ allErrors: true, removeAdditional: true });
        (0, ajv_formats_1.default)(this.ajv);
    }
    /**
     * Initialize the collection - must be called after constructor
     */
    initializeCollection() {
        if (!this.collection) {
            this.collection = this.db.collection(this.collectionName);
            this.loadSchema();
        }
    }
    /**
     * Load JSON schema for validation
     */
    loadSchema() {
        try {
            // Try to load schema from schemas directory
            const schemaPath = `../schemas/${this.collectionName}.json`;
            this.schema = require(schemaPath);
        }
        catch (error) {
            console.warn(`⚠️ No schema found for collection ${this.collectionName}`);
        }
    }
    /**
     * Validate document against JSON schema
     */
    async validateDocument(document) {
        if (!this.schema) {
            return; // No validation if no schema
        }
        const validate = this.ajv.compile(this.schema);
        const valid = validate(document);
        if (!valid) {
            const errors = validate.errors?.map(err => `${err.instancePath} ${err.message}`).join(', ');
            throw new Error(`Document validation failed: ${errors}`);
        }
    }
    /**
     * Generic find with pagination
     */
    async findPaginated(filter = {}, options = {}) {
        const { limit = 50, skip = 0, sort = { createdAt: -1 } } = options;
        const page = Math.floor(skip / limit) + 1;
        const [documents, total] = await Promise.all([
            this.collection
                .find(filter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .toArray(),
            this.collection.countDocuments(filter)
        ]);
        const totalPages = Math.ceil(total / limit);
        const hasMore = page < totalPages;
        return {
            documents: documents,
            total,
            hasMore,
            page,
            totalPages
        };
    }
    /**
     * Generic find one
     */
    async findOne(filter) {
        return await this.collection.findOne(filter);
    }
    /**
     * Generic find by ID
     */
    async findById(id) {
        const objectId = typeof id === 'string' ? new mongodb_1.ObjectId(id) : id;
        return await this.collection.findOne({ _id: objectId });
    }
    /**
     * Generic insert one
     */
    async insertOne(document) {
        const now = new Date();
        const docWithTimestamps = {
            ...document,
            _id: new mongodb_1.ObjectId(),
            createdAt: now,
            updatedAt: now
        };
        await this.validateDocument(docWithTimestamps);
        const result = await this.collection.insertOne(docWithTimestamps);
        if (!result.acknowledged) {
            throw new Error(`Failed to insert document into ${this.collectionName}`);
        }
        return docWithTimestamps;
    }
    /**
     * Generic insert many
     */
    async insertMany(documents) {
        const now = new Date();
        const docsWithTimestamps = documents.map(doc => ({
            ...doc,
            _id: new mongodb_1.ObjectId(),
            createdAt: now,
            updatedAt: now
        }));
        // Validate all documents
        for (const doc of docsWithTimestamps) {
            await this.validateDocument(doc);
        }
        const result = await this.collection.insertMany(docsWithTimestamps);
        if (!result.acknowledged) {
            throw new Error(`Failed to insert documents into ${this.collectionName}`);
        }
        return docsWithTimestamps;
    }
    /**
     * Generic update one
     */
    async updateOne(filter, update, options = {}) {
        const updateDoc = {
            ...update,
            $set: {
                ...update.$set,
                updatedAt: new Date()
            }
        };
        const result = await this.collection.findOneAndUpdate(filter, updateDoc, {
            returnDocument: 'after',
            ...options
        });
        return result;
    }
    /**
     * Generic update by ID
     */
    async updateById(id, update, options = {}) {
        const objectId = typeof id === 'string' ? new mongodb_1.ObjectId(id) : id;
        return await this.updateOne({ _id: objectId }, update, options);
    }
    /**
     * Generic delete one
     */
    async deleteOne(filter) {
        const result = await this.collection.deleteOne(filter);
        return result.deletedCount > 0;
    }
    /**
     * Generic delete by ID
     */
    async deleteById(id) {
        const objectId = typeof id === 'string' ? new mongodb_1.ObjectId(id) : id;
        return await this.deleteOne({ _id: objectId });
    }
    /**
     * Generic delete many
     */
    async deleteMany(filter) {
        const result = await this.collection.deleteMany(filter);
        return result.deletedCount;
    }
    /**
     * Count documents
     */
    async count(filter = {}) {
        return await this.collection.countDocuments(filter);
    }
    /**
     * Check if document exists
     */
    async exists(filter) {
        const count = await this.collection.countDocuments(filter, { limit: 1 });
        return count > 0;
    }
    /**
     * Get distinct values
     */
    async distinct(field, filter = {}) {
        return await this.collection.distinct(field, filter);
    }
    /**
     * Aggregate pipeline
     */
    async aggregate(pipeline) {
        return await this.collection.aggregate(pipeline).toArray();
    }
    /**
     * Create text search index
     */
    async createTextIndex(fields, options = {}) {
        await this.collection.createIndex(fields, {
            name: `${this.collectionName}_text_search`,
            ...options
        });
    }
    /**
     * Text search
     */
    async textSearch(query, options = {}) {
        const { limit = 20, skip = 0, filter = {} } = options;
        const searchFilter = {
            $text: { $search: query },
            ...filter
        };
        return await this.collection
            .find(searchFilter)
            .sort({ score: { $meta: 'textScore' } })
            .skip(skip)
            .limit(limit)
            .toArray();
    }
    /**
     * Bulk write operations
     */
    async bulkWrite(operations) {
        return await this.collection.bulkWrite(operations);
    }
    /**
     * Get collection statistics
     */
    async getStats() {
        const stats = await this.db.command({ collStats: this.collectionName });
        return {
            documentCount: stats.count || 0,
            avgDocumentSize: stats.avgObjSize || 0,
            totalSize: stats.size || 0,
            indexCount: stats.nindexes || 0
        };
    }
    /**
     * Create common indexes
     */
    async createCommonIndexes() {
        await Promise.all([
            this.collection.createIndex({ createdAt: -1 }),
            this.collection.createIndex({ updatedAt: -1 })
        ]);
    }
    /**
     * Drop collection
     */
    async drop() {
        await this.collection.drop();
    }
    /**
     * Get collection name
     */
    getCollectionName() {
        return this.collectionName;
    }
    /**
     * Get MongoDB collection instance
     */
    getCollection() {
        return this.collection;
    }
}
exports.BaseCollection = BaseCollection;
