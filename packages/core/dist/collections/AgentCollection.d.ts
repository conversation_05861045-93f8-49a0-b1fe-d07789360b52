/**
 * @file AgentCollection - MongoDB collection operations for agents
 *
 * This class provides CRUD operations and specialized queries for the agents collection,
 * implementing the complete agent lifecycle management with MongoDB.
 */
import { Db, ObjectId } from 'mongodb';
import { BaseCollection } from './BaseCollection';
export type AgentStatus = 'active' | 'inactive' | 'paused' | 'error' | 'maintenance';
export interface AgentConfiguration {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    tools?: string[];
    memory?: {
        enabled: boolean;
        maxSize?: number;
        ttl?: number;
    };
    safety?: {
        enabled: boolean;
        filters?: string[];
    };
}
export interface Agent {
    _id: ObjectId;
    name: string;
    description?: string;
    instructions?: string;
    framework: string;
    status: AgentStatus;
    configuration: AgentConfiguration;
    tags: string[];
    metadata: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    lastActiveAt: Date;
}
export interface AgentFilter {
    status?: AgentStatus;
    framework?: string;
    tags?: string[];
    createdAfter?: Date;
    createdBefore?: Date;
    lastActiveAfter?: Date;
    lastActiveBefore?: Date;
}
export interface AgentUpdateData {
    name?: string;
    description?: string;
    instructions?: string;
    status?: AgentStatus;
    configuration?: Partial<AgentConfiguration>;
    tags?: string[];
    metadata?: Record<string, any>;
    lastActiveAt?: Date;
}
/**
 * AgentCollection - Complete CRUD operations for agents
 *
 * Features:
 * - Full agent lifecycle management
 * - Status tracking and updates
 * - Framework-specific agent queries
 * - Performance metrics integration
 * - Tag-based organization
 */
export declare class AgentCollection extends BaseCollection<Agent> {
    protected collectionName: string;
    constructor(db: Db);
    /**
     * Create a new agent
     */
    createAgent(agentData: Omit<Agent, '_id' | 'createdAt' | 'updatedAt'>): Promise<Agent>;
    /**
     * Get agent by ID
     */
    getAgent(agentId: string | ObjectId): Promise<Agent | null>;
    /**
     * Get agent by name and framework
     */
    getAgentByName(name: string, framework?: string): Promise<Agent | null>;
    /**
     * Update agent
     */
    updateAgent(agentId: string | ObjectId, updateData: AgentUpdateData): Promise<Agent | null>;
    /**
     * Update agent status
     */
    updateAgentStatus(agentId: string | ObjectId, status: AgentStatus): Promise<boolean>;
    /**
     * Update agent last active timestamp
     */
    updateLastActive(agentId: string | ObjectId): Promise<boolean>;
    /**
     * Delete agent
     */
    deleteAgent(agentId: string | ObjectId): Promise<boolean>;
    /**
     * List agents with filtering and pagination
     */
    listAgents(filter?: AgentFilter, options?: {
        limit?: number;
        skip?: number;
        sort?: Record<string, 1 | -1>;
    }): Promise<{
        agents: Agent[];
        total: number;
    }>;
    /**
     * Get agents by framework
     */
    getAgentsByFramework(framework: string): Promise<Agent[]>;
    /**
     * Get active agents
     */
    getActiveAgents(): Promise<Agent[]>;
    /**
     * Get agents by tags
     */
    getAgentsByTags(tags: string[]): Promise<Agent[]>;
    /**
     * Search agents by name or description
     */
    searchAgents(query: string, limit?: number): Promise<Agent[]>;
    /**
     * Get agent statistics
     */
    getAgentStats(): Promise<{
        total: number;
        byStatus: Record<string, number>;
        byFramework: Record<string, number>;
        recentlyActive: number;
    }>;
    /**
     * Cleanup inactive agents
     */
    cleanupInactiveAgents(inactiveDays?: number): Promise<number>;
    /**
     * Build MongoDB filter from AgentFilter
     */
    private buildMongoFilter;
    /**
     * Create indexes for optimal performance
     */
    createIndexes(): Promise<void>;
}
//# sourceMappingURL=AgentCollection.d.ts.map