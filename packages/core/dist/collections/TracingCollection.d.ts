/**
 * @file TracingCollection - Enterprise-grade agent tracing and observability
 *
 * This collection provides comprehensive tracing for all agent operations,
 * enabling real-time monitoring, performance analysis, and debugging across
 * all framework integrations (Vercel AI, Mastra, OpenAI Agents, LangChain).
 *
 * Features:
 * - Real-time trace monitoring with MongoDB Change Streams
 * - Performance metrics and cost tracking
 * - Error analysis and debugging information
 * - Framework-specific operation tracking
 * - Time-series data optimization
 */
import { ObjectId } from 'mongodb';
import { BaseCollection, BaseDocument } from './BaseCollection';
export interface AgentStep {
    stepId: string;
    stepType: 'context_injection' | 'prompt_enhancement' | 'framework_call' | 'response_processing' | 'safety_check' | 'memory_storage';
    startTime: Date;
    endTime?: Date;
    duration?: number;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    input?: any;
    output?: any;
    error?: AgentError;
    metadata?: Record<string, any>;
}
export interface AgentError {
    errorId: string;
    errorType: 'validation_error' | 'framework_error' | 'mongodb_error' | 'network_error' | 'timeout_error' | 'safety_violation' | 'unknown_error';
    message: string;
    stack?: string;
    code?: string | number;
    timestamp: Date;
    recoverable: boolean;
    retryCount?: number;
    context?: Record<string, any>;
}
export interface PerformanceMetrics {
    totalDuration: number;
    contextRetrievalTime: number;
    promptEnhancementTime: number;
    frameworkCallTime: number;
    responseProcessingTime: number;
    memoryStorageTime: number;
    memoryUsage?: {
        heapUsed: number;
        heapTotal: number;
        external: number;
    };
    networkMetrics?: {
        requestCount: number;
        totalBytes: number;
        avgLatency: number;
    };
}
export interface ContextItem {
    contextId: string;
    source: string;
    content: string;
    relevanceScore: number;
    retrievalTime: number;
    metadata?: Record<string, any>;
}
export interface TokenUsage {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    embeddingTokens?: number;
    frameworkTokens?: {
        inputTokens: number;
        outputTokens: number;
        reasoningTokens?: number;
    };
}
export interface CostBreakdown {
    totalCost: number;
    embeddingCost: number;
    completionCost: number;
    promptCost: number;
    frameworkCosts?: {
        modelCost: number;
        apiCost: number;
        additionalCosts?: Record<string, number>;
    };
    mongoCosts?: {
        vectorSearchCost: number;
        readCost: number;
        writeCost: number;
    };
    currency: string;
    calculatedAt: Date;
}
export interface FrameworkMetadata {
    frameworkName: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain' | 'unknown';
    frameworkVersion?: string;
    vercelAI?: {
        model: string;
        provider: string;
        streaming: boolean;
        tools?: string[];
    };
    mastra?: {
        agentId: string;
        resourceId?: string;
        threadId?: string;
        workflowId?: string;
    };
    openaiAgents?: {
        assistantId: string;
        threadId: string;
        runId: string;
        tools?: string[];
    };
    langchain?: {
        chainType: string;
        memoryType?: string;
        vectorStore?: string;
        llmProvider?: string;
    };
}
export interface AgentTrace extends BaseDocument {
    traceId: string;
    agentId: ObjectId;
    sessionId: string;
    conversationId?: string;
    startTime: Date;
    endTime?: Date;
    totalDuration?: number;
    status: 'active' | 'completed' | 'failed' | 'cancelled' | 'timeout';
    operation: {
        type: 'generate_text' | 'stream_text' | 'generate_object' | 'chat' | 'memory_retrieval' | 'context_search' | 'custom';
        description?: string;
        userInput: string;
        finalOutput?: string;
        outputType?: 'text' | 'object' | 'stream' | 'error';
    };
    steps: AgentStep[];
    performance: PerformanceMetrics;
    errors: AgentError[];
    warnings?: string[];
    contextUsed: ContextItem[];
    memoryOperations?: {
        retrieved: number;
        stored: number;
        updated: number;
    };
    tokensUsed: TokenUsage;
    cost: CostBreakdown;
    framework: FrameworkMetadata;
    safetyChecks?: {
        piiDetected: boolean;
        contentFiltered: boolean;
        hallucinationScore?: number;
        complianceFlags?: string[];
    };
    debugInfo?: {
        environment: string;
        nodeVersion: string;
        memorySnapshot?: any;
        stackTrace?: string[];
    };
    userContext?: {
        userId?: string;
        userAgent?: string;
        ipAddress?: string;
        location?: string;
    };
    tags?: string[];
    metadata?: Record<string, any>;
}
/**
 * TracingCollection - Enterprise-grade agent tracing and observability
 *
 * This collection stores comprehensive trace data for all agent operations,
 * optimized for time-series queries and real-time monitoring.
 */
export declare class TracingCollection extends BaseCollection<AgentTrace> {
    protected collectionName: string;
    constructor(db: any);
    /**
     * Create specialized indexes for tracing queries
     */
    createIndexes(): Promise<void>;
    /**
     * Start a new trace for an agent operation
     */
    startTrace(traceData: {
        traceId: string;
        agentId: ObjectId;
        sessionId: string;
        conversationId?: string;
        operation: AgentTrace['operation'];
        framework: FrameworkMetadata;
        userContext?: AgentTrace['userContext'];
        tags?: string[];
        metadata?: Record<string, any>;
    }): Promise<AgentTrace>;
    /**
     * Complete a trace with final results
     */
    completeTrace(traceId: string, completion: {
        status: 'completed' | 'failed' | 'cancelled' | 'timeout';
        finalOutput?: string;
        outputType?: AgentTrace['operation']['outputType'];
        performance?: Partial<PerformanceMetrics>;
        tokensUsed?: Partial<TokenUsage>;
        cost?: Partial<CostBreakdown>;
        errors?: AgentError[];
        warnings?: string[];
        safetyChecks?: AgentTrace['safetyChecks'];
        debugInfo?: AgentTrace['debugInfo'];
    }): Promise<AgentTrace | null>;
    /**
     * Add a step to an active trace
     */
    addStep(traceId: string, step: AgentStep): Promise<AgentTrace | null>;
    /**
     * Update a specific step in a trace
     */
    updateStep(traceId: string, stepId: string, stepUpdate: Partial<AgentStep>): Promise<AgentTrace | null>;
    /**
     * Add context items used in a trace
     */
    addContextUsed(traceId: string, contextItems: ContextItem[]): Promise<AgentTrace | null>;
    /**
     * Record an error in a trace
     */
    recordError(traceId: string, error: AgentError): Promise<AgentTrace | null>;
    /**
     * Get traces by agent with pagination and filtering
     */
    getTracesByAgent(agentId: ObjectId, options?: {
        status?: AgentTrace['status'];
        framework?: string;
        startDate?: Date;
        endDate?: Date;
        limit?: number;
        skip?: number;
    }): Promise<AgentTrace[]>;
    /**
     * Get performance analytics for a time period
     */
    getPerformanceAnalytics(startDate: Date, endDate: Date, groupBy?: 'hour' | 'day' | 'framework' | 'operation'): Promise<any[]>;
    /**
     * Get error analysis for debugging
     */
    getErrorAnalysis(startDate: Date, endDate: Date): Promise<{
        errorsByType: any[];
        errorsByFramework: any[];
        recoverableErrors: number;
        totalErrors: number;
    }>;
    /**
     * Get active traces (for real-time monitoring)
     */
    getActiveTraces(limit?: number): Promise<AgentTrace[]>;
    /**
     * Get traces by session for conversation analysis
     */
    getTracesBySession(sessionId: string, options?: {
        limit?: number;
        skip?: number;
    }): Promise<AgentTrace[]>;
    /**
     * Clean up old traces (for data retention)
     */
    cleanupOldTraces(olderThanDays: number): Promise<number>;
}
//# sourceMappingURL=TracingCollection.d.ts.map