"use strict";
/**
 * @file Collections Index - Export all MongoDB collection classes
 *
 * This file exports all collection classes for the Universal AI Brain,
 * providing a centralized access point for MongoDB operations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionManager = exports.TracingCollection = exports.MetricsCollection = exports.ToolCollection = exports.WorkflowCollection = exports.ContextCollection = exports.MemoryCollection = exports.AgentCollection = exports.BaseCollection = void 0;
// Base collection class
var BaseCollection_1 = require("./BaseCollection");
Object.defineProperty(exports, "BaseCollection", { enumerable: true, get: function () { return BaseCollection_1.BaseCollection; } });
// Core collection classes
var AgentCollection_1 = require("./AgentCollection");
Object.defineProperty(exports, "AgentCollection", { enumerable: true, get: function () { return AgentCollection_1.AgentCollection; } });
var MemoryCollection_1 = require("./MemoryCollection");
Object.defineProperty(exports, "MemoryCollection", { enumerable: true, get: function () { return MemoryCollection_1.MemoryCollection; } });
var ContextCollection_1 = require("./ContextCollection");
Object.defineProperty(exports, "ContextCollection", { enumerable: true, get: function () { return ContextCollection_1.ContextCollection; } });
var WorkflowCollection_1 = require("./WorkflowCollection");
Object.defineProperty(exports, "WorkflowCollection", { enumerable: true, get: function () { return WorkflowCollection_1.WorkflowCollection; } });
var ToolCollection_1 = require("./ToolCollection");
Object.defineProperty(exports, "ToolCollection", { enumerable: true, get: function () { return ToolCollection_1.ToolCollection; } });
var MetricsCollection_1 = require("./MetricsCollection");
Object.defineProperty(exports, "MetricsCollection", { enumerable: true, get: function () { return MetricsCollection_1.MetricsCollection; } });
var TracingCollection_1 = require("./TracingCollection");
Object.defineProperty(exports, "TracingCollection", { enumerable: true, get: function () { return TracingCollection_1.TracingCollection; } });
const AgentCollection_2 = require("./AgentCollection");
const MemoryCollection_2 = require("./MemoryCollection");
const ContextCollection_2 = require("./ContextCollection");
const WorkflowCollection_2 = require("./WorkflowCollection");
const ToolCollection_2 = require("./ToolCollection");
const MetricsCollection_2 = require("./MetricsCollection");
const TracingCollection_2 = require("./TracingCollection");
class CollectionManager {
    constructor(db) {
        this.db = db;
        // Initialize all collections
        this.agents = new AgentCollection_2.AgentCollection(db);
        this.memory = new MemoryCollection_2.MemoryCollection(db);
        this.context = new ContextCollection_2.ContextCollection(db);
        this.workflows = new WorkflowCollection_2.WorkflowCollection(db);
        this.tools = new ToolCollection_2.ToolCollection(db);
        this.metrics = new MetricsCollection_2.MetricsCollection(db);
        this.tracing = new TracingCollection_2.TracingCollection(db);
    }
    /**
     * Initialize all collections and create indexes
     */
    async initialize() {
        console.log('🔧 Initializing MongoDB collections...');
        try {
            // Create indexes for all collections in parallel
            await Promise.all([
                this.agents.createIndexes(),
                this.memory.createIndexes(),
                this.context.createIndexes(),
                this.workflows.createIndexes(),
                this.tools.createIndexes(),
                this.metrics.createIndexes(),
                this.tracing.createIndexes()
            ]);
            console.log('✅ All collection indexes created successfully');
        }
        catch (error) {
            console.error('❌ Error creating collection indexes:', error);
            throw error;
        }
    }
    /**
     * Get collection statistics
     */
    async getCollectionStats() {
        const [agentStats, memoryStats, contextStats, workflowStats, toolStats, metricsStats, tracingStats] = await Promise.all([
            this.agents.getStats(),
            this.memory.getStats(),
            this.context.getContextStats(),
            this.workflows.getStats(),
            this.tools.getStats(),
            this.metrics.getStats(),
            this.tracing.getStats()
        ]);
        return {
            agents: agentStats,
            memory: memoryStats,
            context: contextStats,
            workflows: workflowStats,
            tools: toolStats,
            metrics: metricsStats,
            tracing: tracingStats
        };
    }
    /**
     * Cleanup old data from all collections
     */
    async cleanupOldData(options = {}) {
        const { agentInactiveDays = 30, memoryExpirationDays = 0, // Only expired memories
        workflowCompletedDays = 30, toolExecutionDays = 30, metricsRetentionDays = 90, tracingRetentionDays = 30 } = options;
        console.log('🧹 Starting cleanup of old data...');
        const [agentsDeleted, memoriesDeleted, workflowsDeleted, toolExecutionsDeleted, metricsDeleted, tracesDeleted] = await Promise.all([
            this.agents.cleanupInactiveAgents(agentInactiveDays),
            memoryExpirationDays > 0
                ? this.memory.cleanupExpiredMemories()
                : this.memory.cleanupExpiredMemories(), // Only cleanup expired
            this.workflows.cleanupOldWorkflows(workflowCompletedDays),
            this.tools.cleanupOldExecutions(toolExecutionDays),
            this.metrics.cleanupOldMetrics(metricsRetentionDays),
            this.tracing.cleanupOldTraces(tracingRetentionDays)
        ]);
        const result = {
            agentsDeleted,
            memoriesDeleted,
            workflowsDeleted,
            toolExecutionsDeleted,
            metricsDeleted,
            tracesDeleted
        };
        console.log('✅ Cleanup completed:', result);
        return result;
    }
    /**
     * Health check for all collections
     */
    async healthCheck() {
        const collections = ['agents', 'memory', 'context', 'workflows', 'tools', 'metrics', 'tracing'];
        const results = {};
        let allHealthy = true;
        for (const collectionName of collections) {
            try {
                const collection = this[collectionName];
                const count = await collection.count();
                results[collectionName] = {
                    accessible: true,
                    documentCount: count
                };
            }
            catch (error) {
                results[collectionName] = {
                    accessible: false,
                    documentCount: 0,
                    error: error.message
                };
                allHealthy = false;
            }
        }
        return {
            healthy: allHealthy,
            collections: results
        };
    }
    /**
     * Drop all collections (use with caution!)
     */
    async dropAllCollections() {
        console.warn('⚠️ Dropping all collections...');
        await Promise.all([
            this.agents.drop().catch(() => { }), // Ignore errors if collection doesn't exist
            this.memory.drop().catch(() => { }),
            this.context.drop().catch(() => { }),
            this.workflows.drop().catch(() => { }),
            this.tools.drop().catch(() => { }),
            this.metrics.drop().catch(() => { }),
            this.tracing.drop().catch(() => { })
        ]);
        console.log('✅ All collections dropped');
    }
    /**
     * Get database instance
     */
    getDatabase() {
        return this.db;
    }
    /**
     * Get collection by name
     */
    getCollection(name) {
        switch (name) {
            case 'agents':
                return this.agents;
            case 'memory':
                return this.memory;
            case 'context':
                return this.context;
            case 'workflows':
                return this.workflows;
            case 'tools':
                return this.tools;
            case 'metrics':
                return this.metrics;
            case 'tracing':
                return this.tracing;
            default:
                throw new Error(`Unknown collection: ${name}`);
        }
    }
}
exports.CollectionManager = CollectionManager;
