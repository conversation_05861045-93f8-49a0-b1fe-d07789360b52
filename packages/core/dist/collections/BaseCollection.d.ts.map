{"version": 3, "file": "BaseCollection.d.ts", "sourceRoot": "", "sources": ["../../src/collections/BaseCollection.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnD,OAAO,GAAG,MAAM,KAAK,CAAC;AAGtB,MAAM,WAAW,YAAY;IAC3B,GAAG,CAAC,EAAE,QAAQ,CAAC;IACf,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,SAAS,CAAC,EAAE,IAAI,CAAC;CAClB;AAED,MAAM,WAAW,iBAAiB;IAChC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,eAAe,CAAC,CAAC;IAChC,SAAS,EAAE,CAAC,EAAE,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,OAAO,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,MAAM,CAAC;CACpB;AAED;;;;;;;;;GASG;AACH,8BAAsB,cAAc,CAAC,CAAC,SAAS,YAAY;IACzD,SAAS,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IAC1C,SAAS,CAAC,UAAU,EAAG,UAAU,CAAC,CAAC,CAAC,CAAC;IACrC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC;IACjB,SAAS,CAAC,GAAG,EAAE,GAAG,CAAC;IACnB,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;gBAEd,EAAE,EAAE,EAAE;IAQlB;;OAEG;IACH,SAAS,CAAC,oBAAoB,IAAI,IAAI;IAOtC;;OAEG;IACH,SAAS,CAAC,UAAU,IAAI,IAAI;IAU5B;;OAEG;cACa,gBAAgB,CAAC,QAAQ,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;IAgB5D;;OAEG;IACG,aAAa,CACjB,MAAM,GAAE,GAAQ,EAChB,OAAO,GAAE,iBAAsB,GAC9B,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;IA0B9B;;OAEG;IACG,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IAI7C;;OAEG;IACG,QAAQ,CAAC,EAAE,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IAKxD;;OAEG;IACG,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAoBrD;;OAEG;IACG,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC,EAAE,CAAC;IAuB3D;;OAEG;IACG,SAAS,CACb,MAAM,EAAE,GAAG,EACX,MAAM,EAAE,GAAG,EACX,OAAO,GAAE;QAAE,MAAM,CAAC,EAAE,OAAO,CAAA;KAAO,GACjC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IAqBpB;;OAEG;IACG,UAAU,CACd,EAAE,EAAE,MAAM,GAAG,QAAQ,EACrB,MAAM,EAAE,GAAG,EACX,OAAO,GAAE;QAAE,MAAM,CAAC,EAAE,OAAO,CAAA;KAAO,GACjC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;IAKpB;;OAEG;IACG,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC;IAK9C;;OAEG;IACG,UAAU,CAAC,EAAE,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;IAKzD;;OAEG;IACG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;IAK9C;;OAEG;IACG,KAAK,CAAC,MAAM,GAAE,GAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;IAI9C;;OAEG;IACG,MAAM,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC;IAK3C;;OAEG;IACG,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,GAAE,GAAQ,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAI/D;;OAEG;IACG,SAAS,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAIhD;;OAEG;IACG,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAE,GAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAOvF;;OAEG;IACG,UAAU,CACd,KAAK,EAAE,MAAM,EACb,OAAO,GAAE;QACP,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,GAAG,CAAC;KACT,GACL,OAAO,CAAC,CAAC,EAAE,CAAC;IAgBf;;OAEG;IACG,SAAS,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;IAIhD;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC;QACxB,aAAa,EAAE,MAAM,CAAC;QACtB,eAAe,EAAE,MAAM,CAAC;QACxB,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IAWF;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IAO1C;;OAEG;IACH,QAAQ,CAAC,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;IAEvC;;OAEG;IACG,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAI3B;;OAEG;IACH,iBAAiB,IAAI,MAAM;IAI3B;;OAEG;IACH,aAAa,IAAI,UAAU,CAAC,CAAC,CAAC;CAG/B"}