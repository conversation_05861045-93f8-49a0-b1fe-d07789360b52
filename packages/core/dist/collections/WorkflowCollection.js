"use strict";
/**
 * @file WorkflowCollection - MongoDB collection operations for agent workflows
 *
 * This class provides CRUD operations and specialized queries for agent workflows,
 * implementing workflow state management and execution tracking.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollection = void 0;
const mongodb_1 = require("mongodb");
const BaseCollection_1 = require("./BaseCollection");
/**
 * WorkflowCollection - Complete CRUD operations for agent workflows
 *
 * Features:
 * - Workflow state management
 * - Step-by-step execution tracking
 * - Error handling and recovery
 * - Variable management
 * - Performance monitoring
 */
class WorkflowCollection extends BaseCollection_1.BaseCollection {
    constructor(db) {
        super(db);
        this.collectionName = 'agent_workflows';
        this.initializeCollection();
    }
    /**
     * Create a new workflow
     */
    async createWorkflow(workflowData) {
        const now = new Date();
        const workflow = {
            ...workflowData,
            _id: new mongodb_1.ObjectId(),
            createdAt: now,
            updatedAt: now,
            status: workflowData.status || 'pending',
            currentStepIndex: 0,
            variables: workflowData.variables || {},
            metadata: workflowData.metadata || {}
        };
        await this.validateDocument(workflow);
        const result = await this.collection.insertOne(workflow);
        if (!result.acknowledged) {
            throw new Error('Failed to create workflow');
        }
        return workflow;
    }
    /**
     * Get workflow by ID
     */
    async getWorkflow(workflowId) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        return await this.collection.findOne({ _id: objectId });
    }
    /**
     * Update workflow
     */
    async updateWorkflow(workflowId, updateData) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        const now = new Date();
        const updateDoc = {
            ...updateData,
            updatedAt: now
        };
        const result = await this.collection.findOneAndUpdate({ _id: objectId }, { $set: updateDoc }, { returnDocument: 'after' });
        return result.value;
    }
    /**
     * Update workflow status
     */
    async updateWorkflowStatus(workflowId, status, error) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        const now = new Date();
        const updateDoc = {
            status,
            updatedAt: now
        };
        if (status === 'running' && !await this.hasStartedAt(objectId)) {
            updateDoc.startedAt = now;
        }
        if (status === 'completed' || status === 'failed') {
            updateDoc.completedAt = now;
        }
        if (error) {
            updateDoc.error = error;
        }
        const result = await this.collection.updateOne({ _id: objectId }, { $set: updateDoc });
        return result.modifiedCount > 0;
    }
    /**
     * Update current step
     */
    async updateCurrentStep(workflowId, stepIndex, stepResult) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        const now = new Date();
        const updateDoc = {
            currentStepIndex: stepIndex,
            updatedAt: now
        };
        // Update step result if provided
        if (stepResult !== undefined) {
            updateDoc[`steps.${stepIndex}.result`] = stepResult;
            updateDoc[`steps.${stepIndex}.completedAt`] = now;
        }
        const result = await this.collection.updateOne({ _id: objectId }, { $set: updateDoc });
        return result.modifiedCount > 0;
    }
    /**
     * Update workflow variables
     */
    async updateWorkflowVariables(workflowId, variables) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        const result = await this.collection.updateOne({ _id: objectId }, {
            $set: {
                variables,
                updatedAt: new Date()
            }
        });
        return result.modifiedCount > 0;
    }
    /**
     * Add step result
     */
    async addStepResult(workflowId, stepIndex, result, error) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        const now = new Date();
        const updateDoc = {
            [`steps.${stepIndex}.result`]: result,
            [`steps.${stepIndex}.completedAt`]: now,
            updatedAt: now
        };
        if (error) {
            updateDoc[`steps.${stepIndex}.error`] = error;
        }
        const updateResult = await this.collection.updateOne({ _id: objectId }, { $set: updateDoc });
        return updateResult.modifiedCount > 0;
    }
    /**
     * Delete workflow
     */
    async deleteWorkflow(workflowId) {
        const objectId = typeof workflowId === 'string' ? new mongodb_1.ObjectId(workflowId) : workflowId;
        const result = await this.collection.deleteOne({ _id: objectId });
        return result.deletedCount > 0;
    }
    /**
     * List workflows with filtering and pagination
     */
    async listWorkflows(filter = {}, options = {}) {
        const mongoFilter = this.buildMongoFilter(filter);
        const { limit = 50, skip = 0, sort = { createdAt: -1 } } = options;
        const [workflows, total] = await Promise.all([
            this.collection
                .find(mongoFilter)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .toArray(),
            this.collection.countDocuments(mongoFilter)
        ]);
        return { workflows, total };
    }
    /**
     * Get workflows by agent
     */
    async getAgentWorkflows(agentId, status) {
        const objectId = typeof agentId === 'string' ? new mongodb_1.ObjectId(agentId) : agentId;
        const filter = { agentId: objectId };
        if (status) {
            filter.status = status;
        }
        return await this.collection
            .find(filter)
            .sort({ createdAt: -1 })
            .toArray();
    }
    /**
     * Get running workflows
     */
    async getRunningWorkflows() {
        return await this.collection
            .find({ status: 'running' })
            .sort({ startedAt: 1 })
            .toArray();
    }
    /**
     * Get workflows by status
     */
    async getWorkflowsByStatus(status) {
        return await this.collection
            .find({ status })
            .sort({ createdAt: -1 })
            .toArray();
    }
    /**
     * Search workflows
     */
    async searchWorkflows(query, limit = 20) {
        const searchFilter = {
            $or: [
                { name: { $regex: query, $options: 'i' } },
                { description: { $regex: query, $options: 'i' } }
            ]
        };
        return await this.collection
            .find(searchFilter)
            .limit(limit)
            .toArray();
    }
    /**
     * Get workflow statistics
     */
    async getWorkflowStats(agentId) {
        const matchStage = agentId
            ? { $match: { agentId: typeof agentId === 'string' ? new mongodb_1.ObjectId(agentId) : agentId } }
            : { $match: {} };
        const pipeline = [
            matchStage,
            {
                $facet: {
                    total: [{ $count: 'count' }],
                    byStatus: [
                        { $group: { _id: '$status', count: { $sum: 1 } } }
                    ],
                    byFramework: [
                        { $group: { _id: '$framework', count: { $sum: 1 } } }
                    ],
                    executionTimes: [
                        {
                            $match: {
                                startedAt: { $exists: true },
                                completedAt: { $exists: true }
                            }
                        },
                        {
                            $project: {
                                executionTime: {
                                    $subtract: ['$completedAt', '$startedAt']
                                }
                            }
                        },
                        {
                            $group: {
                                _id: null,
                                avgTime: { $avg: '$executionTime' }
                            }
                        }
                    ],
                    successRate: [
                        {
                            $group: {
                                _id: null,
                                total: { $sum: 1 },
                                completed: {
                                    $sum: {
                                        $cond: [{ $eq: ['$status', 'completed'] }, 1, 0]
                                    }
                                }
                            }
                        },
                        {
                            $project: {
                                rate: {
                                    $cond: [
                                        { $eq: ['$total', 0] },
                                        0,
                                        { $divide: ['$completed', '$total'] }
                                    ]
                                }
                            }
                        }
                    ]
                }
            }
        ];
        const [result] = await this.collection.aggregate(pipeline).toArray();
        return {
            total: result.total[0]?.count || 0,
            byStatus: result.byStatus.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            byFramework: result.byFramework.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            averageExecutionTime: result.executionTimes[0]?.avgTime || 0,
            successRate: result.successRate[0]?.rate || 0
        };
    }
    /**
     * Cleanup old workflows
     */
    async cleanupOldWorkflows(olderThanDays = 30) {
        const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
        const result = await this.collection.deleteMany({
            status: { $in: ['completed', 'failed', 'cancelled'] },
            completedAt: { $lt: cutoffDate }
        });
        return result.deletedCount;
    }
    /**
     * Check if workflow has started
     */
    async hasStartedAt(workflowId) {
        const workflow = await this.collection.findOne({ _id: workflowId }, { projection: { startedAt: 1 } });
        return !!(workflow?.startedAt);
    }
    /**
     * Build MongoDB filter from WorkflowFilter
     */
    buildMongoFilter(filter) {
        const mongoFilter = {};
        if (filter.agentId) {
            const objectId = typeof filter.agentId === 'string' ? new mongodb_1.ObjectId(filter.agentId) : filter.agentId;
            mongoFilter.agentId = objectId;
        }
        if (filter.status) {
            mongoFilter.status = filter.status;
        }
        if (filter.framework) {
            mongoFilter.framework = filter.framework;
        }
        if (filter.tags && filter.tags.length > 0) {
            mongoFilter.tags = { $in: filter.tags };
        }
        if (filter.createdAfter || filter.createdBefore) {
            mongoFilter.createdAt = {};
            if (filter.createdAfter) {
                mongoFilter.createdAt.$gte = filter.createdAfter;
            }
            if (filter.createdBefore) {
                mongoFilter.createdAt.$lte = filter.createdBefore;
            }
        }
        if (filter.startedAfter || filter.startedBefore) {
            mongoFilter.startedAt = {};
            if (filter.startedAfter) {
                mongoFilter.startedAt.$gte = filter.startedAfter;
            }
            if (filter.startedBefore) {
                mongoFilter.startedAt.$lte = filter.startedBefore;
            }
        }
        if (filter.completedAfter || filter.completedBefore) {
            mongoFilter.completedAt = {};
            if (filter.completedAfter) {
                mongoFilter.completedAt.$gte = filter.completedAfter;
            }
            if (filter.completedBefore) {
                mongoFilter.completedAt.$lte = filter.completedBefore;
            }
        }
        return mongoFilter;
    }
    /**
     * Create indexes for optimal performance
     */
    async createIndexes() {
        await Promise.all([
            // Primary indexes
            this.collection.createIndex({ agentId: 1, status: 1 }),
            this.collection.createIndex({ status: 1 }),
            this.collection.createIndex({ framework: 1 }),
            this.collection.createIndex({ createdAt: -1 }),
            this.collection.createIndex({ startedAt: -1 }),
            this.collection.createIndex({ completedAt: -1 }),
            // Compound indexes
            this.collection.createIndex({ agentId: 1, createdAt: -1 }),
            this.collection.createIndex({ status: 1, startedAt: 1 }),
            this.collection.createIndex({ status: 1, completedAt: -1 }),
            // Text search index
            this.collection.createIndex({
                name: 'text',
                description: 'text'
            }, {
                name: 'workflow_text_search'
            }),
            // Tag index
            this.collection.createIndex({ tags: 1 })
        ]);
    }
}
exports.WorkflowCollection = WorkflowCollection;
