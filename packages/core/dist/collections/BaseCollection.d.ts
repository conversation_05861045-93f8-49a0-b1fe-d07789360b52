/**
 * @file BaseCollection - Base class for MongoDB collection operations
 *
 * This class provides common functionality for all MongoDB collections,
 * including validation, error handling, and basic CRUD operations.
 */
import { Collection, Db, ObjectId } from 'mongodb';
import Ajv from 'ajv';
export interface BaseDocument {
    _id?: ObjectId;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface PaginationOptions {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
}
export interface PaginatedResult<T> {
    documents: T[];
    total: number;
    hasMore: boolean;
    page: number;
    totalPages: number;
}
/**
 * BaseCollection - Abstract base class for MongoDB collections
 *
 * Features:
 * - JSON Schema validation
 * - Common CRUD operations
 * - Error handling and logging
 * - Pagination utilities
 * - Index management
 */
export declare abstract class BaseCollection<T extends BaseDocument> {
    protected abstract collectionName: string;
    protected collection: Collection<T>;
    protected db: Db;
    protected ajv: Ajv;
    protected schema?: object;
    constructor(db: Db);
    /**
     * Initialize the collection - must be called after constructor
     */
    protected initializeCollection(): void;
    /**
     * Load JSON schema for validation
     */
    protected loadSchema(): void;
    /**
     * Validate document against JSON schema
     */
    protected validateDocument(document: T): Promise<void>;
    /**
     * Generic find with pagination
     */
    findPaginated(filter?: any, options?: PaginationOptions): Promise<PaginatedResult<T>>;
    /**
     * Generic find one
     */
    findOne(filter: any): Promise<T | null>;
    /**
     * Generic find by ID
     */
    findById(id: string | ObjectId): Promise<T | null>;
    /**
     * Generic insert one
     */
    insertOne(document: Omit<T, '_id'>): Promise<T>;
    /**
     * Generic insert many
     */
    insertMany(documents: Omit<T, '_id'>[]): Promise<T[]>;
    /**
     * Generic update one
     */
    updateOne(filter: any, update: any, options?: {
        upsert?: boolean;
    }): Promise<T | null>;
    /**
     * Generic update by ID
     */
    updateById(id: string | ObjectId, update: any, options?: {
        upsert?: boolean;
    }): Promise<T | null>;
    /**
     * Generic delete one
     */
    deleteOne(filter: any): Promise<boolean>;
    /**
     * Generic delete by ID
     */
    deleteById(id: string | ObjectId): Promise<boolean>;
    /**
     * Generic delete many
     */
    deleteMany(filter: any): Promise<number>;
    /**
     * Count documents
     */
    count(filter?: any): Promise<number>;
    /**
     * Check if document exists
     */
    exists(filter: any): Promise<boolean>;
    /**
     * Get distinct values
     */
    distinct(field: string, filter?: any): Promise<any[]>;
    /**
     * Aggregate pipeline
     */
    aggregate(pipeline: any[]): Promise<any[]>;
    /**
     * Create text search index
     */
    createTextIndex(fields: Record<string, 'text'>, options?: any): Promise<void>;
    /**
     * Text search
     */
    textSearch(query: string, options?: {
        limit?: number;
        skip?: number;
        filter?: any;
    }): Promise<T[]>;
    /**
     * Bulk write operations
     */
    bulkWrite(operations: any[]): Promise<any>;
    /**
     * Get collection statistics
     */
    getStats(): Promise<{
        documentCount: number;
        avgDocumentSize: number;
        totalSize: number;
        indexCount: number;
    }>;
    /**
     * Create common indexes
     */
    createCommonIndexes(): Promise<void>;
    /**
     * Abstract method for creating collection-specific indexes
     */
    abstract createIndexes(): Promise<void>;
    /**
     * Drop collection
     */
    drop(): Promise<void>;
    /**
     * Get collection name
     */
    getCollectionName(): string;
    /**
     * Get MongoDB collection instance
     */
    getCollection(): Collection<T>;
}
//# sourceMappingURL=BaseCollection.d.ts.map