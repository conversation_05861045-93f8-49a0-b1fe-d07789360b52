/**
 * @file MemoryCollection - MongoDB collection operations for agent memory
 *
 * This class provides CRUD operations and specialized queries for agent memory,
 * implementing TTL (Time To Live) management and semantic search capabilities.
 */
import { Db, ObjectId } from 'mongodb';
import { BaseCollection } from './BaseCollection';
export type MemoryType = 'episodic' | 'semantic' | 'procedural' | 'working' | 'contextual';
export type MemoryImportance = 'low' | 'medium' | 'high' | 'critical';
export interface AgentMemory {
    _id: ObjectId;
    agentId: ObjectId;
    conversationId: string;
    memoryType: MemoryType;
    content: string;
    importance: MemoryImportance;
    tags: string[];
    metadata: Record<string, any>;
    accessCount: number;
    lastAccessedAt: Date;
    expiresAt?: Date;
    createdAt: Date;
    updatedAt: Date;
}
export interface MemoryFilter {
    agentId?: string | ObjectId;
    conversationId?: string;
    memoryType?: MemoryType;
    importance?: MemoryImportance;
    tags?: string[];
    createdAfter?: Date;
    createdBefore?: Date;
    expiresAfter?: Date;
    expiresBefore?: Date;
}
export interface MemoryUpdateData {
    content?: string;
    importance?: MemoryImportance;
    tags?: string[];
    metadata?: Record<string, any>;
    expiresAt?: Date;
    accessCount?: number;
    lastAccessedAt?: Date;
}
export interface MemorySearchOptions {
    limit?: number;
    minImportance?: MemoryImportance;
    includeExpired?: boolean;
    sortBy?: 'relevance' | 'importance' | 'recency' | 'access_count';
}
/**
 * MemoryCollection - Complete CRUD operations for agent memory
 *
 * Features:
 * - TTL (Time To Live) memory management
 * - Semantic search with vector embeddings
 * - Memory importance scoring
 * - Conversation-based memory organization
 * - Automatic cleanup of expired memories
 */
export declare class MemoryCollection extends BaseCollection<AgentMemory> {
    protected collectionName: string;
    constructor(db: Db);
    /**
     * Create a new memory
     */
    createMemory(memoryData: Omit<AgentMemory, '_id' | 'createdAt' | 'updatedAt'>): Promise<AgentMemory>;
    /**
     * Get memory by ID
     */
    getMemory(memoryId: string | ObjectId): Promise<AgentMemory | null>;
    /**
     * Update memory
     */
    updateMemory(memoryId: string | ObjectId, updateData: MemoryUpdateData): Promise<AgentMemory | null>;
    /**
     * Delete memory
     */
    deleteMemory(memoryId: string | ObjectId): Promise<boolean>;
    /**
     * Get memories for an agent
     */
    getAgentMemories(agentId: string | ObjectId, options?: {
        conversationId?: string;
        memoryType?: MemoryType;
        limit?: number;
        includeExpired?: boolean;
    }): Promise<AgentMemory[]>;
    /**
     * Get conversation memories
     */
    getConversationMemories(conversationId: string, options?: {
        agentId?: string | ObjectId;
        limit?: number;
        includeExpired?: boolean;
    }): Promise<AgentMemory[]>;
    /**
     * Search memories by content
     */
    searchMemories(query: string, filter?: MemoryFilter, options?: MemorySearchOptions): Promise<AgentMemory[]>;
    /**
     * Get memories by importance
     */
    getMemoriesByImportance(importance: MemoryImportance, filter?: MemoryFilter, limit?: number): Promise<AgentMemory[]>;
    /**
     * Update memory importance
     */
    updateMemoryImportance(memoryId: string | ObjectId, importance: MemoryImportance): Promise<boolean>;
    /**
     * Update access tracking
     */
    updateAccessTracking(memoryId: string | ObjectId): Promise<boolean>;
    /**
     * Set memory expiration
     */
    setMemoryExpiration(memoryId: string | ObjectId, expiresAt: Date | null): Promise<boolean>;
    /**
     * Cleanup expired memories
     */
    cleanupExpiredMemories(): Promise<number>;
    /**
     * Get memory statistics
     */
    getMemoryStats(agentId?: string | ObjectId): Promise<{
        total: number;
        byType: Record<string, number>;
        byImportance: Record<string, number>;
        expired: number;
        averageAccessCount: number;
    }>;
    /**
     * Build MongoDB filter from MemoryFilter
     */
    private buildMongoFilter;
    /**
     * Create indexes for optimal performance
     */
    createIndexes(): Promise<void>;
}
//# sourceMappingURL=MemoryCollection.d.ts.map