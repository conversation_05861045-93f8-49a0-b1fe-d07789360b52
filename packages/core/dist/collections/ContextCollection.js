"use strict";
/**
 * @file ContextCollection - MongoDB collection for context management
 *
 * This collection manages context items used for intelligent prompt enhancement.
 * It stores context with vector embeddings for semantic search and provides
 * efficient retrieval and management of contextual information.
 *
 * Features:
 * - Context storage with vector embeddings
 * - Semantic search capabilities
 * - Context relevance scoring
 * - TTL (Time To Live) support
 * - Framework-specific context organization
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextCollection = void 0;
const BaseCollection_1 = require("./BaseCollection");
/**
 * ContextCollection - Complete CRUD operations for context items
 *
 * Features:
 * - Context lifecycle management
 * - Vector embedding storage and search
 * - Relevance scoring and optimization
 * - Framework-specific context queries
 * - TTL-based automatic cleanup
 */
class ContextCollection extends BaseCollection_1.BaseCollection {
    constructor(db, collectionName) {
        super(db);
        this.collectionName = 'context_items';
        if (collectionName) {
            this.collectionName = collectionName;
        }
        this.initializeCollection();
    }
    /**
     * Create database indexes for optimal performance
     */
    async createIndexes() {
        await Promise.all([
            // Primary indexes
            this.collection.createIndex({ contextId: 1 }, { unique: true }),
            this.collection.createIndex({ source: 1 }),
            this.collection.createIndex({ 'metadata.type': 1 }),
            this.collection.createIndex({ 'metadata.framework': 1 }),
            this.collection.createIndex({ 'metadata.sessionId': 1 }),
            this.collection.createIndex({ 'metadata.userId': 1 }),
            this.collection.createIndex({ 'metadata.tags': 1 }),
            // Performance indexes
            this.collection.createIndex({ relevanceScore: -1 }),
            this.collection.createIndex({ 'metadata.importance': -1 }),
            this.collection.createIndex({ 'metadata.confidence': -1 }),
            this.collection.createIndex({ 'metadata.lastUsed': -1 }),
            this.collection.createIndex({ 'metadata.usageCount': -1 }),
            // Compound indexes for common queries
            this.collection.createIndex({
                'metadata.framework': 1,
                'metadata.type': 1,
                relevanceScore: -1
            }),
            this.collection.createIndex({
                'metadata.sessionId': 1,
                'metadata.lastUsed': -1
            }),
            this.collection.createIndex({
                'metadata.userId': 1,
                'metadata.framework': 1,
                'metadata.lastUsed': -1
            }),
            // TTL index for automatic cleanup
            this.collection.createIndex({ ttl: 1 }, { expireAfterSeconds: 0 }),
            // Vector search index (for MongoDB Atlas Vector Search)
            this.collection.createIndex({ 'embedding.values': '2dsphere' }),
            // Text search index
            this.collection.createIndex({
                content: 'text',
                source: 'text',
                'metadata.tags': 'text'
            }, { name: 'context_text_index' })
        ]);
    }
    /**
     * Store a new context item
     */
    async storeContext(contextData) {
        const context = {
            ...contextData,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        const result = await this.collection.insertOne(context);
        return { ...context, _id: result.insertedId };
    }
    /**
     * Find context items by filter
     */
    async findContext(filter = {}, options = {}) {
        const mongoFilter = this.buildMongoFilter(filter);
        const { limit = 50, skip = 0, sort = { 'metadata.lastUsed': -1 }, includeEmbeddings = false, minRelevanceScore } = options;
        // Add relevance score filter if specified
        if (minRelevanceScore !== undefined) {
            mongoFilter.relevanceScore = { $gte: minRelevanceScore };
        }
        const projection = includeEmbeddings ? {} : { 'embedding.values': 0 };
        return await this.collection
            .find(mongoFilter, { projection })
            .sort(sort)
            .skip(skip)
            .limit(limit)
            .toArray();
    }
    /**
     * Update context item
     */
    async updateContext(contextId, updateData) {
        return await this.updateOne({ contextId }, {
            $set: {
                ...updateData,
                updatedAt: new Date()
            }
        });
    }
    /**
     * Increment usage count and update last used timestamp
     */
    async recordContextUsage(contextId) {
        return await this.updateOne({ contextId }, {
            $inc: { 'metadata.usageCount': 1 },
            $set: {
                'metadata.lastUsed': new Date(),
                updatedAt: new Date()
            }
        });
    }
    /**
     * Get context statistics
     */
    async getContextStats() {
        const pipeline = [
            {
                $facet: {
                    totalCount: [{ $count: 'total' }],
                    byType: [
                        { $group: { _id: '$metadata.type', count: { $sum: 1 } } }
                    ],
                    byFramework: [
                        { $group: { _id: '$metadata.framework', count: { $sum: 1 } } }
                    ],
                    averageRelevance: [
                        { $group: { _id: null, avg: { $avg: '$relevanceScore' } } }
                    ],
                    averageUsage: [
                        { $group: { _id: null, avg: { $avg: '$metadata.usageCount' } } }
                    ],
                    recentlyUsed: [
                        {
                            $match: {
                                'metadata.lastUsed': {
                                    $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
                                }
                            }
                        },
                        { $count: 'recent' }
                    ]
                }
            }
        ];
        const results = await this.collection.aggregate(pipeline).toArray();
        const facetResults = results[0];
        return {
            totalContexts: facetResults.totalCount[0]?.total || 0,
            contextsByType: this.arrayToRecord(facetResults.byType),
            contextsByFramework: this.arrayToRecord(facetResults.byFramework),
            averageRelevanceScore: facetResults.averageRelevance[0]?.avg || 0,
            averageUsageCount: facetResults.averageUsage[0]?.avg || 0,
            recentlyUsed: facetResults.recentlyUsed[0]?.recent || 0
        };
    }
    /**
     * Clean up expired context items
     */
    async cleanupExpiredContext() {
        const result = await this.collection.deleteMany({
            ttl: { $lte: new Date() }
        });
        return result.deletedCount;
    }
    /**
     * Clean up old unused context items
     */
    async cleanupUnusedContext(daysOld = 30) {
        const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
        const result = await this.collection.deleteMany({
            $and: [
                { 'metadata.lastUsed': { $lt: cutoffDate } },
                { 'metadata.usageCount': { $lte: 1 } },
                { 'metadata.importance': { $lt: 0.3 } }
            ]
        });
        return result.deletedCount;
    }
    /**
     * Initialize collection with proper setup
     */
    async initialize() {
        await this.createIndexes();
        console.log(`✅ ContextCollection (${this.collectionName}) initialized`);
    }
    // Private helper methods
    buildMongoFilter(filter) {
        const mongoFilter = {};
        if (filter.contextId)
            mongoFilter.contextId = filter.contextId;
        if (filter.source)
            mongoFilter.source = filter.source;
        if (filter.type)
            mongoFilter['metadata.type'] = filter.type;
        if (filter.framework)
            mongoFilter['metadata.framework'] = filter.framework;
        if (filter.sessionId)
            mongoFilter['metadata.sessionId'] = filter.sessionId;
        if (filter.userId)
            mongoFilter['metadata.userId'] = filter.userId;
        if (filter.tags && filter.tags.length > 0) {
            mongoFilter['metadata.tags'] = { $in: filter.tags };
        }
        if (filter.minRelevanceScore !== undefined) {
            mongoFilter.relevanceScore = { $gte: filter.minRelevanceScore };
        }
        if (filter.minImportance !== undefined) {
            mongoFilter['metadata.importance'] = { $gte: filter.minImportance };
        }
        if (filter.minConfidence !== undefined) {
            mongoFilter['metadata.confidence'] = { $gte: filter.minConfidence };
        }
        // Date range filters
        if (filter.createdAfter || filter.createdBefore) {
            mongoFilter.createdAt = {};
            if (filter.createdAfter)
                mongoFilter.createdAt.$gte = filter.createdAfter;
            if (filter.createdBefore)
                mongoFilter.createdAt.$lte = filter.createdBefore;
        }
        if (filter.lastUsedAfter || filter.lastUsedBefore) {
            mongoFilter['metadata.lastUsed'] = {};
            if (filter.lastUsedAfter)
                mongoFilter['metadata.lastUsed'].$gte = filter.lastUsedAfter;
            if (filter.lastUsedBefore)
                mongoFilter['metadata.lastUsed'].$lte = filter.lastUsedBefore;
        }
        return mongoFilter;
    }
    arrayToRecord(array) {
        const record = {};
        array.forEach(item => {
            record[item._id] = item.count;
        });
        return record;
    }
}
exports.ContextCollection = ContextCollection;
