{"version": 3, "file": "WorkflowCollection.d.ts", "sourceRoot": "", "sources": ["../../src/collections/WorkflowCollection.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAc,EAAE,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnD,OAAO,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC7E,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,MAAM,WAAW,cAAc;IAC7B,OAAO,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC;IAC5B,MAAM,CAAC,EAAE,cAAc,CAAC;IACxB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAChB,YAAY,CAAC,EAAE,IAAI,CAAC;IACpB,aAAa,CAAC,EAAE,IAAI,CAAC;IACrB,YAAY,CAAC,EAAE,IAAI,CAAC;IACpB,aAAa,CAAC,EAAE,IAAI,CAAC;IACrB,cAAc,CAAC,EAAE,IAAI,CAAC;IACtB,eAAe,CAAC,EAAE,IAAI,CAAC;CACxB;AAED,MAAM,WAAW,kBAAkB;IACjC,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,MAAM,CAAC,EAAE,cAAc,CAAC;IACxB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC;IACvB,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAChC,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC/B,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAChB,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB,WAAW,CAAC,EAAE,IAAI,CAAC;IACnB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,wBAAwB;IACvC,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,SAAS,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAChC,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;;;;;;;;GASG;AACH,qBAAa,kBAAmB,SAAQ,cAAc,CAAC,aAAa,CAAC;IACnE,SAAS,CAAC,cAAc,SAAqB;gBAEjC,EAAE,EAAE,EAAE;IAKlB;;OAEG;IACG,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,OAAO,CAAC,aAAa,CAAC;IAuBlH;;OAEG;IACG,WAAW,CAAC,UAAU,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAK/E;;OAEG;IACG,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,QAAQ,EAAE,UAAU,EAAE,kBAAkB,GAAG,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC;IAkBlH;;OAEG;IACG,oBAAoB,CACxB,UAAU,EAAE,MAAM,GAAG,QAAQ,EAC7B,MAAM,EAAE,cAAc,EACtB,KAAK,CAAC,EAAE,MAAM,GACb,OAAO,CAAC,OAAO,CAAC;IA6BnB;;OAEG;IACG,iBAAiB,CACrB,UAAU,EAAE,MAAM,GAAG,QAAQ,EAC7B,SAAS,EAAE,MAAM,EACjB,UAAU,CAAC,EAAE,GAAG,GACf,OAAO,CAAC,OAAO,CAAC;IAuBnB;;OAEG;IACG,uBAAuB,CAC3B,UAAU,EAAE,MAAM,GAAG,QAAQ,EAC7B,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC7B,OAAO,CAAC,OAAO,CAAC;IAgBnB;;OAEG;IACG,aAAa,CACjB,UAAU,EAAE,MAAM,GAAG,QAAQ,EAC7B,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,GAAG,EACX,KAAK,CAAC,EAAE,MAAM,GACb,OAAO,CAAC,OAAO,CAAC;IAsBnB;;OAEG;IACG,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;IAMrE;;OAEG;IACG,aAAa,CACjB,MAAM,GAAE,cAAmB,EAC3B,OAAO,GAAE;QACP,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC1B,GACL,OAAO,CAAC;QAAE,SAAS,EAAE,aAAa,EAAE,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;IAkBzD;;OAEG;IACG,iBAAiB,CACrB,OAAO,EAAE,MAAM,GAAG,QAAQ,EAC1B,MAAM,CAAC,EAAE,cAAc,GACtB,OAAO,CAAC,aAAa,EAAE,CAAC;IAc3B;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;IAOrD;;OAEG;IACG,oBAAoB,CAAC,MAAM,EAAE,cAAc,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAO5E;;OAEG;IACG,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,GAAE,MAAW,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;IAclF;;OAEG;IACG,gBAAgB,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC;QAC3D,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;QACzC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpC,oBAAoB,EAAE,MAAM,CAAC;QAC7B,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IAkFF;;OAEG;IACG,mBAAmB,CAAC,aAAa,GAAE,MAAW,GAAG,OAAO,CAAC,MAAM,CAAC;IAWtE;;OAEG;YACW,YAAY;IAQ1B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAqDxB;;OAEG;IACG,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;CA2BrC"}