/**
 * @file ToolCollection - MongoDB collection operations for agent tools
 *
 * This class provides CRUD operations and specialized queries for agent tools,
 * implementing tool execution tracking, rate limiting, and cost monitoring.
 */
import { Db, ObjectId } from 'mongodb';
import { AgentTool, ToolExecution, ToolStatus } from '../types/index';
import { BaseCollection } from './BaseCollection';
export interface ToolFilter {
    agentId?: string | ObjectId;
    status?: ToolStatus;
    category?: string;
    tags?: string[];
    createdAfter?: Date;
    createdBefore?: Date;
    lastUsedAfter?: Date;
    lastUsedBefore?: Date;
}
export interface ToolUpdateData {
    name?: string;
    description?: string;
    status?: ToolStatus;
    configuration?: Record<string, any>;
    rateLimits?: {
        maxCallsPerMinute?: number;
        maxCallsPerHour?: number;
        maxCallsPerDay?: number;
    };
    costTracking?: {
        costPerCall?: number;
        currency?: string;
    };
    tags?: string[];
    metadata?: Record<string, any>;
    lastUsedAt?: Date;
}
export interface ToolExecutionFilter {
    toolId?: string | ObjectId;
    agentId?: string | ObjectId;
    status?: 'pending' | 'running' | 'completed' | 'failed';
    executedAfter?: Date;
    executedBefore?: Date;
}
/**
 * ToolCollection - Complete CRUD operations for agent tools
 *
 * Features:
 * - Tool lifecycle management
 * - Execution tracking and monitoring
 * - Rate limiting enforcement
 * - Cost tracking and analysis
 * - Performance metrics
 */
export declare class ToolCollection extends BaseCollection<AgentTool> {
    protected collectionName: string;
    private executionCollection;
    constructor(db: Db);
    /**
     * Create a new tool
     */
    createTool(toolData: Omit<AgentTool, '_id' | 'createdAt' | 'updatedAt'>): Promise<AgentTool>;
    /**
     * Get tool by ID
     */
    getTool(toolId: string | ObjectId): Promise<AgentTool | null>;
    /**
     * Get tool by name and agent
     */
    getToolByName(name: string, agentId?: string | ObjectId): Promise<AgentTool | null>;
    /**
     * Update tool
     */
    updateTool(toolId: string | ObjectId, updateData: ToolUpdateData): Promise<AgentTool | null>;
    /**
     * Update tool status
     */
    updateToolStatus(toolId: string | ObjectId, status: ToolStatus): Promise<boolean>;
    /**
     * Delete tool
     */
    deleteTool(toolId: string | ObjectId): Promise<boolean>;
    /**
     * List tools with filtering and pagination
     */
    listTools(filter?: ToolFilter, options?: {
        limit?: number;
        skip?: number;
        sort?: Record<string, 1 | -1>;
    }): Promise<{
        tools: AgentTool[];
        total: number;
    }>;
    /**
     * Get tools by agent
     */
    getAgentTools(agentId: string | ObjectId, status?: ToolStatus): Promise<AgentTool[]>;
    /**
     * Get tools by category
     */
    getToolsByCategory(category: string): Promise<AgentTool[]>;
    /**
     * Search tools
     */
    searchTools(query: string, limit?: number): Promise<AgentTool[]>;
    /**
     * Record tool execution
     */
    recordExecution(executionData: Omit<ToolExecution, '_id' | 'createdAt'>): Promise<ToolExecution>;
    /**
     * Update tool statistics after execution
     */
    updateToolStats(toolId: string | ObjectId, cost?: number): Promise<boolean>;
    /**
     * Check rate limits for tool
     */
    checkRateLimit(toolId: string | ObjectId): Promise<{
        allowed: boolean;
        limits: {
            perMinute: {
                current: number;
                max: number;
                allowed: boolean;
            };
            perHour: {
                current: number;
                max: number;
                allowed: boolean;
            };
            perDay: {
                current: number;
                max: number;
                allowed: boolean;
            };
        };
    }>;
    /**
     * Get tool executions
     */
    getToolExecutions(filter?: ToolExecutionFilter, options?: {
        limit?: number;
        skip?: number;
        sort?: Record<string, 1 | -1>;
    }): Promise<{
        executions: ToolExecution[];
        total: number;
    }>;
    /**
     * Get tool statistics
     */
    getToolStats(toolId?: string | ObjectId): Promise<{
        total: number;
        byStatus: Record<ToolStatus, number>;
        byCategory: Record<string, number>;
        totalExecutions: number;
        totalCost: number;
        averageCostPerExecution: number;
        mostUsedTools: Array<{
            name: string;
            executionCount: number;
        }>;
    }>;
    /**
     * Cleanup old executions
     */
    cleanupOldExecutions(olderThanDays?: number): Promise<number>;
    /**
     * Build MongoDB filter from ToolFilter
     */
    private buildMongoFilter;
    /**
     * Build MongoDB filter from ToolExecutionFilter
     */
    private buildExecutionFilter;
    /**
     * Create indexes for optimal performance
     */
    createIndexes(): Promise<void>;
}
//# sourceMappingURL=ToolCollection.d.ts.map