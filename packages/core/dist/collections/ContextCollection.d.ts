/**
 * @file ContextCollection - MongoDB collection for context management
 *
 * This collection manages context items used for intelligent prompt enhancement.
 * It stores context with vector embeddings for semantic search and provides
 * efficient retrieval and management of contextual information.
 *
 * Features:
 * - Context storage with vector embeddings
 * - Semantic search capabilities
 * - Context relevance scoring
 * - TTL (Time To Live) support
 * - Framework-specific context organization
 */
import { Db } from 'mongodb';
import { BaseCollection, BaseDocument } from './BaseCollection';
export interface ContextItem extends BaseDocument {
    contextId: string;
    content: string;
    source: string;
    relevanceScore: number;
    metadata: {
        type: 'conversation' | 'knowledge' | 'procedure' | 'example' | 'reference';
        framework: string;
        sessionId?: string;
        userId?: string;
        tags: string[];
        importance: number;
        confidence: number;
        lastUsed: Date;
        usageCount: number;
    };
    embedding?: {
        values: number[];
        model: string;
        dimensions: number;
    };
    ttl?: Date;
}
export interface ContextFilter {
    contextId?: string;
    source?: string;
    type?: ContextItem['metadata']['type'];
    framework?: string;
    sessionId?: string;
    userId?: string;
    tags?: string[];
    minRelevanceScore?: number;
    minImportance?: number;
    minConfidence?: number;
    createdAfter?: Date;
    createdBefore?: Date;
    lastUsedAfter?: Date;
    lastUsedBefore?: Date;
}
export interface ContextUpdateData {
    content?: string;
    source?: string;
    relevanceScore?: number;
    metadata?: Partial<ContextItem['metadata']>;
    embedding?: ContextItem['embedding'];
    ttl?: Date;
}
export interface ContextSearchOptions {
    limit?: number;
    skip?: number;
    sort?: Record<string, 1 | -1>;
    includeEmbeddings?: boolean;
    minRelevanceScore?: number;
}
/**
 * ContextCollection - Complete CRUD operations for context items
 *
 * Features:
 * - Context lifecycle management
 * - Vector embedding storage and search
 * - Relevance scoring and optimization
 * - Framework-specific context queries
 * - TTL-based automatic cleanup
 */
export declare class ContextCollection extends BaseCollection<ContextItem> {
    protected collectionName: string;
    constructor(db: Db, collectionName?: string);
    /**
     * Create database indexes for optimal performance
     */
    createIndexes(): Promise<void>;
    /**
     * Store a new context item
     */
    storeContext(contextData: Omit<ContextItem, '_id' | 'createdAt' | 'updatedAt'>): Promise<ContextItem>;
    /**
     * Find context items by filter
     */
    findContext(filter?: ContextFilter, options?: ContextSearchOptions): Promise<ContextItem[]>;
    /**
     * Update context item
     */
    updateContext(contextId: string, updateData: ContextUpdateData): Promise<ContextItem | null>;
    /**
     * Increment usage count and update last used timestamp
     */
    recordContextUsage(contextId: string): Promise<ContextItem | null>;
    /**
     * Get context statistics
     */
    getContextStats(): Promise<{
        totalContexts: number;
        contextsByType: Record<string, number>;
        contextsByFramework: Record<string, number>;
        averageRelevanceScore: number;
        averageUsageCount: number;
        recentlyUsed: number;
    }>;
    /**
     * Clean up expired context items
     */
    cleanupExpiredContext(): Promise<number>;
    /**
     * Clean up old unused context items
     */
    cleanupUnusedContext(daysOld?: number): Promise<number>;
    /**
     * Initialize collection with proper setup
     */
    initialize(): Promise<void>;
    private buildMongoFilter;
    private arrayToRecord;
}
//# sourceMappingURL=ContextCollection.d.ts.map