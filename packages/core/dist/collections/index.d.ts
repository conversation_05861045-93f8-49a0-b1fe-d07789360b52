/**
 * @file Collections Index - Export all MongoDB collection classes
 *
 * This file exports all collection classes for the Universal AI Brain,
 * providing a centralized access point for MongoDB operations.
 */
export { BaseCollection } from './BaseCollection';
export { AgentCollection } from './AgentCollection';
export { MemoryCollection } from './MemoryCollection';
export { ContextCollection } from './ContextCollection';
export { WorkflowCollection } from './WorkflowCollection';
export { ToolCollection } from './ToolCollection';
export { MetricsCollection } from './MetricsCollection';
export { TracingCollection } from './TracingCollection';
export type { AgentFilter, AgentUpdateData } from './AgentCollection';
export type { MemoryFilter, MemoryUpdateData, MemorySearchOptions } from './MemoryCollection';
export type { ContextItem, ContextFilter, ContextUpdateData, ContextSearchOptions } from './ContextCollection';
export type { WorkflowFilter, WorkflowUpdateData, WorkflowExecutionOptions } from './WorkflowCollection';
export type { ToolFilter, ToolUpdateData, ToolExecutionFilter } from './ToolCollection';
export type { MetricsFilter, MetricsAggregationOptions, TimeSeriesPoint } from './MetricsCollection';
export type { AgentTrace, AgentStep, AgentError, PerformanceMetrics, ContextItem, TokenUsage, CostBreakdown, FrameworkMetadata } from './TracingCollection';
export type { BaseDocument, PaginationOptions, PaginatedResult } from './BaseCollection';
/**
 * Collection Manager - Centralized management of all collections
 */
import { Db } from 'mongodb';
import { AgentCollection } from './AgentCollection';
import { MemoryCollection } from './MemoryCollection';
import { ContextCollection } from './ContextCollection';
import { WorkflowCollection } from './WorkflowCollection';
import { ToolCollection } from './ToolCollection';
import { MetricsCollection } from './MetricsCollection';
import { TracingCollection } from './TracingCollection';
export declare class CollectionManager {
    private db;
    agents: AgentCollection;
    memory: MemoryCollection;
    context: ContextCollection;
    workflows: WorkflowCollection;
    tools: ToolCollection;
    metrics: MetricsCollection;
    tracing: TracingCollection;
    constructor(db: Db);
    /**
     * Initialize all collections and create indexes
     */
    initialize(): Promise<void>;
    /**
     * Get collection statistics
     */
    getCollectionStats(): Promise<{
        agents: any;
        memory: any;
        context: any;
        workflows: any;
        tools: any;
        metrics: any;
        tracing: any;
    }>;
    /**
     * Cleanup old data from all collections
     */
    cleanupOldData(options?: {
        agentInactiveDays?: number;
        memoryExpirationDays?: number;
        workflowCompletedDays?: number;
        toolExecutionDays?: number;
        metricsRetentionDays?: number;
        tracingRetentionDays?: number;
    }): Promise<{
        agentsDeleted: number;
        memoriesDeleted: number;
        workflowsDeleted: number;
        toolExecutionsDeleted: number;
        metricsDeleted: number;
        tracesDeleted: number;
    }>;
    /**
     * Health check for all collections
     */
    healthCheck(): Promise<{
        healthy: boolean;
        collections: Record<string, {
            accessible: boolean;
            documentCount: number;
            error?: string;
        }>;
    }>;
    /**
     * Drop all collections (use with caution!)
     */
    dropAllCollections(): Promise<void>;
    /**
     * Get database instance
     */
    getDatabase(): Db;
    /**
     * Get collection by name
     */
    getCollection(name: string): any;
}
//# sourceMappingURL=index.d.ts.map