{"version": 3, "file": "TracingCollection.d.ts", "sourceRoot": "", "sources": ["../../src/collections/TracingCollection.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AAGhE,MAAM,WAAW,SAAS;IACxB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE,mBAAmB,GAAG,oBAAoB,GAAG,gBAAgB,GAAG,qBAAqB,GAAG,cAAc,GAAG,gBAAgB,CAAC;IACpI,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,CAAC;IACrE,KAAK,CAAC,EAAE,GAAG,CAAC;IACZ,MAAM,CAAC,EAAE,GAAG,CAAC;IACb,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,UAAU;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,kBAAkB,GAAG,iBAAiB,GAAG,eAAe,GAAG,eAAe,GAAG,eAAe,GAAG,kBAAkB,GAAG,eAAe,CAAC;IAC/I,OAAO,EAAE,MAAM,CAAC;IAChB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACvB,SAAS,EAAE,IAAI,CAAC;IAChB,WAAW,EAAE,OAAO,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,kBAAkB;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,oBAAoB,EAAE,MAAM,CAAC;IAC7B,qBAAqB,EAAE,MAAM,CAAC;IAC9B,iBAAiB,EAAE,MAAM,CAAC;IAC1B,sBAAsB,EAAE,MAAM,CAAC;IAC/B,iBAAiB,EAAE,MAAM,CAAC;IAG1B,WAAW,CAAC,EAAE;QACZ,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;IAGF,cAAc,CAAC,EAAE;QACf,YAAY,EAAE,MAAM,CAAC;QACrB,UAAU,EAAE,MAAM,CAAC;QACnB,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,WAAW;IAC1B,SAAS,EAAE,MAAM,CAAC;IAClB,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,cAAc,EAAE,MAAM,CAAC;IACvB,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,UAAU;IACzB,YAAY,EAAE,MAAM,CAAC;IACrB,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,CAAC,EAAE,MAAM,CAAC;IAGzB,eAAe,CAAC,EAAE;QAChB,WAAW,EAAE,MAAM,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC;QACrB,eAAe,CAAC,EAAE,MAAM,CAAC;KAC1B,CAAC;CACH;AAED,MAAM,WAAW,aAAa;IAC5B,SAAS,EAAE,MAAM,CAAC;IAGlB,aAAa,EAAE,MAAM,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;IAGnB,cAAc,CAAC,EAAE;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,OAAO,EAAE,MAAM,CAAC;QAChB,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC1C,CAAC;IAGF,UAAU,CAAC,EAAE;QACX,gBAAgB,EAAE,MAAM,CAAC;QACzB,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;IAEF,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE,IAAI,CAAC;CACpB;AAED,MAAM,WAAW,iBAAiB;IAChC,aAAa,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,SAAS,CAAC;IAClF,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAG1B,QAAQ,CAAC,EAAE;QACT,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,OAAO,CAAC;QACnB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;KAClB,CAAC;IAEF,MAAM,CAAC,EAAE;QACP,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC;IAEF,YAAY,CAAC,EAAE;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;KAClB,CAAC;IAEF,SAAS,CAAC,EAAE;QACV,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,WAAW,CAAC,EAAE,MAAM,CAAC;KACtB,CAAC;CACH;AAGD,MAAM,WAAW,UAAW,SAAQ,YAAY;IAE9C,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,QAAQ,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,cAAc,CAAC,EAAE,MAAM,CAAC;IAGxB,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,CAAC,EAAE,IAAI,CAAC;IACf,aAAa,CAAC,EAAE,MAAM,CAAC;IAGvB,MAAM,EAAE,QAAQ,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,GAAG,SAAS,CAAC;IAGpE,SAAS,EAAE;QACT,IAAI,EAAE,eAAe,GAAG,aAAa,GAAG,iBAAiB,GAAG,MAAM,GAAG,kBAAkB,GAAG,gBAAgB,GAAG,QAAQ,CAAC;QACtH,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,EAAE,MAAM,CAAC;QAClB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC;KACrD,CAAC;IAGF,KAAK,EAAE,SAAS,EAAE,CAAC;IAGnB,WAAW,EAAE,kBAAkB,CAAC;IAChC,MAAM,EAAE,UAAU,EAAE,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;IAGpB,WAAW,EAAE,WAAW,EAAE,CAAC;IAC3B,gBAAgB,CAAC,EAAE;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IAGF,UAAU,EAAE,UAAU,CAAC;IACvB,IAAI,EAAE,aAAa,CAAC;IAGpB,SAAS,EAAE,iBAAiB,CAAC;IAG7B,YAAY,CAAC,EAAE;QACb,WAAW,EAAE,OAAO,CAAC;QACrB,eAAe,EAAE,OAAO,CAAC;QACzB,kBAAkB,CAAC,EAAE,MAAM,CAAC;QAC5B,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;KAC5B,CAAC;IAGF,SAAS,CAAC,EAAE;QACV,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;QACpB,cAAc,CAAC,EAAE,GAAG,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;KACvB,CAAC;IAGF,WAAW,CAAC,EAAE;QACZ,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC;IAGF,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;IAGhB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED;;;;;GAKG;AACH,qBAAa,iBAAkB,SAAQ,cAAc,CAAC,UAAU,CAAC;IAC/D,SAAS,CAAC,cAAc,SAAkB;gBAE9B,EAAE,EAAE,GAAG;IAKnB;;OAEG;IACG,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;IAqDpC;;OAEG;IACG,UAAU,CAAC,SAAS,EAAE;QAC1B,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,QAAQ,CAAC;QAClB,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,SAAS,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;QACnC,SAAS,EAAE,iBAAiB,CAAC;QAC7B,WAAW,CAAC,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;QACxC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KAChC,GAAG,OAAO,CAAC,UAAU,CAAC;IAkCvB;;OAEG;IACG,aAAa,CACjB,OAAO,EAAE,MAAM,EACf,UAAU,EAAE;QACV,MAAM,EAAE,WAAW,GAAG,QAAQ,GAAG,WAAW,GAAG,SAAS,CAAC;QACzD,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,CAAC;QACnD,WAAW,CAAC,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;QAC1C,UAAU,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAC9B,MAAM,CAAC,EAAE,UAAU,EAAE,CAAC;QACtB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;QACpB,YAAY,CAAC,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;QAC1C,SAAS,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;KACrC,GACA,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAsE7B;;OAEG;IACG,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAU3E;;OAEG;IACG,UAAU,CACd,OAAO,EAAE,MAAM,EACf,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,OAAO,CAAC,SAAS,CAAC,GAC7B,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAkB7B;;OAEG;IACG,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAU9F;;OAEG;IACG,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;IAUjF;;OAEG;IACG,gBAAgB,CACpB,OAAO,EAAE,QAAQ,EACjB,OAAO,GAAE;QACP,MAAM,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC9B,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,IAAI,CAAC;QACjB,OAAO,CAAC,EAAE,IAAI,CAAC;QACf,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;KACV,GACL,OAAO,CAAC,UAAU,EAAE,CAAC;IA6BxB;;OAEG;IACG,uBAAuB,CAC3B,SAAS,EAAE,IAAI,EACf,OAAO,EAAE,IAAI,EACb,OAAO,GAAE,MAAM,GAAG,KAAK,GAAG,WAAW,GAAG,WAAmB,GAC1D,OAAO,CAAC,GAAG,EAAE,CAAC;IA+EjB;;OAEG;IACG,gBAAgB,CACpB,SAAS,EAAE,IAAI,EACf,OAAO,EAAE,IAAI,GACZ,OAAO,CAAC;QACT,YAAY,EAAE,GAAG,EAAE,CAAC;QACpB,iBAAiB,EAAE,GAAG,EAAE,CAAC;QACzB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IA6DF;;OAEG;IACG,eAAe,CAAC,KAAK,GAAE,MAAY,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;IAQjE;;OAEG;IACG,kBAAkB,CACtB,SAAS,EAAE,MAAM,EACjB,OAAO,GAAE;QAAE,KAAK,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,MAAM,CAAA;KAAO,GAC9C,OAAO,CAAC,UAAU,EAAE,CAAC;IASxB;;OAEG;IACG,gBAAgB,CAAC,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CAW/D"}