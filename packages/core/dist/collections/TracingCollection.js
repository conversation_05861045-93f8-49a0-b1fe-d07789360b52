"use strict";
/**
 * @file TracingCollection - Enterprise-grade agent tracing and observability
 *
 * This collection provides comprehensive tracing for all agent operations,
 * enabling real-time monitoring, performance analysis, and debugging across
 * all framework integrations (Vercel AI, Mastra, OpenAI Agents, LangChain).
 *
 * Features:
 * - Real-time trace monitoring with MongoDB Change Streams
 * - Performance metrics and cost tracking
 * - Error analysis and debugging information
 * - Framework-specific operation tracking
 * - Time-series data optimization
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TracingCollection = void 0;
const BaseCollection_1 = require("./BaseCollection");
/**
 * TracingCollection - Enterprise-grade agent tracing and observability
 *
 * This collection stores comprehensive trace data for all agent operations,
 * optimized for time-series queries and real-time monitoring.
 */
class TracingCollection extends BaseCollection_1.BaseCollection {
    constructor(db) {
        super(db);
        this.collectionName = 'agent_traces';
        this.initializeCollection();
    }
    /**
     * Create specialized indexes for tracing queries
     */
    async createIndexes() {
        await Promise.all([
            // Common indexes
            this.createCommonIndexes(),
            // Tracing-specific indexes
            this.collection.createIndex({ traceId: 1 }, { unique: true }),
            this.collection.createIndex({ agentId: 1, startTime: -1 }),
            this.collection.createIndex({ sessionId: 1, startTime: -1 }),
            this.collection.createIndex({ conversationId: 1, startTime: -1 }),
            // Status and operation indexes
            this.collection.createIndex({ status: 1, startTime: -1 }),
            this.collection.createIndex({ 'operation.type': 1, startTime: -1 }),
            this.collection.createIndex({ 'framework.frameworkName': 1, startTime: -1 }),
            // Performance indexes
            this.collection.createIndex({ 'performance.totalDuration': -1 }),
            this.collection.createIndex({ 'cost.totalCost': -1 }),
            this.collection.createIndex({ 'tokensUsed.totalTokens': -1 }),
            // Time-series indexes for analytics
            this.collection.createIndex({ startTime: -1, endTime: -1 }),
            this.collection.createIndex({
                startTime: -1,
                'framework.frameworkName': 1,
                status: 1
            }),
            // Error analysis indexes
            this.collection.createIndex({ 'errors.errorType': 1, startTime: -1 }),
            this.collection.createIndex({ 'errors.recoverable': 1, startTime: -1 }),
            // User and session indexes
            this.collection.createIndex({ 'userContext.userId': 1, startTime: -1 }),
            this.collection.createIndex({ tags: 1, startTime: -1 }),
            // Compound indexes for common queries
            this.collection.createIndex({
                agentId: 1,
                status: 1,
                startTime: -1
            }),
            this.collection.createIndex({
                'framework.frameworkName': 1,
                'operation.type': 1,
                startTime: -1
            })
        ]);
        console.log('✅ TracingCollection indexes created successfully');
    }
    /**
     * Start a new trace for an agent operation
     */
    async startTrace(traceData) {
        const trace = {
            ...traceData,
            startTime: new Date(),
            status: 'active',
            steps: [],
            performance: {
                totalDuration: 0,
                contextRetrievalTime: 0,
                promptEnhancementTime: 0,
                frameworkCallTime: 0,
                responseProcessingTime: 0,
                memoryStorageTime: 0
            },
            errors: [],
            contextUsed: [],
            tokensUsed: {
                promptTokens: 0,
                completionTokens: 0,
                totalTokens: 0
            },
            cost: {
                totalCost: 0,
                embeddingCost: 0,
                completionCost: 0,
                promptCost: 0,
                currency: 'USD',
                calculatedAt: new Date()
            }
        };
        return await this.insertOne(trace);
    }
    /**
     * Complete a trace with final results
     */
    async completeTrace(traceId, completion) {
        const endTime = new Date();
        // Calculate total duration
        const trace = await this.findOne({ traceId });
        if (!trace) {
            throw new Error(`Trace ${traceId} not found`);
        }
        const totalDuration = endTime.getTime() - trace.startTime.getTime();
        const updateData = {
            $set: {
                endTime,
                totalDuration,
                status: completion.status,
                'operation.finalOutput': completion.finalOutput,
                'operation.outputType': completion.outputType,
                updatedAt: new Date()
            },
            $push: {},
            $inc: {}
        };
        // Update performance metrics
        if (completion.performance) {
            Object.keys(completion.performance).forEach(key => {
                updateData.$set[`performance.${key}`] = completion.performance[key];
            });
            updateData.$set['performance.totalDuration'] = totalDuration;
        }
        // Update token usage
        if (completion.tokensUsed) {
            Object.keys(completion.tokensUsed).forEach(key => {
                updateData.$set[`tokensUsed.${key}`] = completion.tokensUsed[key];
            });
        }
        // Update cost breakdown
        if (completion.cost) {
            Object.keys(completion.cost).forEach(key => {
                updateData.$set[`cost.${key}`] = completion.cost[key];
            });
            updateData.$set['cost.calculatedAt'] = new Date();
        }
        // Add errors if any
        if (completion.errors && completion.errors.length > 0) {
            updateData.$push = { errors: { $each: completion.errors } };
        }
        // Add warnings if any
        if (completion.warnings && completion.warnings.length > 0) {
            updateData.$push = { ...updateData.$push, warnings: { $each: completion.warnings } };
        }
        // Add safety checks
        if (completion.safetyChecks) {
            updateData.$set['safetyChecks'] = completion.safetyChecks;
        }
        // Add debug info
        if (completion.debugInfo) {
            updateData.$set['debugInfo'] = completion.debugInfo;
        }
        return await this.updateOne({ traceId }, updateData);
    }
    /**
     * Add a step to an active trace
     */
    async addStep(traceId, step) {
        return await this.updateOne({ traceId, status: 'active' }, {
            $push: { steps: step },
            $set: { updatedAt: new Date() }
        });
    }
    /**
     * Update a specific step in a trace
     */
    async updateStep(traceId, stepId, stepUpdate) {
        const updateFields = {};
        Object.keys(stepUpdate).forEach(key => {
            updateFields[`steps.$.${key}`] = stepUpdate[key];
        });
        return await this.updateOne({ traceId, 'steps.stepId': stepId }, {
            $set: {
                ...updateFields,
                updatedAt: new Date()
            }
        });
    }
    /**
     * Add context items used in a trace
     */
    async addContextUsed(traceId, contextItems) {
        return await this.updateOne({ traceId }, {
            $push: { contextUsed: { $each: contextItems } },
            $set: { updatedAt: new Date() }
        });
    }
    /**
     * Record an error in a trace
     */
    async recordError(traceId, error) {
        return await this.updateOne({ traceId }, {
            $push: { errors: error },
            $set: { updatedAt: new Date() }
        });
    }
    /**
     * Get traces by agent with pagination and filtering
     */
    async getTracesByAgent(agentId, options = {}) {
        const filter = { agentId };
        if (options.status) {
            filter.status = options.status;
        }
        if (options.framework) {
            filter['framework.frameworkName'] = options.framework;
        }
        if (options.startDate || options.endDate) {
            filter.startTime = {};
            if (options.startDate) {
                filter.startTime.$gte = options.startDate;
            }
            if (options.endDate) {
                filter.startTime.$lte = options.endDate;
            }
        }
        return await this.collection
            .find(filter)
            .sort({ startTime: -1 })
            .skip(options.skip || 0)
            .limit(options.limit || 50)
            .toArray();
    }
    /**
     * Get performance analytics for a time period
     */
    async getPerformanceAnalytics(startDate, endDate, groupBy = 'day') {
        const pipeline = [
            {
                $match: {
                    startTime: { $gte: startDate, $lte: endDate },
                    status: 'completed'
                }
            }
        ];
        // Group by different dimensions
        switch (groupBy) {
            case 'hour':
                pipeline.push({
                    $group: {
                        _id: {
                            year: { $year: '$startTime' },
                            month: { $month: '$startTime' },
                            day: { $dayOfMonth: '$startTime' },
                            hour: { $hour: '$startTime' }
                        },
                        avgDuration: { $avg: '$performance.totalDuration' },
                        totalTraces: { $sum: 1 },
                        totalCost: { $sum: '$cost.totalCost' },
                        totalTokens: { $sum: '$tokensUsed.totalTokens' },
                        errorCount: { $sum: { $size: '$errors' } }
                    }
                });
                break;
            case 'day':
                pipeline.push({
                    $group: {
                        _id: {
                            year: { $year: '$startTime' },
                            month: { $month: '$startTime' },
                            day: { $dayOfMonth: '$startTime' }
                        },
                        avgDuration: { $avg: '$performance.totalDuration' },
                        totalTraces: { $sum: 1 },
                        totalCost: { $sum: '$cost.totalCost' },
                        totalTokens: { $sum: '$tokensUsed.totalTokens' },
                        errorCount: { $sum: { $size: '$errors' } }
                    }
                });
                break;
            case 'framework':
                pipeline.push({
                    $group: {
                        _id: '$framework.frameworkName',
                        avgDuration: { $avg: '$performance.totalDuration' },
                        totalTraces: { $sum: 1 },
                        totalCost: { $sum: '$cost.totalCost' },
                        totalTokens: { $sum: '$tokensUsed.totalTokens' },
                        errorCount: { $sum: { $size: '$errors' } }
                    }
                });
                break;
            case 'operation':
                pipeline.push({
                    $group: {
                        _id: '$operation.type',
                        avgDuration: { $avg: '$performance.totalDuration' },
                        totalTraces: { $sum: 1 },
                        totalCost: { $sum: '$cost.totalCost' },
                        totalTokens: { $sum: '$tokensUsed.totalTokens' },
                        errorCount: { $sum: { $size: '$errors' } }
                    }
                });
                break;
        }
        pipeline.push({ $sort: { _id: 1 } });
        return await this.aggregate(pipeline);
    }
    /**
     * Get error analysis for debugging
     */
    async getErrorAnalysis(startDate, endDate) {
        const pipeline = [
            {
                $match: {
                    startTime: { $gte: startDate, $lte: endDate },
                    errors: { $exists: true, $ne: [] }
                }
            },
            {
                $unwind: '$errors'
            },
            {
                $facet: {
                    errorsByType: [
                        {
                            $group: {
                                _id: '$errors.errorType',
                                count: { $sum: 1 },
                                recoverableCount: {
                                    $sum: { $cond: ['$errors.recoverable', 1, 0] }
                                }
                            }
                        },
                        { $sort: { count: -1 } }
                    ],
                    errorsByFramework: [
                        {
                            $group: {
                                _id: '$framework.frameworkName',
                                count: { $sum: 1 },
                                errorTypes: { $addToSet: '$errors.errorType' }
                            }
                        },
                        { $sort: { count: -1 } }
                    ],
                    totalStats: [
                        {
                            $group: {
                                _id: null,
                                totalErrors: { $sum: 1 },
                                recoverableErrors: {
                                    $sum: { $cond: ['$errors.recoverable', 1, 0] }
                                }
                            }
                        }
                    ]
                }
            }
        ];
        const result = await this.aggregate(pipeline);
        const stats = result[0];
        return {
            errorsByType: stats.errorsByType,
            errorsByFramework: stats.errorsByFramework,
            recoverableErrors: stats.totalStats[0]?.recoverableErrors || 0,
            totalErrors: stats.totalStats[0]?.totalErrors || 0
        };
    }
    /**
     * Get active traces (for real-time monitoring)
     */
    async getActiveTraces(limit = 100) {
        return await this.collection
            .find({ status: 'active' })
            .sort({ startTime: -1 })
            .limit(limit)
            .toArray();
    }
    /**
     * Get traces by session for conversation analysis
     */
    async getTracesBySession(sessionId, options = {}) {
        return await this.collection
            .find({ sessionId })
            .sort({ startTime: -1 })
            .skip(options.skip || 0)
            .limit(options.limit || 50)
            .toArray();
    }
    /**
     * Clean up old traces (for data retention)
     */
    async cleanupOldTraces(olderThanDays) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        const result = await this.collection.deleteMany({
            startTime: { $lt: cutoffDate },
            status: { $in: ['completed', 'failed', 'cancelled'] }
        });
        return result.deletedCount;
    }
}
exports.TracingCollection = TracingCollection;
