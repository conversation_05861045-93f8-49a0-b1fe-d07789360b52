/**
 * @file MetricsCollection - MongoDB collection operations for performance metrics
 *
 * This class provides CRUD operations and specialized queries for agent performance metrics,
 * implementing time series data collection and analytics.
 */
import { Db, ObjectId } from 'mongodb';
import { AgentPerformanceMetrics } from '../types/index';
import { BaseCollection } from './BaseCollection';
export interface MetricsFilter {
    agentId?: string | ObjectId;
    framework?: string;
    metricType?: string;
    recordedAfter?: Date;
    recordedBefore?: Date;
}
export interface MetricsAggregationOptions {
    groupBy?: 'hour' | 'day' | 'week' | 'month';
    metrics?: string[];
    limit?: number;
}
export interface TimeSeriesPoint {
    timestamp: Date;
    value: number;
    metadata?: Record<string, any>;
}
/**
 * MetricsCollection - Complete CRUD operations for performance metrics
 *
 * Features:
 * - Time series metrics collection
 * - Performance analytics and aggregation
 * - Real-time monitoring capabilities
 * - Historical trend analysis
 * - Custom metric types support
 */
export declare class MetricsCollection extends BaseCollection<AgentPerformanceMetrics> {
    protected collectionName: string;
    constructor(db: Db);
    /**
     * Record a new metric
     */
    recordMetric(metricData: Omit<AgentPerformanceMetrics, '_id' | 'createdAt'>): Promise<AgentPerformanceMetrics>;
    /**
     * Record multiple metrics in batch
     */
    recordMetrics(metricsData: Omit<AgentPerformanceMetrics, '_id' | 'createdAt'>[]): Promise<AgentPerformanceMetrics[]>;
    /**
     * Get metrics for an agent
     */
    getAgentMetrics(agentId: string | ObjectId, options?: {
        metricType?: string;
        startDate?: Date;
        endDate?: Date;
        limit?: number;
    }): Promise<AgentPerformanceMetrics[]>;
    /**
     * Get latest metrics for an agent
     */
    getLatestMetrics(agentId: string | ObjectId, metricTypes?: string[]): Promise<Record<string, AgentPerformanceMetrics>>;
    /**
     * Get aggregated metrics over time
     */
    getAggregatedMetrics(filter?: MetricsFilter, options?: MetricsAggregationOptions): Promise<{
        timeSeries: Array<{
            timestamp: Date;
            metrics: Record<string, {
                avg: number;
                min: number;
                max: number;
                count: number;
            }>;
        }>;
        summary: Record<string, {
            avg: number;
            min: number;
            max: number;
            total: number;
        }>;
    }>;
    /**
     * Get performance trends
     */
    getPerformanceTrends(agentId: string | ObjectId, metricType: string, days?: number): Promise<{
        trend: 'improving' | 'declining' | 'stable';
        changePercentage: number;
        dataPoints: TimeSeriesPoint[];
    }>;
    /**
     * Get real-time metrics dashboard data
     */
    getDashboardData(agentId?: string | ObjectId): Promise<{
        currentMetrics: Record<string, number>;
        recentTrends: Record<string, {
            value: number;
            change: number;
        }>;
        alerts: Array<{
            metric: string;
            value: number;
            threshold: number;
            severity: 'warning' | 'critical';
        }>;
    }>;
    /**
     * Cleanup old metrics
     */
    cleanupOldMetrics(olderThanDays?: number): Promise<number>;
    /**
     * Get group format for time aggregation
     */
    private getGroupFormat;
    /**
     * Build MongoDB filter from MetricsFilter
     */
    private buildMongoFilter;
    /**
     * Create indexes for optimal performance
     */
    createIndexes(): Promise<void>;
}
//# sourceMappingURL=MetricsCollection.d.ts.map