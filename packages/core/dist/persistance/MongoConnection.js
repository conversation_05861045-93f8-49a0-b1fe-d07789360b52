"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoConnection = void 0;
const mongodb_1 = require("mongodb");
class MongoConnection {
    constructor(config) {
        this.isConnected = false;
        this.config = config;
        // Optimized connection options for Atlas
        const defaultOptions = {
            maxPoolSize: 10,
            serverSelectionTimeoutMS: 5000,
            socketTimeoutMS: 45000,
            family: 4, // Use IPv4, skip trying IPv6
            retryWrites: true,
            retryReads: true,
            ...config.options
        };
        this.client = new mongodb_1.MongoClient(config.uri, defaultOptions);
        this.db = this.client.db(config.dbName);
    }
    static getInstance(config) {
        if (!MongoConnection.instance) {
            MongoConnection.instance = new MongoConnection(config);
        }
        return MongoConnection.instance;
    }
    async connect() {
        if (this.isConnected) {
            return;
        }
        try {
            await this.client.connect();
            // Test the connection
            await this.client.db('admin').command({ ping: 1 });
            this.isConnected = true;
            console.log(`✅ Connected to MongoDB Atlas: ${this.config.dbName}`);
        }
        catch (error) {
            this.isConnected = false;
            console.error('❌ MongoDB connection failed:', error);
            throw new Error(`Failed to connect to MongoDB: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async disconnect() {
        if (!this.isConnected) {
            return;
        }
        try {
            await this.client.close();
            this.isConnected = false;
            console.log('✅ Disconnected from MongoDB Atlas');
        }
        catch (error) {
            console.error('❌ Error disconnecting from MongoDB:', error);
            throw error;
        }
    }
    getDb() {
        if (!this.isConnected) {
            throw new Error('MongoDB not connected. Call connect() first.');
        }
        return this.db;
    }
    getClient() {
        if (!this.isConnected) {
            throw new Error('MongoDB not connected. Call connect() first.');
        }
        return this.client;
    }
    isConnectionActive() {
        return this.isConnected;
    }
    async healthCheck() {
        try {
            await this.client.db('admin').command({ ping: 1 });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.MongoConnection = MongoConnection;
