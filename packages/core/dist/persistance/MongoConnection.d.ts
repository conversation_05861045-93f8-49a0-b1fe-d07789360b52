import { MongoClient, Db, MongoClientOptions } from 'mongodb';
export interface MongoConnectionConfig {
    uri: string;
    dbName: string;
    options?: MongoClientOptions;
}
export declare class MongoConnection {
    private static instance;
    private client;
    private db;
    private config;
    private isConnected;
    private constructor();
    static getInstance(config: MongoConnectionConfig): MongoConnection;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getDb(): Db;
    getClient(): MongoClient;
    isConnectionActive(): boolean;
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=MongoConnection.d.ts.map