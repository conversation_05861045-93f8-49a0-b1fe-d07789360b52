import { Db, Document } from 'mongodb';
import { IEmbeddingStore, EmbeddedDocument, SimilaritySearchResult } from './IEmbeddingStore';
export declare class MongoEmbeddingProvider<T extends Document> implements IEmbeddingStore<T> {
    private collection;
    private indexName;
    constructor(db: Db, collectionName: string, indexName: string);
    add(doc: EmbeddedDocument<T>): Promise<void>;
    addMany(docs: EmbeddedDocument<T>[]): Promise<void>;
    findSimilar(query: number[], options?: {
        k?: number;
        filter?: any;
    }): Promise<SimilaritySearchResult<T>[]>;
    /**
     * Enhanced vector search with metadata filtering and score thresholds
     * Based on MongoDB's production RAG implementation
     */
    vectorSearchWithMetadata(query: number[], options?: {
        k?: number;
        filter?: any;
        minScore?: number;
        includeMetadata?: boolean;
    }): Promise<SimilaritySearchResult<T>[]>;
    /**
     * Hybrid search combining vector search with text search
     * Essential for production RAG applications
     */
    hybridSearch(query: number[], textQuery?: string, options?: {
        k?: number;
        filter?: any;
        minScore?: number;
    }): Promise<SimilaritySearchResult<T>[]>;
    /**
     * Get collection statistics for monitoring
     */
    getStats(): Promise<any>;
}
//# sourceMappingURL=MongoEmbeddingProvider.d.ts.map