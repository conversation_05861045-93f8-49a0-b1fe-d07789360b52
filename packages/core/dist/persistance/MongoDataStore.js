"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoDataStore = void 0;
const mongodb_1 = require("mongodb");
class MongoDataStore {
    constructor(db, collectionName) {
        this.collection = db.collection(collectionName);
    }
    async create(item) {
        const result = await this.collection.insertOne(item);
        return { ...item, _id: result.insertedId };
    }
    async read(id) {
        const result = await this.collection.findOne({ _id: new mongodb_1.ObjectId(id) });
        return result;
    }
    async find(filter, options) {
        const results = await this.collection.find(filter, options).toArray();
        return results;
    }
    async findOne(filter, options) {
        const result = await this.collection.findOne(filter, options);
        return result;
    }
    async update(id, item) {
        await this.collection.updateOne({ _id: new mongodb_1.ObjectId(id) }, item);
        return this.read(id);
    }
    async delete(id) {
        const result = await this.collection.deleteOne({ _id: new mongodb_1.ObjectId(id) });
        return result.deletedCount === 1;
    }
}
exports.MongoDataStore = MongoDataStore;
