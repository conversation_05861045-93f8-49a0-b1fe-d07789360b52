import { Db } from 'mongodb';
import { IMemoryStore, Message, SessionMetadata } from './IMemoryStore';
export declare class MongoMemoryProvider implements IMemoryStore {
    private collection;
    private ttlHours;
    constructor(db: Db, ttlHours?: number);
    getHistory(agentId: string, sessionId: string, options?: {
        limit?: number;
    }): Promise<Message[]>;
    addMessage(agentId: string, sessionId: string, message: Message): Promise<void>;
    clearSession(agentId: string, sessionId: string): Promise<void>;
    getSessionMetadata(agentId: string, sessionId: string): Promise<SessionMetadata | null>;
    updateSessionMetadata(agentId: string, sessionId: string, metadata: Partial<SessionMetadata>): Promise<void>;
}
//# sourceMappingURL=MongoMemoryProvider.d.ts.map