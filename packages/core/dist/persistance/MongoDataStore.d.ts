import { Db, Filter, FindOptions, UpdateFilter, Document } from 'mongodb';
import { IDataStore } from './IDataStore';
export declare class MongoDataStore<T extends Document> implements IDataStore<T> {
    private collection;
    constructor(db: Db, collectionName: string);
    create(item: T): Promise<T>;
    read(id: string): Promise<T | null>;
    find(filter: Filter<T>, options?: FindOptions<T>): Promise<T[]>;
    findOne(filter: Filter<T>, options?: FindOptions<T>): Promise<T | null>;
    update(id: string, item: UpdateFilter<T>): Promise<T | null>;
    delete(id: string): Promise<boolean>;
}
//# sourceMappingURL=MongoDataStore.d.ts.map