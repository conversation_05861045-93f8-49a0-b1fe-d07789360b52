"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongoMemoryProvider = void 0;
const validator_1 = require("../schemas/validator");
const MEMORY_COLLECTION = 'agent_working_memory';
class MongoMemoryProvider {
    constructor(db, ttlHours = 3) {
        this.collection = db.collection(MEMORY_COLLECTION);
        this.ttlHours = ttlHours;
    }
    async getHistory(agentId, sessionId, options) {
        const memory = await this.collection.findOne({
            agent_id: agentId,
            session_id: sessionId
        });
        if (!memory) {
            return [];
        }
        const messages = memory.context_window.map(msg => ({
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp
        }));
        if (options?.limit) {
            return messages.slice(-options.limit);
        }
        return messages;
    }
    async addMessage(agentId, sessionId, message) {
        const ttlMs = this.ttlHours * 60 * 60 * 1000;
        const now = new Date();
        const expires_at = new Date(now.getTime() + ttlMs);
        const contextMessage = {
            role: message.role,
            content: message.content,
            timestamp: message.timestamp || now
        };
        // Try to update existing document
        const result = await this.collection.updateOne({ agent_id: agentId, session_id: sessionId }, {
            $push: { context_window: contextMessage },
            $set: { expires_at },
        });
        // If no document exists, create a new one
        if (result.matchedCount === 0) {
            const newDoc = {
                session_id: sessionId,
                agent_id: agentId,
                created_at: now,
                expires_at,
                context_window: [contextMessage],
                working_state: {
                    current_task: 'conversation',
                    progress: 0,
                    confidence: 1.0,
                    variables: {}
                },
                temp_findings: {}
            };
            // Convert dates to ISO strings for validation
            const validationDoc = {
                ...newDoc,
                created_at: newDoc.created_at.toISOString(),
                expires_at: newDoc.expires_at.toISOString(),
                context_window: newDoc.context_window.map(msg => ({
                    ...msg,
                    timestamp: msg.timestamp.toISOString()
                }))
            };
            // Validate the document
            validator_1.SchemaValidator.validateOrThrow('agentWorkingMemory', validationDoc);
            await this.collection.insertOne(newDoc);
        }
    }
    async clearSession(agentId, sessionId) {
        await this.collection.deleteOne({ agent_id: agentId, session_id: sessionId });
    }
    async getSessionMetadata(agentId, sessionId) {
        const memory = await this.collection.findOne({ agent_id: agentId, session_id: sessionId }, { projection: { agent_id: 1, session_id: 1, context_window: { $slice: -1 } } });
        if (!memory) {
            return null;
        }
        return {
            agentId: memory.agent_id,
            sessionId: memory.session_id,
            messageCount: memory.context_window.length,
            lastMessageAt: memory.context_window[0]?.timestamp,
        };
    }
    async updateSessionMetadata(agentId, sessionId, metadata) {
        await this.collection.updateOne({ agent_id: agentId, session_id: sessionId }, { $set: metadata }, { upsert: true });
    }
}
exports.MongoMemoryProvider = MongoMemoryProvider;
