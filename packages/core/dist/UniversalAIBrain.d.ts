/**
 * @file UniversalAIBrain - Main orchestrator for the Universal AI Brain system
 *
 * This is the central orchestrator that integrates all components of the Universal AI Brain:
 * MongoDB-powered intelligence, semantic memory, context injection, safety systems,
 * self-improvement engines, and real-time monitoring. Provides a unified interface
 * for any TypeScript framework to integrate and gain superpowers.
 *
 * Features:
 * - Framework-agnostic integration layer
 * - MongoDB Atlas Vector Search intelligence
 * - Comprehensive safety and compliance systems
 * - Self-improvement and optimization engines
 * - Real-time monitoring and analytics
 * - Production-ready with enterprise-grade reliability
 */
export interface UniversalAIBrainConfig {
    mongodb: {
        connectionString: string;
        databaseName: string;
        collections: {
            tracing: string;
            memory: string;
            context: string;
            metrics: string;
            audit: string;
        };
    };
    intelligence: {
        embeddingModel: string;
        vectorDimensions: number;
        similarityThreshold: number;
        maxContextLength: number;
    };
    safety: {
        enableContentFiltering: boolean;
        enablePIIDetection: boolean;
        enableHallucinationDetection: boolean;
        enableComplianceLogging: boolean;
        safetyLevel: 'strict' | 'moderate' | 'permissive';
    };
    monitoring: {
        enableRealTimeMonitoring: boolean;
        metricsRetentionDays: number;
        alertingEnabled: boolean;
        dashboardRefreshInterval: number;
    };
    selfImprovement: {
        enableAutomaticOptimization: boolean;
        learningRate: number;
        optimizationInterval: number;
        feedbackLoopEnabled: boolean;
    };
}
export interface AIBrainResponse {
    success: boolean;
    data?: any;
    error?: string;
    metadata: {
        responseTime: number;
        tokensUsed: number;
        cost: number;
        safetyScore: number;
        contextUsed: string[];
        traceId: string;
    };
}
/**
 * UniversalAIBrain - The central orchestrator for AI intelligence
 *
 * Provides a unified interface for any TypeScript framework to integrate
 * with MongoDB-powered AI intelligence, safety systems, and self-improvement.
 */
export declare class UniversalAIBrain {
    private config;
    private mongoClient;
    private database;
    private isInitialized;
    private tracingCollection;
    private memoryCollection;
    private contextCollection;
    private metricsCollection;
    private auditCollection;
    private semanticMemoryEngine;
    private contextInjectionEngine;
    private vectorSearchEngine;
    private safetyEngine;
    private hallucinationDetector;
    private piiDetector;
    private complianceAuditLogger;
    private frameworkSafetyIntegration;
    private failureAnalysisEngine;
    private contextLearningEngine;
    private frameworkOptimizationEngine;
    private selfImprovementMetrics;
    private performanceAnalyticsEngine;
    private realTimeMonitoringDashboard;
    constructor(config: UniversalAIBrainConfig);
    /**
     * Initialize the Universal AI Brain system
     */
    initialize(): Promise<void>;
    /**
     * Process AI request with full intelligence and safety pipeline
     */
    processRequest(framework: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain', input: string, context?: any, sessionId?: string): Promise<AIBrainResponse>;
    /**
     * Get real-time dashboard metrics
     */
    getDashboardMetrics(): Promise<any>;
    /**
     * Shutdown the Universal AI Brain system
     */
    shutdown(): Promise<void>;
    private initializeCollections;
    private initializeIntelligenceLayer;
    private initializeSafetySystems;
    private initializeSelfImprovementEngines;
    private initializeMonitoringSystems;
    private processWithFramework;
    private storeInteraction;
    private calculateSafetyScore;
    private triggerSelfImprovement;
}
//# sourceMappingURL=UniversalAIBrain.d.ts.map