"use strict";
/**
 * @file UniversalAIBrain - The core orchestrator for MongoDB-powered AI intelligence
 *
 * This is the heart of your vision: a universal MongoDB-powered intelligence layer
 * that ANY TypeScript framework can integrate with to get superpowers.
 *
 * Key Features:
 * - MongoDB Atlas Vector Search for semantic memory
 * - Intelligent context injection and prompt enhancement
 * - Framework-agnostic adapter system
 * - Persistent conversation memory
 * - Knowledge graph capabilities
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalAIBrain = void 0;
const mongodb_1 = require("mongodb");
const MongoConnection_1 = require("../persistance/MongoConnection");
const MongoVectorStore_1 = require("../vector/MongoVectorStore");
const OpenAIEmbeddingProvider_1 = require("../embeddings/OpenAIEmbeddingProvider");
const index_1 = require("../collections/index");
/**
 * UniversalAIBrain - The core MongoDB-powered intelligence orchestrator
 *
 * This class provides the central intelligence layer that any framework can integrate with.
 * It handles semantic memory, context injection, and intelligent prompt enhancement.
 */
class UniversalAIBrain {
    constructor(config) {
        this.isInitialized = false;
        this.config = config;
        this.mongoConnection = MongoConnection_1.MongoConnection.getInstance({
            uri: config.mongoConfig.uri,
            dbName: config.mongoConfig.dbName
        });
        this.agentId = new mongodb_1.ObjectId(); // Generate unique agent ID
    }
    /**
     * Initialize the Universal AI Brain
     * Sets up MongoDB connections, collections, and vector search
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            // Connect to MongoDB Atlas
            await this.mongoConnection.connect();
            this.db = this.mongoConnection.getDb();
            // Initialize Collection Manager - The data layer that powers everything! 💾
            this.collections = new index_1.CollectionManager(this.db);
            await this.collections.initialize();
            // Initialize legacy collections (for backward compatibility)
            this.conversationsCollection = this.db.collection('conversations');
            this.interactionsCollection = this.db.collection('interactions');
            // Initialize embedding provider
            this.embeddingProvider = new OpenAIEmbeddingProvider_1.OpenAIEmbeddingProvider({
                apiKey: this.config.embeddingConfig.apiKey,
                model: this.config.embeddingConfig.model
            });
            // Initialize vector store
            this.vectorStore = new MongoVectorStore_1.MongoVectorStore(this.mongoConnection, this.config.vectorSearchConfig.collectionName, this.config.vectorSearchConfig.indexName);
            await this.vectorStore.initialize(this.embeddingProvider);
            // Ensure indexes exist
            await this.ensureIndexes();
            this.isInitialized = true;
            console.log('🧠 Universal AI Brain initialized successfully!');
        }
        catch (error) {
            console.error('❌ Failed to initialize Universal AI Brain:', error);
            throw error;
        }
    }
    /**
     * Enhance a prompt with relevant context from MongoDB
     * This is the core intelligence feature that frameworks will use
     */
    async enhancePrompt(prompt, options) {
        await this.ensureInitialized();
        const { frameworkType = 'unknown', conversationId, maxContextItems = 5, enhancementStrategy = 'hybrid' } = options || {};
        try {
            // Generate embedding for the prompt
            const promptEmbedding = await this.generateEmbedding(prompt);
            // Retrieve relevant context based on strategy
            let relevantContext = [];
            switch (enhancementStrategy) {
                case 'semantic':
                    relevantContext = await this.getSemanticContext(promptEmbedding, maxContextItems);
                    break;
                case 'hybrid':
                    relevantContext = await this.getHybridContext(prompt, promptEmbedding, maxContextItems);
                    break;
                case 'conversational':
                    relevantContext = await this.getConversationalContext(prompt, promptEmbedding, conversationId, maxContextItems);
                    break;
            }
            // Build enhanced prompt
            const enhancedPrompt = this.buildEnhancedPrompt(prompt, relevantContext, frameworkType);
            return {
                originalPrompt: prompt,
                enhancedPrompt,
                injectedContext: relevantContext,
                metadata: {
                    frameworkType,
                    enhancementStrategy,
                    contextSources: relevantContext.map(c => c.source)
                }
            };
        }
        catch (error) {
            console.error('Error enhancing prompt:', error);
            // Return original prompt if enhancement fails
            return {
                originalPrompt: prompt,
                enhancedPrompt: prompt,
                injectedContext: [],
                metadata: {
                    frameworkType,
                    enhancementStrategy: 'fallback',
                    contextSources: []
                }
            };
        }
    }
    /**
     * Store an interaction for future context and learning
     */
    async storeInteraction(interaction) {
        await this.ensureInitialized();
        const fullInteraction = {
            ...interaction,
            id: new mongodb_1.ObjectId().toString(),
            timestamp: new Date()
        };
        try {
            // Store the interaction
            await this.interactionsCollection.insertOne(fullInteraction);
            // Generate and store embeddings for future retrieval
            await this.storeInteractionEmbeddings(fullInteraction);
            return fullInteraction.id;
        }
        catch (error) {
            console.error('Error storing interaction:', error);
            throw error;
        }
    }
    /**
     * Retrieve relevant context based on a query
     */
    async retrieveRelevantContext(query, options) {
        await this.ensureInitialized();
        try {
            const queryEmbedding = await this.generateEmbedding(query);
            const searchOptions = {
                k: options?.limit || 10,
                filter: this.buildSearchFilter(options),
                minScore: options?.minRelevanceScore || this.config.vectorSearchConfig.minScore
            };
            const results = await this.vectorStore.vectorSearch(queryEmbedding, searchOptions);
            return results.map((result) => this.convertToContext(result));
        }
        catch (error) {
            console.error('Error retrieving context:', error);
            return [];
        }
    }
    /**
     * Alias for retrieveRelevantContext for backward compatibility
     */
    async getRelevantContext(query, options) {
        return this.retrieveRelevantContext(query, options);
    }
    /**
     * Integrate with a framework using an adapter
     */
    async integrateWithFramework(adapter) {
        await this.ensureInitialized();
        console.log(`🔌 Integrating Universal AI Brain with ${adapter.frameworkName}`);
        return adapter.integrate(this);
    }
    /**
     * Get the unique agent ID for this brain instance
     */
    getAgentId() {
        return this.agentId;
    }
    /**
     * Get brain statistics and health information
     */
    async getStats() {
        await this.ensureInitialized();
        try {
            const [vectorStoreStats, interactionCount, conversationCount] = await Promise.all([
                this.vectorStore.getStats(),
                this.interactionsCollection.countDocuments(),
                this.conversationsCollection.countDocuments()
            ]);
            return {
                isHealthy: await this.mongoConnection.healthCheck(),
                collections: {
                    vectorStore: vectorStoreStats,
                    interactions: interactionCount,
                    conversations: conversationCount
                },
                embeddingProvider: {
                    model: this.embeddingProvider.getModel(),
                    dimensions: this.embeddingProvider.getDimensions()
                },
                lastUpdated: new Date()
            };
        }
        catch (error) {
            console.error('Error getting brain stats:', error);
            return { isHealthy: false, error: error.message };
        }
    }
    /**
     * Cleanup method for graceful shutdown
     */
    async cleanup() {
        if (!this.isInitialized) {
            return;
        }
        try {
            await this.mongoConnection.disconnect();
            this.isInitialized = false;
            console.log('🧠 Universal AI Brain cleanup completed');
        }
        catch (error) {
            console.error('Error during cleanup:', error);
            throw error;
        }
    }
    // Private helper methods will be added in the next part...
    async ensureInitialized() {
        if (!this.isInitialized) {
            await this.initialize();
        }
    }
    async ensureIndexes() {
        try {
            // Create indexes for conversations
            await this.conversationsCollection.createIndex({ id: 1 }, { unique: true });
            await this.conversationsCollection.createIndex({ framework: 1 });
            await this.conversationsCollection.createIndex({ createdAt: -1 });
            // Create indexes for interactions
            await this.interactionsCollection.createIndex({ conversationId: 1 });
            await this.interactionsCollection.createIndex({ framework: 1 });
            await this.interactionsCollection.createIndex({ timestamp: -1 });
            // Create text search index for hybrid search on vector store collection
            const vectorCollection = this.db.collection('vector_documents');
            await vectorCollection.createIndex({
                "text": "text",
                "metadata.content": "text"
            }, { name: "text_index" });
            console.log('✅ MongoDB indexes created successfully');
        }
        catch (error) {
            console.warn('⚠️ Some indexes may already exist:', error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async generateEmbedding(text) {
        try {
            return await this.embeddingProvider.generateEmbedding(text);
        }
        catch (error) {
            console.error('Error generating embedding:', error);
            throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getSemanticContext(embedding, limit) {
        try {
            const results = await this.vectorStore.vectorSearch(embedding, {
                limit,
                minScore: this.config.vectorSearchConfig.minScore,
                includeEmbeddings: false
            });
            return results.map(result => this.convertToContext(result));
        }
        catch (error) {
            console.error('Error in semantic context retrieval:', error);
            return [];
        }
    }
    async getHybridContext(text, embedding, limit) {
        try {
            const results = await this.vectorStore.hybridSearch(text, {
                limit,
                minScore: this.config.vectorSearchConfig.minScore,
                includeEmbeddings: false
            });
            return results.map(result => this.convertToContext(result));
        }
        catch (error) {
            console.error('Error in hybrid context retrieval:', error);
            // Fallback to semantic search
            return this.getSemanticContext(embedding, limit);
        }
    }
    async getConversationalContext(text, embedding, conversationId, limit = 5) {
        try {
            // First get recent conversation history if conversationId provided
            let conversationContext = [];
            if (conversationId) {
                const recentInteractions = await this.interactionsCollection
                    .find({ conversationId })
                    .sort({ timestamp: -1 })
                    .limit(3)
                    .toArray();
                conversationContext = recentInteractions.map(interaction => ({
                    id: interaction.id,
                    content: `User: ${interaction.userMessage}\nAssistant: ${interaction.assistantResponse}`,
                    metadata: { type: 'conversation_history', ...interaction.metadata },
                    relevanceScore: 0.9, // High relevance for conversation history
                    source: 'conversation_memory',
                    timestamp: interaction.timestamp
                }));
            }
            // Then get semantic context
            const semanticContext = await this.getHybridContext(text, embedding, limit - conversationContext.length);
            // Combine and return
            return [...conversationContext, ...semanticContext].slice(0, limit);
        }
        catch (error) {
            console.error('Error in conversational context retrieval:', error);
            return this.getSemanticContext(embedding, limit);
        }
    }
    buildEnhancedPrompt(prompt, context, frameworkType) {
        // Framework-specific prompt enhancement
        const frameworkInstructions = this.getFrameworkInstructions(frameworkType);
        // If no context, still provide framework instructions
        if (context.length === 0) {
            return `${frameworkInstructions}

USER QUERY:
${prompt}

Please provide a helpful response.`;
        }
        // Build context section
        const contextSection = context
            .map((ctx, index) => {
            return `Context ${index + 1} (${ctx.source}, relevance: ${ctx.relevanceScore.toFixed(2)}):\n${ctx.content}`;
        })
            .join('\n\n');
        return `${frameworkInstructions}

RELEVANT CONTEXT:
${contextSection}

USER QUERY:
${prompt}

Please provide a helpful response based on the context above. If the context doesn't contain relevant information, say so and provide the best answer you can.`;
    }
    getFrameworkInstructions(frameworkType) {
        const instructions = {
            'mastra': 'You are an AI assistant integrated with Mastra framework. Use the provided context to enhance your responses with relevant information from the knowledge base.',
            'vercel-ai': 'You are an AI assistant enhanced with Vercel AI SDK. Leverage the context to provide accurate, contextual responses.',
            'langchain': 'You are an AI assistant powered by LangChain.js. Use the retrieved context to provide comprehensive, well-informed responses.',
            'openai-agents': 'You are an OpenAI agent with access to a knowledge base. Use the provided context to give accurate, helpful responses.',
            'unknown': 'You are an AI assistant with access to a knowledge base. Use the provided context to enhance your responses.'
        };
        return instructions[frameworkType] || instructions['unknown'];
    }
    async storeInteractionEmbeddings(interaction) {
        try {
            // Store user message and assistant response as separate documents
            await Promise.all([
                this.vectorStore.storeDocument(interaction.userMessage, {
                    type: 'user_message',
                    conversationId: interaction.conversationId,
                    framework: interaction.framework,
                    ...interaction.metadata
                }, 'conversation_memory'),
                this.vectorStore.storeDocument(interaction.assistantResponse, {
                    type: 'assistant_response',
                    conversationId: interaction.conversationId,
                    framework: interaction.framework,
                    ...interaction.metadata
                }, 'conversation_memory')
            ]);
        }
        catch (error) {
            console.error('Error storing interaction embeddings:', error);
            // Don't throw - this is not critical for the main flow
        }
    }
    buildSearchFilter(options) {
        const filter = {};
        if (options?.conversationId) {
            filter['document.conversationId'] = options.conversationId;
        }
        if (options?.framework) {
            filter['document.framework'] = options.framework;
        }
        if (options?.timeRange) {
            filter['document.timestamp'] = {
                $gte: options.timeRange.start,
                $lte: options.timeRange.end
            };
        }
        return filter;
    }
    convertToContext(result) {
        return {
            id: result._id?.toString() || new mongodb_1.ObjectId().toString(),
            content: result.text || JSON.stringify(result),
            metadata: result.metadata || {},
            relevanceScore: result.score,
            source: result.source || 'knowledge_base',
            timestamp: result.timestamp || new Date()
        };
    }
}
exports.UniversalAIBrain = UniversalAIBrain;
