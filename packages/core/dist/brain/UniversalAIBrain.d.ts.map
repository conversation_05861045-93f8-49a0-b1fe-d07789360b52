{"version": 3, "file": "UniversalAIBrain.d.ts", "sourceRoot": "", "sources": ["../../src/brain/UniversalAIBrain.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,EAAkB,QAAQ,EAAE,MAAM,SAAS,CAAC;AAEnD,OAAO,EAAE,gBAAgB,EAAyC,MAAM,4BAA4B,CAAC;AAErG,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AAGzD,MAAM,WAAW,WAAW;IAC1B,WAAW,EAAE;QACX,GAAG,EAAE,MAAM,CAAC;QACZ,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,eAAe,EAAE;QACf,QAAQ,EAAE,QAAQ,GAAG,QAAQ,GAAG,aAAa,CAAC;QAC9C,KAAK,EAAE,MAAM,CAAC;QACd,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,kBAAkB,EAAE;QAClB,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,EAAE,MAAM,CAAC;QACvB,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;CACH;AAED,MAAM,WAAW,OAAO;IACtB,EAAE,EAAE,MAAM,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9B,cAAc,EAAE,MAAM,CAAC;IACvB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;CACjB;AAED,MAAM,WAAW,cAAc;IAC7B,cAAc,EAAE,MAAM,CAAC;IACvB,cAAc,EAAE,MAAM,CAAC;IACvB,eAAe,EAAE,OAAO,EAAE,CAAC;IAC3B,QAAQ,EAAE;QACR,aAAa,EAAE,MAAM,CAAC;QACtB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,cAAc,EAAE,MAAM,EAAE,CAAC;KAC1B,CAAC;CACH;AAED,MAAM,WAAW,WAAW;IAC1B,EAAE,EAAE,MAAM,CAAC;IACX,cAAc,EAAE,MAAM,CAAC;IACvB,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,OAAO,EAAE,OAAO,EAAE,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9B,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB,CAAC,CAAC;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,CAAC,KAAK,EAAE,gBAAgB,GAAG,CAAC,CAAC;IACtC,gBAAgB,CAAC,gBAAgB,EAAE,GAAG,EAAE,KAAK,EAAE,gBAAgB,GAAG,GAAG,CAAC;CACvE;AAED,MAAM,WAAW,mBAAmB;IAClC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,SAAS,CAAC,EAAE;QACV,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B;AAED;;;;;GAKG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,EAAE,CAAK;IACf,OAAO,CAAC,eAAe,CAAkB;IAClC,WAAW,EAAE,gBAAgB,CAAC;IACrC,OAAO,CAAC,iBAAiB,CAAoB;IACtC,WAAW,EAAE,iBAAiB,CAAC;IACtC,OAAO,CAAC,uBAAuB,CAAa;IAC5C,OAAO,CAAC,sBAAsB,CAAa;IAC3C,OAAO,CAAC,MAAM,CAAc;IAC5B,OAAO,CAAC,aAAa,CAAkB;IACvC,OAAO,CAAC,OAAO,CAAW;gBAEd,MAAM,EAAE,WAAW;IAS/B;;;OAGG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IA4CjC;;;OAGG;IACG,aAAa,CACjB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE;QACR,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,mBAAmB,CAAC,EAAE,UAAU,GAAG,QAAQ,GAAG,gBAAgB,CAAC;KAChE,GACA,OAAO,CAAC,cAAc,CAAC;IA+D1B;;OAEG;IACG,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,WAAW,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAuB3F;;OAEG;IACG,uBAAuB,CAC3B,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,mBAAmB,GAC5B,OAAO,CAAC,OAAO,EAAE,CAAC;IAwBrB;;OAEG;IACG,sBAAsB,CAAC,CAAC,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;IAOzE;;OAEG;IACH,UAAU,IAAI,QAAQ;IAItB;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC;YAkChB,iBAAiB;YAMjB,aAAa;YA4Bb,iBAAiB;YASjB,kBAAkB;YAelB,gBAAgB;YAgBhB,wBAAwB;IAsCtC,OAAO,CAAC,mBAAmB;IAgC3B,OAAO,CAAC,wBAAwB;YAYlB,0BAA0B;IA+BxC,OAAO,CAAC,iBAAiB;IAqBzB,OAAO,CAAC,gBAAgB;CAUzB"}