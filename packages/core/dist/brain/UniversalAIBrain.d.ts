/**
 * @file UniversalAIBrain - The core orchestrator for MongoDB-powered AI intelligence
 *
 * This is the heart of your vision: a universal MongoDB-powered intelligence layer
 * that ANY TypeScript framework can integrate with to get superpowers.
 *
 * Key Features:
 * - MongoDB Atlas Vector Search for semantic memory
 * - Intelligent context injection and prompt enhancement
 * - Framework-agnostic adapter system
 * - Persistent conversation memory
 * - Knowledge graph capabilities
 */
import { ObjectId } from 'mongodb';
import { MongoVectorStore } from '../vector/MongoVectorStore';
import { CollectionManager } from '../collections/index';
export interface BrainConfig {
    mongoConfig: {
        uri: string;
        dbName: string;
    };
    embeddingConfig: {
        provider: 'openai' | 'cohere' | 'huggingface';
        model: string;
        apiKey: string;
        dimensions: number;
    };
    vectorSearchConfig: {
        indexName: string;
        collectionName: string;
        minScore: number;
    };
}
export interface Context {
    id: string;
    content: string;
    metadata: Record<string, any>;
    relevanceScore: number;
    source: string;
    timestamp: Date;
}
export interface EnhancedPrompt {
    originalPrompt: string;
    enhancedPrompt: string;
    injectedContext: Context[];
    metadata: {
        frameworkType: string;
        enhancementStrategy: string;
        contextSources: string[];
    };
}
export interface Interaction {
    id: string;
    conversationId: string;
    userMessage: string;
    assistantResponse: string;
    context: Context[];
    metadata: Record<string, any>;
    timestamp: Date;
    framework: string;
}
export interface FrameworkAdapter<T> {
    frameworkName: string;
    integrate(brain: UniversalAIBrain): T;
    enhanceWithBrain(originalFunction: any, brain: UniversalAIBrain): any;
}
export interface MemorySearchOptions {
    limit?: number;
    timeRange?: {
        start: Date;
        end: Date;
    };
    conversationId?: string;
    framework?: string;
    minRelevanceScore?: number;
}
/**
 * UniversalAIBrain - The core MongoDB-powered intelligence orchestrator
 *
 * This class provides the central intelligence layer that any framework can integrate with.
 * It handles semantic memory, context injection, and intelligent prompt enhancement.
 */
export declare class UniversalAIBrain {
    private db;
    private mongoConnection;
    vectorStore: MongoVectorStore;
    private embeddingProvider;
    collections: CollectionManager;
    private conversationsCollection;
    private interactionsCollection;
    private config;
    private isInitialized;
    private agentId;
    constructor(config: BrainConfig);
    /**
     * Initialize the Universal AI Brain
     * Sets up MongoDB connections, collections, and vector search
     */
    initialize(): Promise<void>;
    /**
     * Enhance a prompt with relevant context from MongoDB
     * This is the core intelligence feature that frameworks will use
     */
    enhancePrompt(prompt: string, options?: {
        frameworkType?: string;
        conversationId?: string;
        maxContextItems?: number;
        enhancementStrategy?: 'semantic' | 'hybrid' | 'conversational';
    }): Promise<EnhancedPrompt>;
    /**
     * Store an interaction for future context and learning
     */
    storeInteraction(interaction: Omit<Interaction, 'id' | 'timestamp'>): Promise<string>;
    /**
     * Retrieve relevant context based on a query
     */
    retrieveRelevantContext(query: string, options?: MemorySearchOptions): Promise<Context[]>;
    /**
     * Integrate with a framework using an adapter
     */
    integrateWithFramework<T>(adapter: FrameworkAdapter<T>): Promise<T>;
    /**
     * Get the unique agent ID for this brain instance
     */
    getAgentId(): ObjectId;
    /**
     * Get brain statistics and health information
     */
    getStats(): Promise<any>;
    private ensureInitialized;
    private ensureIndexes;
    private generateEmbedding;
    private getSemanticContext;
    private getHybridContext;
    private getConversationalContext;
    private buildEnhancedPrompt;
    private getFrameworkInstructions;
    private storeInteractionEmbeddings;
    private buildSearchFilter;
    private convertToContext;
}
//# sourceMappingURL=UniversalAIBrain.d.ts.map