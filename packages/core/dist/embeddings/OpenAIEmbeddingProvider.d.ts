/**
 * @file OpenAIEmbeddingProvider - Production-ready OpenAI embedding implementation
 *
 * This provides OpenAI embedding generation for the Universal AI Brain.
 * Supports both OpenAI and Azure OpenAI endpoints with proper error handling,
 * rate limiting, and batch processing.
 */
import { EmbeddingProvider } from '../vector/MongoVectorStore';
export interface OpenAIConfig {
    apiKey: string;
    model: string;
    baseUrl?: string;
    maxRetries?: number;
    timeout?: number;
    batchSize?: number;
}
export interface EmbeddingResponse {
    object: string;
    data: Array<{
        object: string;
        embedding: number[];
        index: number;
    }>;
    model: string;
    usage: {
        prompt_tokens: number;
        total_tokens: number;
    };
}
/**
 * OpenAIEmbeddingProvider - Production-ready OpenAI embedding implementation
 *
 * Features:
 * - Support for OpenAI and Azure OpenAI
 * - Automatic retry with exponential backoff
 * - Batch processing for efficiency
 * - Rate limiting and error handling
 * - Token counting and cost tracking
 */
export declare class OpenAIEmbeddingProvider implements EmbeddingProvider {
    private config;
    private requestCount;
    private totalTokens;
    constructor(config: OpenAIConfig);
    /**
     * Generate embedding for a single text
     */
    generateEmbedding(text: string): Promise<number[]>;
    /**
     * Generate embeddings for multiple texts (batch processing)
     */
    generateEmbeddings(texts: string[]): Promise<number[][]>;
    /**
     * Get embedding dimensions for the current model
     */
    getDimensions(): number;
    /**
     * Get the current model name
     */
    getModel(): string;
    /**
     * Get usage statistics
     */
    getUsageStats(): {
        requestCount: number;
        totalTokens: number;
        estimatedCost: number;
    };
    /**
     * Reset usage statistics
     */
    resetUsageStats(): void;
    private processBatches;
    private callEmbeddingAPI;
    private makeRequest;
    private sleep;
    private validateConfig;
    private getCostPerToken;
    /**
     * Test the embedding provider with a simple query
     */
    test(): Promise<{
        success: boolean;
        details: any;
    }>;
    /**
     * Create a provider instance from environment variables
     */
    static fromEnv(): OpenAIEmbeddingProvider;
    /**
     * Create an Azure OpenAI provider instance
     */
    static forAzure(config: {
        apiKey: string;
        endpoint: string;
        deploymentName: string;
        apiVersion?: string;
    }): OpenAIEmbeddingProvider;
}
//# sourceMappingURL=OpenAIEmbeddingProvider.d.ts.map