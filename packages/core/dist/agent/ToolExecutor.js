"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolExecutor = void 0;
const utils_1 = require("@mongodb-ai/utils");
class ToolExecutor {
    constructor(toolStore, executionStore) {
        this.toolStore = toolStore;
        this.executionStore = executionStore;
    }
    async execute(toolId, input, context) {
        const tool = await this.toolStore.findOne({ tool_id: toolId });
        if (!tool) {
            throw new Error(`Tool with id ${toolId} not found`);
        }
        // In a real implementation, we would execute the tool here.
        // For now, we'll just return a mock output.
        const output = { success: true, result: `Executed ${tool.name}` };
        // Log the execution
        const executionLog = {
            tool_id: toolId,
            agent_id: context.agent_id,
            workflow_id: context.workflow_id,
            input,
            output,
            timestamp: new Date(),
        };
        await this.executionStore.create(executionLog);
        utils_1.logger.info('Tool executed', {
            tool_id: toolId,
            agent_id: context.agent_id,
            workflow_id: context.workflow_id,
        });
        return output;
    }
    async createTool(tool) {
        return this.toolStore.create(tool);
    }
}
exports.ToolExecutor = ToolExecutor;
