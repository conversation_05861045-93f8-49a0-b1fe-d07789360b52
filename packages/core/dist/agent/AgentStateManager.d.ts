import { IDataStore } from '../persistance/IDataStore';
import { Document } from 'mongodb';
export interface AgentState extends Document {
    agent_id: string;
    name: string;
    version: string;
    status: 'active' | 'inactive' | 'deprecated';
    model_config: {
        provider: string;
        model: string;
        [key: string]: any;
    };
    tools?: string[];
}
export interface AgentConfig extends Document {
}
export declare class AgentStateManager {
    private agentStore;
    private configStore;
    constructor(agentStore: IDataStore<AgentState>, configStore: IDataStore<AgentConfig>);
    getAgentState(agentId: string): Promise<AgentState | null>;
    saveAgentState(state: AgentState): Promise<AgentState>;
}
//# sourceMappingURL=AgentStateManager.d.ts.map