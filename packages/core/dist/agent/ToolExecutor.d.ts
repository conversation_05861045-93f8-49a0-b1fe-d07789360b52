import { IDataStore } from '../persistance/IDataStore';
import { Document } from 'mongodb';
export interface ToolDefinition extends Document {
    tool_id: string;
    name: string;
    description: string;
    input_schema: any;
    output_schema: any;
    execute: (input: any) => Promise<any>;
}
export interface ToolExecutionLog extends Document {
}
export interface ToolExecutionContext {
    agent_id: string;
    workflow_id: string;
    timeout_ms: number;
}
export declare class ToolExecutor {
    private toolStore;
    private executionStore;
    constructor(toolStore: IDataStore<ToolDefinition>, executionStore: IDataStore<ToolExecutionLog>);
    execute(toolId: string, input: any, context: ToolExecutionContext): Promise<any>;
    createTool(tool: ToolDefinition): Promise<ToolDefinition>;
}
//# sourceMappingURL=ToolExecutor.d.ts.map