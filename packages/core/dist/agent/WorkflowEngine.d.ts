import { ObjectId } from 'mongodb';
import { IDataStore } from '../persistance/IDataStore';
import { AgentStateManager } from './AgentStateManager';
import { ToolExecutor } from './ToolExecutor';
export interface WorkflowStep {
    step_id: string;
    agent_id: string;
    description: string;
    depends_on?: string[];
    timeout_seconds?: number;
    retry_count?: number;
    tool_id?: string;
    input_mapping?: Record<string, any>;
    condition?: string;
}
export interface WorkflowStepExecution {
    step_id: string;
    agent_id: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
    started_at?: Date;
    completed_at?: Date;
    duration_seconds?: number;
    input?: Record<string, any>;
    output?: Record<string, any>;
    cost?: number;
    tokens_used?: number;
    error?: {
        message: string;
        stack?: string;
        retry_count: number;
    };
}
export interface Workflow {
    _id?: ObjectId;
    workflow_id: string;
    workflow_name: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
    created_at: Date;
    updated_at: Date;
    workflow_definition: {
        name: string;
        version: string;
        steps: WorkflowStep[];
    };
    current_step?: number;
    execution_log: WorkflowStepExecution[];
    shared_context: Record<string, any>;
    error_log: Array<{
        step_id: string;
        error: string;
        timestamp: Date;
    }>;
    retry_attempts?: number;
    max_retries?: number;
}
export interface WorkflowExecutionOptions {
    timeout_seconds?: number;
    max_retries?: number;
    parallel_execution?: boolean;
    continue_on_error?: boolean;
}
export declare class WorkflowEngine {
    private workflowStore;
    private agentStateManager;
    private toolExecutor;
    constructor(workflowStore: IDataStore<Workflow>, agentStateManager: AgentStateManager, toolExecutor: ToolExecutor);
    /**
     * Create a new workflow
     */
    createWorkflow(workflowName: string, steps: WorkflowStep[], initialContext?: Record<string, any>, options?: WorkflowExecutionOptions): Promise<Workflow>;
    /**
     * Execute a workflow
     */
    executeWorkflow(workflowId: string, options?: WorkflowExecutionOptions): Promise<void>;
    /**
     * Execute workflow steps sequentially
     */
    private executeWorkflowSequential;
    /**
     * Execute a single workflow step
     */
    private executeStep;
    /**
     * Prepare input for a step based on input mapping
     */
    private prepareStepInput;
    /**
     * Check if step dependencies are met
     */
    private areDependenciesMet;
    /**
     * Evaluate a simple condition
     */
    private evaluateCondition;
    /**
     * Log step execution
     */
    private logStepExecution;
    /**
     * Log workflow error
     */
    private logError;
    /**
     * Update workflow status
     */
    private updateWorkflowStatus;
    /**
     * Update current step
     */
    private updateCurrentStep;
    /**
     * Update workflow context
     */
    private updateWorkflowContext;
    /**
     * Retry a failed workflow
     */
    retryWorkflow(workflowId: string): Promise<void>;
    /**
     * Cancel a workflow
     */
    cancelWorkflow(workflowId: string): Promise<void>;
    /**
     * Get workflow status
     */
    getWorkflowStatus(workflowId: string): Promise<Workflow | null>;
    /**
     * Get workflow execution summary
     */
    getWorkflowSummary(workflowId: string): Promise<{
        workflow: Workflow;
        totalSteps: number;
        completedSteps: number;
        failedSteps: number;
        totalCost: number;
        totalDuration: number;
    } | null>;
}
//# sourceMappingURL=WorkflowEngine.d.ts.map