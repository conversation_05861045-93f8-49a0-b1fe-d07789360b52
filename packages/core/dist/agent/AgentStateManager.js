"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentStateManager = void 0;
class AgentStateManager {
    constructor(agentStore, configStore) {
        this.agentStore = agentStore;
        this.configStore = configStore;
    }
    async getAgentState(agentId) {
        return this.agentStore.findOne({ agent_id: agentId });
    }
    async saveAgentState(state) {
        return this.agentStore.create(state);
    }
}
exports.AgentStateManager = AgentStateManager;
