{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Secure Credentials", "description": "Schema for the 'secure_credentials' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "credential_id": {"type": "string"}, "resource_id": {"type": "string"}, "credential_type": {"type": "string"}, "encrypted_credentials": {"type": "object"}, "last_rotated": {"type": "string", "format": "date-time"}, "status": {"type": "string", "enum": ["active", "revoked"]}}, "required": ["credential_id", "resource_id", "credential_type", "encrypted_credentials", "status"]}