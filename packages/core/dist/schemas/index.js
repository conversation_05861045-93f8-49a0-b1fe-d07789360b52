"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.schemas = void 0;
const agent_schema_json_1 = __importDefault(require("./agent.schema.json"));
const agent_configurations_schema_json_1 = __importDefault(require("./agent_configurations.schema.json"));
const agent_memory_schema_json_1 = __importDefault(require("./agent_memory.schema.json"));
const agent_working_memory_schema_json_1 = __importDefault(require("./agent_working_memory.schema.json"));
const agent_tools_schema_json_1 = __importDefault(require("./agent_tools.schema.json"));
const agent_performance_metrics_schema_json_1 = __importDefault(require("./agent_performance_metrics.schema.json"));
const agent_workflows_schema_json_1 = __importDefault(require("./agent_workflows.schema.json"));
const vector_embeddings_schema_json_1 = __importDefault(require("./vector_embeddings.schema.json"));
const tool_executions_schema_json_1 = __importDefault(require("./tool_executions.schema.json"));
const traces_schema_json_1 = __importDefault(require("./traces.schema.json"));
const dynamic_plans_schema_json_1 = __importDefault(require("./dynamic_plans.schema.json"));
const evaluations_schema_json_1 = __importDefault(require("./evaluations.schema.json"));
const human_feedback_schema_json_1 = __importDefault(require("./human_feedback.schema.json"));
const agent_permissions_schema_json_1 = __importDefault(require("./agent_permissions.schema.json"));
const resource_registry_schema_json_1 = __importDefault(require("./resource_registry.schema.json"));
const secure_credentials_schema_json_1 = __importDefault(require("./secure_credentials.schema.json"));
const ingestion_pipelines_schema_json_1 = __importDefault(require("./ingestion_pipelines.schema.json"));
const agent_events_schema_json_1 = __importDefault(require("./agent_events.schema.json"));
exports.schemas = {
    agent: agent_schema_json_1.default,
    agentConfigurations: agent_configurations_schema_json_1.default,
    agentMemory: agent_memory_schema_json_1.default,
    agentWorkingMemory: agent_working_memory_schema_json_1.default,
    agentTools: agent_tools_schema_json_1.default,
    agentPerformanceMetrics: agent_performance_metrics_schema_json_1.default,
    agentWorkflows: agent_workflows_schema_json_1.default,
    vectorEmbeddings: vector_embeddings_schema_json_1.default,
    toolExecutions: tool_executions_schema_json_1.default,
    traces: traces_schema_json_1.default,
    dynamicPlans: dynamic_plans_schema_json_1.default,
    evaluations: evaluations_schema_json_1.default,
    humanFeedback: human_feedback_schema_json_1.default,
    agentPermissions: agent_permissions_schema_json_1.default,
    resourceRegistry: resource_registry_schema_json_1.default,
    secureCredentials: secure_credentials_schema_json_1.default,
    ingestionPipelines: ingestion_pipelines_schema_json_1.default,
    agentEvents: agent_events_schema_json_1.default,
};
