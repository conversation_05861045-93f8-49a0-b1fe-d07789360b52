{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent Configurations", "description": "Schema for the 'agent_configurations' collection", "type": "object", "properties": {"_id": {"type": "string", "pattern": "^[0-9a-fA-F]{24}$"}, "config_id": {"type": "string"}, "agent_id": {"type": "string"}, "version": {"type": "string"}, "is_active": {"type": "boolean"}, "created_at": {"type": "string", "format": "date-time"}, "prompts": {"type": "object"}, "parameters": {"type": "object"}, "quality_gates": {"type": "object"}}, "required": ["config_id", "agent_id", "version", "is_active", "created_at"]}