"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchemaValidator = void 0;
const ajv_1 = __importDefault(require("ajv"));
const ajv_formats_1 = __importDefault(require("ajv-formats"));
const index_1 = require("./index");
const ajv = new ajv_1.default({ allErrors: true });
(0, ajv_formats_1.default)(ajv);
class SchemaValidator {
    static validateOrThrow(schemaName, data) {
        const schema = index_1.schemas[schemaName];
        if (!schema) {
            throw new Error(`Schema not found: ${schemaName}`);
        }
        const validate = ajv.compile(schema);
        const valid = validate(data);
        if (!valid) {
            const errorMessages = validate.errors?.map(error => `${error.instancePath} ${error.message}`).join(', ');
            throw new Error(`Schema validation failed for ${schemaName}: ${errorMessages}`);
        }
    }
}
exports.SchemaValidator = SchemaValidator;
