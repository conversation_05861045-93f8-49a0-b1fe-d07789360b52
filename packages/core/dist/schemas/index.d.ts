export declare const schemas: {
    agent: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            agent_id: {
                type: string;
            };
            name: {
                type: string;
            };
            description: {
                type: string;
            };
            version: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            updated_at: {
                type: string;
                format: string;
            };
            capabilities: {
                type: string;
                items: {
                    type: string;
                };
            };
            tools: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        tool_id: {
                            type: string;
                        };
                        name: {
                            type: string;
                        };
                        config: {
                            type: string;
                        };
                        rate_limits: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
            model_config: {
                type: string;
                properties: {
                    provider: {
                        type: string;
                    };
                    model: {
                        type: string;
                    };
                    temperature: {
                        type: string;
                    };
                    max_tokens: {
                        type: string;
                    };
                    system_prompt: {
                        type: string;
                    };
                };
                required: string[];
            };
            performance_targets: {
                type: string;
                properties: {
                    max_response_time_seconds: {
                        type: string;
                    };
                    min_confidence_score: {
                        type: string;
                    };
                    max_cost_per_execution: {
                        type: string;
                    };
                };
            };
        };
        required: string[];
    };
    agentConfigurations: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            config_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            version: {
                type: string;
            };
            is_active: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            prompts: {
                type: string;
            };
            parameters: {
                type: string;
            };
            quality_gates: {
                type: string;
            };
        };
        required: string[];
    };
    agentMemory: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            memory_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            memory_type: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            last_accessed: {
                type: string;
                format: string;
            };
            access_count: {
                type: string;
            };
            content: {
                type: string;
            };
            embedding: {
                type: string;
                items: {
                    type: string;
                };
            };
            embedding_model: {
                type: string;
            };
            metadata: {
                type: string;
            };
            usage_stats: {
                type: string;
            };
        };
        required: string[];
    };
    agentWorkingMemory: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            session_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            expires_at: {
                type: string;
                format: string;
            };
            context_window: {
                type: string;
                items: {
                    type: string;
                };
            };
            working_state: {
                type: string;
            };
            temp_findings: {
                type: string;
            };
        };
        required: string[];
    };
    agentTools: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            tool_id: {
                type: string;
            };
            name: {
                type: string;
            };
            description: {
                type: string;
            };
            version: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            config: {
                type: string;
            };
            input_schema: {
                type: string;
            };
            output_schema: {
                type: string;
            };
            rate_limits: {
                type: string;
            };
            cost_model: {
                type: string;
            };
            performance_stats: {
                type: string;
            };
        };
        required: string[];
    };
    agentPerformanceMetrics: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            metric_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            time_window: {
                type: string;
            };
            metrics: {
                type: string;
            };
            quality: {
                type: string;
            };
            resources: {
                type: string;
            };
            errors: {
                type: string;
            };
        };
        required: string[];
    };
    agentWorkflows: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            workflow_id: {
                type: string;
            };
            workflow_name: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            created_at: {
                type: string;
                format: string;
            };
            updated_at: {
                type: string;
                format: string;
            };
            workflow_definition: {
                type: string;
            };
            current_step: {
                type: string;
            };
            execution_log: {
                type: string;
                items: {
                    type: string;
                };
            };
            shared_context: {
                type: string;
            };
            error_log: {
                type: string;
                items: {
                    type: string;
                };
            };
            retry_attempts: {
                type: string;
            };
            max_retries: {
                type: string;
            };
        };
        required: string[];
    };
    vectorEmbeddings: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            embedding_id: {
                type: string;
            };
            source_type: {
                type: string;
            };
            source_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
            embedding: {
                type: string;
                items: {
                    type: string;
                };
            };
            embedding_model: {
                type: string;
            };
            embedding_version: {
                type: string;
            };
            content: {
                type: string;
            };
            metadata: {
                type: string;
            };
            usage_stats: {
                type: string;
            };
        };
        required: string[];
    };
    toolExecutions: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            execution_id: {
                type: string;
            };
            tool_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            executed_at: {
                type: string;
                format: string;
            };
            input: {
                type: string;
            };
            output: {
                type: string;
            };
            performance: {
                type: string;
            };
            error: {
                type: string[];
            };
            retry_count: {
                type: string;
            };
            embedding: {
                type: string;
                items: {
                    type: string;
                };
            };
            embedding_model: {
                type: string;
            };
        };
        required: string[];
    };
    traces: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            trace_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            plan_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            trace_data: {
                type: string;
                items: {
                    type: string;
                };
            };
        };
        required: string[];
    };
    dynamicPlans: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            plan_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            goal: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            plan: {
                type: string;
                items: {
                    type: string;
                };
            };
            validation_critera: {
                type: string;
            };
            created_at: {
                type: string;
                format: string;
            };
        };
        required: string[];
    };
    evaluations: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            evaluation_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            agent_version: {
                type: string;
            };
            benchmark_id: {
                type: string;
            };
            executed_at: {
                type: string;
                format: string;
            };
            task_input: {
                type: string;
            };
            agent_output: {
                type: string;
            };
            ground_truth: {
                type: string;
            };
            scores: {
                type: string;
            };
            passed: {
                type: string;
            };
        };
        required: string[];
    };
    humanFeedback: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            feedback_id: {
                type: string;
            };
            workflow_id: {
                type: string;
            };
            trace_id: {
                type: string;
            };
            user_id: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            feedback_type: {
                type: string;
                enum: string[];
            };
            target_trace_step: {
                type: string;
            };
            correction: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
        };
        required: string[];
    };
    agentPermissions: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            agent_id: {
                type: string;
            };
            permissions: {
                type: string;
                items: {
                    type: string;
                    properties: {
                        tool_id: {
                            type: string;
                        };
                        policy: {
                            type: string;
                            enum: string[];
                        };
                        approver_group: {
                            type: string;
                        };
                    };
                    required: string[];
                };
            };
        };
        required: string[];
    };
    resourceRegistry: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            resource_id: {
                type: string;
            };
            resource_type: {
                type: string;
                enum: string[];
            };
            name: {
                type: string;
            };
            description: {
                type: string;
            };
            status: {
                type: string;
                enum: string[];
            };
            access_point: {
                type: string;
            };
            authentication_method: {
                type: string;
            };
            credential_id: {
                type: string;
            };
            metadata: {
                type: string;
            };
        };
        required: string[];
    };
    secureCredentials: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            credential_id: {
                type: string;
            };
            resource_id: {
                type: string;
            };
            credential_type: {
                type: string;
            };
            encrypted_credentials: {
                type: string;
            };
            last_rotated: {
                type: string;
                format: string;
            };
            status: {
                type: string;
                enum: string[];
            };
        };
        required: string[];
    };
    ingestionPipelines: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            pipeline_id: {
                type: string;
            };
            source_type: {
                type: string;
            };
            source_config: {
                type: string;
            };
            trigger: {
                type: string;
                enum: string[];
            };
            processing_steps: {
                type: string;
                items: {
                    type: string;
                };
            };
            status: {
                type: string;
                enum: string[];
            };
            last_run: {
                type: string;
            };
        };
        required: string[];
    };
    agentEvents: {
        $schema: string;
        title: string;
        description: string;
        type: string;
        properties: {
            _id: {
                type: string;
                pattern: string;
            };
            event_id: {
                type: string;
            };
            agent_id: {
                type: string;
            };
            event_type: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
            data: {
                type: string;
            };
        };
        required: string[];
    };
};
//# sourceMappingURL=index.d.ts.map