/**
 * @file SemanticMemoryEngine - Advanced semantic memory management for Universal AI Brain
 *
 * This engine provides sophisticated semantic memory capabilities using MongoDB Atlas Vector Search.
 * It handles memory storage, retrieval, importance scoring, and semantic relationships
 * with production-grade performance and reliability.
 *
 * Features:
 * - Semantic memory storage with vector embeddings
 * - Intelligent memory retrieval based on semantic similarity
 * - Memory importance scoring and decay
 * - Memory clustering and relationship mapping
 * - Real-time memory analytics and optimization
 * - Framework-agnostic memory management
 */
import { MemoryCollection } from '../collections/MemoryCollection';
import { EmbeddingProvider } from '../vector/MongoVectorStore';
export interface Memory {
    id: string;
    content: string;
    embedding?: number[];
    metadata: {
        type: 'conversation' | 'fact' | 'procedure' | 'context' | 'preference';
        importance: number;
        confidence: number;
        source: string;
        framework: string;
        sessionId: string;
        userId?: string;
        tags: string[];
        relationships: string[];
        accessCount: number;
        lastAccessed: Date;
        created: Date;
        updated: Date;
    };
    ttl?: Date;
}
export interface MemorySearchOptions {
    limit?: number;
    minImportance?: number;
    minConfidence?: number;
    types?: Memory['metadata']['type'][];
    frameworks?: string[];
    sessionId?: string;
    userId?: string;
    tags?: string[];
    includeRelated?: boolean;
    timeRange?: {
        start: Date;
        end: Date;
    };
}
export interface MemoryAnalytics {
    totalMemories: number;
    memoriesByType: Record<string, number>;
    memoriesByFramework: Record<string, number>;
    averageImportance: number;
    averageConfidence: number;
    memoryGrowthTrend: {
        date: Date;
        count: number;
    }[];
    topTags: {
        tag: string;
        count: number;
    }[];
    memoryHealth: {
        staleMemories: number;
        lowConfidenceMemories: number;
        orphanedMemories: number;
    };
}
/**
 * SemanticMemoryEngine - Advanced semantic memory management
 *
 * Provides intelligent memory storage and retrieval using MongoDB Atlas Vector Search
 * with sophisticated importance scoring and relationship mapping.
 */
export declare class SemanticMemoryEngine {
    private memoryCollection;
    private embeddingProvider;
    private memoryCache;
    private cacheSize;
    constructor(memoryCollection: MemoryCollection, embeddingProvider?: EmbeddingProvider);
    /**
     * Store a new memory with semantic embedding
     */
    storeMemory(content: string, metadata: Partial<Memory['metadata']>, options?: {
        generateEmbedding?: boolean;
        updateIfExists?: boolean;
        ttl?: Date;
    }): Promise<string>;
    /**
     * Retrieve memories based on semantic similarity
     */
    retrieveRelevantMemories(query: string, options?: MemorySearchOptions): Promise<Memory[]>;
    /**
     * Update memory importance based on usage patterns
     */
    updateMemoryImportance(memoryId: string, importance: number, reason?: string): Promise<void>;
    /**
     * Create relationships between memories
     */
    createMemoryRelationship(memoryId1: string, memoryId2: string, relationshipType?: 'similar' | 'causal' | 'temporal' | 'contextual'): Promise<void>;
    /**
     * Generate comprehensive memory analytics
     */
    generateMemoryAnalytics(timeRange?: {
        start: Date;
        end: Date;
    }): Promise<MemoryAnalytics>;
    private getMemoryById;
    private parseMemoryFromDocument;
    private updateCache;
    private calculateDecayFactor;
    private updateAccessTracking;
    private getRelatedMemories;
    private fallbackTextSearch;
    private arrayToRecord;
}
//# sourceMappingURL=SemanticMemoryEngine.d.ts.map