/**
 * @file VectorSearchEngine - Advanced vector search capabilities for Universal AI Brain
 *
 * This engine provides sophisticated vector search functionality using MongoDB Atlas Vector Search.
 * It handles semantic search, hybrid search, embedding generation, and search optimization
 * with production-grade performance and reliability.
 *
 * Features:
 * - Semantic vector search using MongoDB Atlas Vector Search
 * - Hybrid search combining vector and text search
 * - Multiple embedding provider support
 * - Search result ranking and filtering
 * - Real-time search analytics and optimization
 * - Search caching and performance optimization
 */
import { Db } from 'mongodb';
import { EmbeddingProvider } from '../vector/MongoVectorStore';
export interface SearchResult {
    id: string;
    content: string;
    score: number;
    metadata: Record<string, any>;
    embedding?: number[];
    explanation?: string;
}
export interface SearchOptions {
    limit?: number;
    minScore?: number;
    maxCandidates?: number;
    includeEmbeddings?: boolean;
    includeExplanation?: boolean;
    filters?: Record<string, any>;
    boost?: {
        field: string;
        factor: number;
    }[];
}
export interface HybridSearchOptions extends SearchOptions {
    vectorWeight?: number;
    textWeight?: number;
    textQuery?: string;
}
export interface SearchAnalytics {
    totalSearches: number;
    averageLatency: number;
    averageResultCount: number;
    searchTypeDistribution: {
        semantic: number;
        hybrid: number;
        text: number;
    };
    popularQueries: {
        query: string;
        count: number;
        averageScore: number;
    }[];
    performanceMetrics: {
        cacheHitRate: number;
        averageEmbeddingTime: number;
        averageSearchTime: number;
    };
    qualityMetrics: {
        averageRelevanceScore: number;
        zeroResultRate: number;
        userSatisfactionScore: number;
    };
}
/**
 * VectorSearchEngine - Advanced vector search using MongoDB Atlas Vector Search
 *
 * Provides semantic search, hybrid search, and embedding generation capabilities
 * with intelligent result ranking and optimization.
 */
export declare class VectorSearchEngine {
    private db;
    private embeddingProvider;
    private collectionName;
    private vectorIndexName;
    private textIndexName;
    private searchCache;
    private cacheSize;
    private cacheTTL;
    constructor(db: Db, embeddingProvider?: EmbeddingProvider, collectionName?: string, vectorIndexName?: string, textIndexName?: string);
    /**
     * Perform semantic search using vector similarity
     */
    semanticSearch(query: string, options?: SearchOptions): Promise<SearchResult[]>;
    /**
     * Perform hybrid search combining vector and text search
     */
    hybridSearch(query: string, options?: HybridSearchOptions): Promise<SearchResult[]>;
    /**
     * Create embedding for text
     */
    createEmbedding(text: string): Promise<number[]>;
    /**
     * Perform text-only search
     */
    textSearch(query: string, options?: SearchOptions): Promise<SearchResult[]>;
    /**
     * Get search suggestions based on partial query
     */
    getSearchSuggestions(partialQuery: string, limit?: number): Promise<string[]>;
    private buildVectorSearchPipeline;
    private buildHybridSearchPipeline;
    private processSearchResults;
    private generateCacheKey;
    private getFromCache;
    private setCache;
    private logSearchAnalytics;
}
//# sourceMappingURL=VectorSearchEngine.d.ts.map