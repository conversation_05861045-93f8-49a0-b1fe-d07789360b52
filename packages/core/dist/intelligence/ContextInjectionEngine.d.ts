/**
 * @file ContextInjectionEngine - Intelligent context injection for Universal AI Brain
 *
 * This engine provides sophisticated context injection capabilities that enhance
 * AI framework prompts with relevant semantic context from MongoDB. It optimizes
 * context selection, formatting, and injection for maximum relevance and efficiency.
 *
 * Features:
 * - Intelligent context selection based on semantic relevance
 * - Framework-specific context formatting and optimization
 * - Context compression and summarization
 * - Real-time context analytics and optimization
 * - Multi-modal context support (text, metadata, relationships)
 * - Context caching and performance optimization
 */
import { SemanticMemoryEngine } from './SemanticMemoryEngine';
import { VectorSearchEngine } from './VectorSearchEngine';
export interface ContextItem {
    id: string;
    content: string;
    type: 'memory' | 'fact' | 'procedure' | 'example' | 'preference' | 'relationship';
    relevanceScore: number;
    importance: number;
    source: string;
    metadata: Record<string, any>;
    relationships?: string[];
}
export interface EnhancedPrompt {
    originalPrompt: string;
    enhancedPrompt: string;
    injectedContext: ContextItem[];
    contextSummary: string;
    optimizationMetrics: {
        contextRelevance: number;
        contextDensity: number;
        tokenCount: number;
        compressionRatio: number;
    };
    framework: string;
    timestamp: Date;
}
export interface ContextOptions {
    maxContextItems?: number;
    maxTokens?: number;
    minRelevanceScore?: number;
    includeRelationships?: boolean;
    contextTypes?: ContextItem['type'][];
    framework?: string;
    sessionId?: string;
    userId?: string;
    compressionLevel?: 'none' | 'light' | 'medium' | 'aggressive';
    prioritizeRecent?: boolean;
    includeMetadata?: boolean;
}
export interface ContextAnalytics {
    totalContextInjections: number;
    averageRelevanceScore: number;
    averageContextItems: number;
    averageTokenCount: number;
    frameworkUsage: Record<string, number>;
    contextTypeUsage: Record<string, number>;
    performanceMetrics: {
        averageInjectionTime: number;
        cacheHitRate: number;
        compressionEfficiency: number;
    };
    qualityMetrics: {
        userSatisfactionScore: number;
        contextAccuracyScore: number;
        relevanceImprovement: number;
    };
}
/**
 * ContextInjectionEngine - Intelligent context injection for AI frameworks
 *
 * Enhances AI prompts with relevant semantic context using MongoDB-powered
 * semantic search and intelligent context optimization.
 */
export declare class ContextInjectionEngine {
    private semanticMemoryEngine;
    private vectorSearchEngine;
    private contextCache;
    private cacheSize;
    private cacheTTL;
    constructor(semanticMemoryEngine: SemanticMemoryEngine, vectorSearchEngine: VectorSearchEngine);
    /**
     * Enhance a prompt with relevant context
     */
    enhancePrompt(prompt: string, options?: ContextOptions): Promise<EnhancedPrompt>;
    /**
     * Select relevant context items based on semantic similarity
     */
    selectRelevantContext(query: string, options: ContextOptions): Promise<ContextItem[]>;
    /**
     * Optimize context for specific framework
     */
    optimizeContextForFramework(context: ContextItem[], framework: string, maxTokens: number, compressionLevel: 'none' | 'light' | 'medium' | 'aggressive'): Promise<ContextItem[]>;
    /**
     * Inject context into prompt with framework-specific formatting
     */
    private injectContextIntoPrompt;
    /**
     * Format context items for specific framework
     */
    private formatContextForFramework;
    /**
     * Generate a summary of injected context
     */
    private generateContextSummary;
    /**
     * Calculate optimization metrics
     */
    private calculateOptimizationMetrics;
    private memoryToContextItem;
    private vectorResultToContextItem;
    private getContextAge;
    private optimizeForVercelAI;
    private optimizeForMastra;
    private optimizeForLangChain;
    private optimizeForOpenAIAgents;
    private compressContext;
    private enforceTokenLimit;
    private truncateContent;
    private estimateTokenCount;
    private generateCacheKey;
    private getFromCache;
    private setCache;
}
//# sourceMappingURL=ContextInjectionEngine.d.ts.map