/**
 * @file Intelligence Layer - Core intelligence components for Universal AI Brain
 *
 * This module exports the core intelligence components that provide semantic memory,
 * context injection, and vector search capabilities for the Universal AI Brain.
 * These components work together to provide intelligent context-aware AI interactions.
 */
export { SemanticMemoryEngine } from './SemanticMemoryEngine';
export { ContextInjectionEngine } from './ContextInjectionEngine';
export { VectorSearchEngine } from './VectorSearchEngine';
export type { Memory, MemorySearchOptions, MemoryAnalytics } from './SemanticMemoryEngine';
export type { ContextItem, EnhancedPrompt, ContextOptions, ContextAnalytics } from './ContextInjectionEngine';
export type { SearchResult, SearchOptions, HybridSearchOptions, SearchAnalytics } from './VectorSearchEngine';
//# sourceMappingURL=index.d.ts.map