"use strict";
/**
 * @file UniversalAIBrain - Main orchestrator for the Universal AI Brain system
 *
 * This is the central orchestrator that integrates all components of the Universal AI Brain:
 * MongoDB-powered intelligence, semantic memory, context injection, safety systems,
 * self-improvement engines, and real-time monitoring. Provides a unified interface
 * for any TypeScript framework to integrate and gain superpowers.
 *
 * Features:
 * - Framework-agnostic integration layer
 * - MongoDB Atlas Vector Search intelligence
 * - Comprehensive safety and compliance systems
 * - Self-improvement and optimization engines
 * - Real-time monitoring and analytics
 * - Production-ready with enterprise-grade reliability
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalAIBrain = void 0;
const mongodb_1 = require("mongodb");
const MongoVectorStore_1 = require("./vector/MongoVectorStore");
const MongoConnection_1 = require("./persistance/MongoConnection");
// Core Collections
const TracingCollection_1 = require("./collections/TracingCollection");
const MemoryCollection_1 = require("./collections/MemoryCollection");
const ContextCollection_1 = require("./collections/ContextCollection");
// Intelligence Layer
const SemanticMemoryEngine_1 = require("./intelligence/SemanticMemoryEngine");
const ContextInjectionEngine_1 = require("./intelligence/ContextInjectionEngine");
const VectorSearchEngine_1 = require("./intelligence/VectorSearchEngine");
// Safety & Guardrails
const SafetyGuardrailsEngine_1 = require("./safety/SafetyGuardrailsEngine");
const HallucinationDetector_1 = require("./safety/HallucinationDetector");
const PIIDetector_1 = require("./safety/PIIDetector");
const ComplianceAuditLogger_1 = require("./safety/ComplianceAuditLogger");
const FrameworkSafetyIntegration_1 = require("./safety/FrameworkSafetyIntegration");
// Self-Improvement
const FailureAnalysisEngine_1 = require("./self-improvement/FailureAnalysisEngine");
const ContextLearningEngine_1 = require("./self-improvement/ContextLearningEngine");
const FrameworkOptimizationEngine_1 = require("./self-improvement/FrameworkOptimizationEngine");
const SelfImprovementMetrics_1 = require("./self-improvement/SelfImprovementMetrics");
// Monitoring
const PerformanceAnalyticsEngine_1 = require("./monitoring/PerformanceAnalyticsEngine");
const RealTimeMonitoringDashboard_1 = require("./monitoring/RealTimeMonitoringDashboard");
/**
 * UniversalAIBrain - The central orchestrator for AI intelligence
 *
 * Provides a unified interface for any TypeScript framework to integrate
 * with MongoDB-powered AI intelligence, safety systems, and self-improvement.
 */
class UniversalAIBrain {
    constructor(config) {
        this.isInitialized = false;
        this.config = config;
        this.mongoClient = new mongodb_1.MongoClient(config.mongodb.connectionString);
    }
    /**
     * Initialize the Universal AI Brain system
     */
    async initialize() {
        if (this.isInitialized) {
            throw new Error('Universal AI Brain is already initialized');
        }
        try {
            // Connect to MongoDB
            await this.mongoClient.connect();
            this.database = this.mongoClient.db(this.config.mongodb.databaseName);
            // Initialize core collections
            await this.initializeCollections();
            // Initialize intelligence layer
            await this.initializeIntelligenceLayer();
            // Initialize safety systems
            await this.initializeSafetySystems();
            // Initialize self-improvement engines
            await this.initializeSelfImprovementEngines();
            // Initialize monitoring systems
            await this.initializeMonitoringSystems();
            // Start real-time monitoring if enabled
            if (this.config.monitoring.enableRealTimeMonitoring) {
                await this.realTimeMonitoringDashboard.startMonitoring();
            }
            this.isInitialized = true;
            console.log('🧠 Universal AI Brain initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize Universal AI Brain:', error);
            throw error;
        }
    }
    /**
     * Process AI request with full intelligence and safety pipeline
     */
    async processRequest(framework, input, context, sessionId) {
        if (!this.isInitialized) {
            throw new Error('Universal AI Brain must be initialized before processing requests');
        }
        const startTime = Date.now();
        const traceId = `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        try {
            // 1. Pre-processing safety validation
            const inputValidation = await this.frameworkSafetyIntegration.validateInput(framework, input, context, sessionId);
            if (!inputValidation.allowed) {
                return {
                    success: false,
                    error: `Input blocked by safety system: ${inputValidation.violations.map(v => v.description).join(', ')}`,
                    metadata: {
                        responseTime: Date.now() - startTime,
                        tokensUsed: 0,
                        cost: 0,
                        safetyScore: 0,
                        contextUsed: [],
                        traceId
                    }
                };
            }
            // 2. Context injection and semantic memory retrieval
            const enhancedContext = await this.contextInjectionEngine.enhancePrompt(inputValidation.filteredContent || input, context, framework);
            // 3. Vector search for relevant information
            const relevantMemories = await this.vectorSearchEngine.semanticSearch(inputValidation.filteredContent || input, {
                limit: 10,
                minScore: this.config.intelligence.similarityThreshold,
                includeMetadata: true
            });
            // 4. Process with framework (this would be implemented by framework adapters)
            const processedResult = await this.processWithFramework(framework, inputValidation.filteredContent || input, enhancedContext, relevantMemories);
            // 5. Post-processing safety validation
            const outputValidation = await this.frameworkSafetyIntegration.validateOutput(framework, processedResult.output, enhancedContext, sessionId);
            if (!outputValidation.allowed) {
                return {
                    success: false,
                    error: `Output blocked by safety system: ${outputValidation.violations.map(v => v.description).join(', ')}`,
                    metadata: {
                        responseTime: Date.now() - startTime,
                        tokensUsed: processedResult.tokensUsed,
                        cost: processedResult.cost,
                        safetyScore: 0,
                        contextUsed: enhancedContext.sources,
                        traceId
                    }
                };
            }
            // 6. Store interaction for learning
            await this.storeInteraction({
                traceId,
                framework,
                input: inputValidation.filteredContent || input,
                output: outputValidation.filteredContent || processedResult.output,
                context: enhancedContext,
                relevantMemories,
                tokensUsed: processedResult.tokensUsed,
                cost: processedResult.cost,
                responseTime: Date.now() - startTime,
                safetyScore: this.calculateSafetyScore(inputValidation, outputValidation),
                sessionId
            });
            // 7. Trigger self-improvement if enabled
            if (this.config.selfImprovement.enableAutomaticOptimization) {
                await this.triggerSelfImprovement(framework, traceId);
            }
            return {
                success: true,
                data: outputValidation.filteredContent || processedResult.output,
                metadata: {
                    responseTime: Date.now() - startTime,
                    tokensUsed: processedResult.tokensUsed,
                    cost: processedResult.cost,
                    safetyScore: this.calculateSafetyScore(inputValidation, outputValidation),
                    contextUsed: enhancedContext.sources,
                    traceId
                }
            };
        }
        catch (error) {
            // Log failure for analysis
            await this.failureAnalysisEngine.analyzeFailures(new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            new Date(), {
                frameworks: [framework],
                minFrequency: 1
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred',
                metadata: {
                    responseTime: Date.now() - startTime,
                    tokensUsed: 0,
                    cost: 0,
                    safetyScore: 0,
                    contextUsed: [],
                    traceId
                }
            };
        }
    }
    /**
     * Get real-time dashboard metrics
     */
    async getDashboardMetrics() {
        if (!this.isInitialized) {
            throw new Error('Universal AI Brain must be initialized');
        }
        return await this.realTimeMonitoringDashboard.getCurrentDashboardMetrics();
    }
    /**
     * Shutdown the Universal AI Brain system
     */
    async shutdown() {
        if (!this.isInitialized) {
            return;
        }
        try {
            // Stop monitoring
            if (this.config.monitoring.enableRealTimeMonitoring) {
                await this.realTimeMonitoringDashboard.stopMonitoring();
            }
            // Close MongoDB connection
            await this.mongoClient.close();
            this.isInitialized = false;
            console.log('🧠 Universal AI Brain shutdown complete');
        }
        catch (error) {
            console.error('Error during shutdown:', error);
            throw error;
        }
    }
    // Private initialization methods
    async initializeCollections() {
        this.tracingCollection = new TracingCollection_1.TracingCollection(this.database);
        this.memoryCollection = new MemoryCollection_1.MemoryCollection(this.database);
        this.contextCollection = new ContextCollection_1.ContextCollection(this.database);
        this.metricsCollection = new MemoryCollection_1.MemoryCollection(this.database);
        this.auditCollection = new MemoryCollection_1.MemoryCollection(this.database);
        await Promise.all([
            this.tracingCollection.createIndexes(),
            this.memoryCollection.createIndexes(),
            this.contextCollection.createIndexes(),
            this.metricsCollection.createIndexes(),
            this.auditCollection.createIndexes()
        ]);
    }
    async initializeIntelligenceLayer() {
        this.semanticMemoryEngine = new SemanticMemoryEngine_1.SemanticMemoryEngine(this.memoryCollection);
        this.vectorSearchEngine = new VectorSearchEngine_1.VectorSearchEngine(this.database);
        // Initialize vector store
        const mongoConnection = MongoConnection_1.MongoConnection.getInstance(this.config.mongodb.connectionString);
        this.vectorStore = new MongoVectorStore_1.MongoVectorStore(mongoConnection);
        this.contextInjectionEngine = new ContextInjectionEngine_1.ContextInjectionEngine(this.semanticMemoryEngine, this.vectorSearchEngine);
    }
    async initializeSafetySystems() {
        this.safetyEngine = new SafetyGuardrailsEngine_1.SafetyGuardrailsEngine(this.tracingCollection, this.memoryCollection);
        this.hallucinationDetector = new HallucinationDetector_1.HallucinationDetector(this.tracingCollection, this.memoryCollection, this.vectorStore);
        this.piiDetector = new PIIDetector_1.PIIDetector(this.tracingCollection, this.memoryCollection);
        this.complianceAuditLogger = new ComplianceAuditLogger_1.ComplianceAuditLogger(this.tracingCollection, this.memoryCollection, this.auditCollection);
        this.frameworkSafetyIntegration = new FrameworkSafetyIntegration_1.FrameworkSafetyIntegration(this.safetyEngine, this.hallucinationDetector, this.piiDetector, this.complianceAuditLogger, this.tracingCollection, this.memoryCollection);
    }
    async initializeSelfImprovementEngines() {
        this.failureAnalysisEngine = new FailureAnalysisEngine_1.FailureAnalysisEngine(this.tracingCollection, this.memoryCollection);
        this.contextLearningEngine = new ContextLearningEngine_1.ContextLearningEngine(this.tracingCollection, this.memoryCollection, this.vectorStore);
        this.frameworkOptimizationEngine = new FrameworkOptimizationEngine_1.FrameworkOptimizationEngine(this.tracingCollection, this.memoryCollection);
        this.selfImprovementMetrics = new SelfImprovementMetrics_1.SelfImprovementMetrics(this.tracingCollection, this.memoryCollection, this.failureAnalysisEngine, this.contextLearningEngine, this.frameworkOptimizationEngine);
    }
    async initializeMonitoringSystems() {
        this.performanceAnalyticsEngine = new PerformanceAnalyticsEngine_1.PerformanceAnalyticsEngine(this.tracingCollection, this.memoryCollection, this.metricsCollection);
        this.realTimeMonitoringDashboard = new RealTimeMonitoringDashboard_1.RealTimeMonitoringDashboard(this.performanceAnalyticsEngine, this.frameworkSafetyIntegration, this.complianceAuditLogger, this.selfImprovementMetrics, this.tracingCollection, this.memoryCollection, {
            refreshInterval: this.config.monitoring.dashboardRefreshInterval,
            displayOptions: {
                showHistoricalData: true,
                timeRange: '24h',
                autoRefresh: this.config.monitoring.enableRealTimeMonitoring,
                enableNotifications: this.config.monitoring.alertingEnabled
            }
        });
    }
    async processWithFramework(framework, input, context, relevantMemories) {
        // This would be implemented by framework-specific adapters
        // For now, return a mock response
        return {
            output: `Processed by ${framework}: ${input}`,
            tokensUsed: Math.floor(Math.random() * 1000) + 100,
            cost: Math.random() * 0.01
        };
    }
    async storeInteraction(interaction) {
        await this.tracingCollection.startTrace({
            traceId: interaction.traceId,
            agentId: new mongodb_1.ObjectId(), // Generate a new agent ID for now
            sessionId: interaction.sessionId || 'default-session',
            operation: {
                type: 'chat',
                userInput: interaction.input,
                finalOutput: interaction.output
            },
            framework: {
                frameworkName: interaction.framework,
                frameworkVersion: '1.0.0'
            }
        });
    }
    calculateSafetyScore(inputValidation, outputValidation) {
        const inputScore = inputValidation.violations.length === 0 ? 100 :
            Math.max(0, 100 - (inputValidation.violations.length * 20));
        const outputScore = outputValidation.violations.length === 0 ? 100 :
            Math.max(0, 100 - (outputValidation.violations.length * 20));
        return Math.round((inputScore + outputScore) / 2);
    }
    async triggerSelfImprovement(framework, traceId) {
        // Trigger self-improvement processes asynchronously
        setImmediate(async () => {
            try {
                await this.selfImprovementMetrics.processFeedbackLoops();
            }
            catch (error) {
                console.error('Self-improvement process failed:', error);
            }
        });
    }
}
exports.UniversalAIBrain = UniversalAIBrain;
