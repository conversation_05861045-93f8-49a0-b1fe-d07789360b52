"use strict";
// ============================================================================
// UNIVERSAL AI BRAIN - CORE EXPORTS
// ============================================================================
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealTimeMonitoringDashboard = exports.PerformanceAnalyticsEngine = exports.SelfImprovementMetrics = exports.FrameworkOptimizationEngine = exports.ContextLearningEngine = exports.FailureAnalysisEngine = exports.FrameworkSafetyIntegration = exports.ComplianceAuditLogger = exports.PIIDetector = exports.HallucinationDetector = exports.SafetyEngine = exports.SafetyGuardrailsEngine = exports.VectorSearchEngine = exports.ContextInjectionEngine = exports.SemanticMemoryEngine = exports.CollectionManager = exports.TracingCollection = exports.MetricsCollection = exports.ToolCollection = exports.WorkflowCollection = exports.MemoryCollection = exports.AgentCollection = exports.BaseCollection = exports.OpenAIEmbeddingProvider = exports.MongoVectorStore = exports.OpenAIAgentsAdapter = exports.LangChainJSAdapter = exports.VercelAIAdapter = exports.MastraAdapter = exports.BaseFrameworkAdapter = exports.UniversalAIBrainV2 = exports.UniversalAIBrain = void 0;
// Core Universal AI Brain - The heart of your vision! 🧠⚡
var UniversalAIBrain_1 = require("./brain/UniversalAIBrain");
Object.defineProperty(exports, "UniversalAIBrain", { enumerable: true, get: function () { return UniversalAIBrain_1.UniversalAIBrain; } });
var UniversalAIBrain_2 = require("./UniversalAIBrain");
Object.defineProperty(exports, "UniversalAIBrainV2", { enumerable: true, get: function () { return UniversalAIBrain_2.UniversalAIBrain; } });
// Framework adapters - The magic that connects ANY framework to MongoDB! 🔌
var BaseFrameworkAdapter_1 = require("./adapters/BaseFrameworkAdapter");
Object.defineProperty(exports, "BaseFrameworkAdapter", { enumerable: true, get: function () { return BaseFrameworkAdapter_1.BaseFrameworkAdapter; } });
var MastraAdapter_1 = require("./adapters/MastraAdapter");
Object.defineProperty(exports, "MastraAdapter", { enumerable: true, get: function () { return MastraAdapter_1.MastraAdapter; } });
var VercelAIAdapter_1 = require("./adapters/VercelAIAdapter");
Object.defineProperty(exports, "VercelAIAdapter", { enumerable: true, get: function () { return VercelAIAdapter_1.VercelAIAdapter; } });
var LangChainJSAdapter_1 = require("./adapters/LangChainJSAdapter");
Object.defineProperty(exports, "LangChainJSAdapter", { enumerable: true, get: function () { return LangChainJSAdapter_1.LangChainJSAdapter; } });
var OpenAIAgentsAdapter_1 = require("./adapters/OpenAIAgentsAdapter");
Object.defineProperty(exports, "OpenAIAgentsAdapter", { enumerable: true, get: function () { return OpenAIAgentsAdapter_1.OpenAIAgentsAdapter; } });
// Vector Search and Embeddings
var MongoVectorStore_1 = require("./vector/MongoVectorStore");
Object.defineProperty(exports, "MongoVectorStore", { enumerable: true, get: function () { return MongoVectorStore_1.MongoVectorStore; } });
var OpenAIEmbeddingProvider_1 = require("./embeddings/OpenAIEmbeddingProvider");
Object.defineProperty(exports, "OpenAIEmbeddingProvider", { enumerable: true, get: function () { return OpenAIEmbeddingProvider_1.OpenAIEmbeddingProvider; } });
// Core types and interfaces
__exportStar(require("./types"), exports);
// MongoDB persistence layer (enhanced with Vector Search)
__exportStar(require("./persistance"), exports);
// MongoDB collections - The data layer that powers everything! 💾
var index_1 = require("./collections/index");
Object.defineProperty(exports, "BaseCollection", { enumerable: true, get: function () { return index_1.BaseCollection; } });
Object.defineProperty(exports, "AgentCollection", { enumerable: true, get: function () { return index_1.AgentCollection; } });
Object.defineProperty(exports, "MemoryCollection", { enumerable: true, get: function () { return index_1.MemoryCollection; } });
Object.defineProperty(exports, "WorkflowCollection", { enumerable: true, get: function () { return index_1.WorkflowCollection; } });
Object.defineProperty(exports, "ToolCollection", { enumerable: true, get: function () { return index_1.ToolCollection; } });
Object.defineProperty(exports, "MetricsCollection", { enumerable: true, get: function () { return index_1.MetricsCollection; } });
Object.defineProperty(exports, "TracingCollection", { enumerable: true, get: function () { return index_1.TracingCollection; } });
Object.defineProperty(exports, "CollectionManager", { enumerable: true, get: function () { return index_1.CollectionManager; } });
// MongoDB schemas
__exportStar(require("./schemas"), exports);
// Enterprise tracing and observability - Production-grade monitoring! 🔍
__exportStar(require("./tracing"), exports);
// NEW: Universal AI Brain V2 Components - Production-Ready Intelligence Layer! 🚀
// Intelligence Layer
var SemanticMemoryEngine_1 = require("./intelligence/SemanticMemoryEngine");
Object.defineProperty(exports, "SemanticMemoryEngine", { enumerable: true, get: function () { return SemanticMemoryEngine_1.SemanticMemoryEngine; } });
var ContextInjectionEngine_1 = require("./intelligence/ContextInjectionEngine");
Object.defineProperty(exports, "ContextInjectionEngine", { enumerable: true, get: function () { return ContextInjectionEngine_1.ContextInjectionEngine; } });
var VectorSearchEngine_1 = require("./intelligence/VectorSearchEngine");
Object.defineProperty(exports, "VectorSearchEngine", { enumerable: true, get: function () { return VectorSearchEngine_1.VectorSearchEngine; } });
// Safety & Guardrails
var SafetyGuardrailsEngine_1 = require("./safety/SafetyGuardrailsEngine");
Object.defineProperty(exports, "SafetyGuardrailsEngine", { enumerable: true, get: function () { return SafetyGuardrailsEngine_1.SafetyGuardrailsEngine; } });
var SafetyGuardrailsEngine_2 = require("./safety/SafetyGuardrailsEngine"); // Alias for backward compatibility
Object.defineProperty(exports, "SafetyEngine", { enumerable: true, get: function () { return SafetyGuardrailsEngine_2.SafetyGuardrailsEngine; } });
var HallucinationDetector_1 = require("./safety/HallucinationDetector");
Object.defineProperty(exports, "HallucinationDetector", { enumerable: true, get: function () { return HallucinationDetector_1.HallucinationDetector; } });
var PIIDetector_1 = require("./safety/PIIDetector");
Object.defineProperty(exports, "PIIDetector", { enumerable: true, get: function () { return PIIDetector_1.PIIDetector; } });
var ComplianceAuditLogger_1 = require("./safety/ComplianceAuditLogger");
Object.defineProperty(exports, "ComplianceAuditLogger", { enumerable: true, get: function () { return ComplianceAuditLogger_1.ComplianceAuditLogger; } });
var FrameworkSafetyIntegration_1 = require("./safety/FrameworkSafetyIntegration");
Object.defineProperty(exports, "FrameworkSafetyIntegration", { enumerable: true, get: function () { return FrameworkSafetyIntegration_1.FrameworkSafetyIntegration; } });
// Self-Improvement Engines
var FailureAnalysisEngine_1 = require("./self-improvement/FailureAnalysisEngine");
Object.defineProperty(exports, "FailureAnalysisEngine", { enumerable: true, get: function () { return FailureAnalysisEngine_1.FailureAnalysisEngine; } });
var ContextLearningEngine_1 = require("./self-improvement/ContextLearningEngine");
Object.defineProperty(exports, "ContextLearningEngine", { enumerable: true, get: function () { return ContextLearningEngine_1.ContextLearningEngine; } });
var FrameworkOptimizationEngine_1 = require("./self-improvement/FrameworkOptimizationEngine");
Object.defineProperty(exports, "FrameworkOptimizationEngine", { enumerable: true, get: function () { return FrameworkOptimizationEngine_1.FrameworkOptimizationEngine; } });
var SelfImprovementMetrics_1 = require("./self-improvement/SelfImprovementMetrics");
Object.defineProperty(exports, "SelfImprovementMetrics", { enumerable: true, get: function () { return SelfImprovementMetrics_1.SelfImprovementMetrics; } });
// Real-Time Monitoring
var PerformanceAnalyticsEngine_1 = require("./monitoring/PerformanceAnalyticsEngine");
Object.defineProperty(exports, "PerformanceAnalyticsEngine", { enumerable: true, get: function () { return PerformanceAnalyticsEngine_1.PerformanceAnalyticsEngine; } });
var RealTimeMonitoringDashboard_1 = require("./monitoring/RealTimeMonitoringDashboard");
Object.defineProperty(exports, "RealTimeMonitoringDashboard", { enumerable: true, get: function () { return RealTimeMonitoringDashboard_1.RealTimeMonitoringDashboard; } });
// Legacy exports (keeping for backward compatibility)
__exportStar(require("./agent"), exports);
__exportStar(require("./features"), exports);
__exportStar(require("./real-time"), exports);
