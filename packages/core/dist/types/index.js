"use strict";
/**
 * @file Core Types and Interfaces for Universal AI Brain
 *
 * This file defines all the core TypeScript interfaces and types used throughout
 * the Universal AI Brain system. These types ensure type safety and provide
 * clear contracts for framework integrations.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorSearchError = exports.FrameworkIntegrationError = exports.BrainError = void 0;
// ============================================================================
// ERROR TYPES
// ============================================================================
class BrainError extends Error {
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'BrainError';
    }
}
exports.BrainError = BrainError;
class FrameworkIntegrationError extends BrainError {
    constructor(frameworkName, message, details) {
        super(`Framework integration error (${frameworkName}): ${message}`, 'FRAMEWORK_INTEGRATION_ERROR', details);
        this.name = 'FrameworkIntegrationError';
    }
}
exports.FrameworkIntegrationError = FrameworkIntegrationError;
class VectorSearchError extends BrainError {
    constructor(message, details) {
        super(`Vector search error: ${message}`, 'VECTOR_SEARCH_ERROR', details);
        this.name = 'VectorSearchError';
    }
}
exports.VectorSearchError = VectorSearchError;
