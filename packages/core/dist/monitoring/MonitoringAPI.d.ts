/**
 * @file MonitoringAPI - Comprehensive real-time monitoring API system
 *
 * This API provides REST endpoints for real-time metrics, WebSocket support for
 * live updates, and GraphQL interface for flexible data querying. Built using
 * Express.js patterns with MongoDB integration, authentication, and rate limiting.
 *
 * Features:
 * - RESTful API endpoints for all monitoring data
 * - WebSocket real-time updates using Socket.IO patterns
 * - GraphQL interface for flexible data querying
 * - JWT authentication and role-based access control
 * - Rate limiting and API security
 * - Real-time dashboard data streaming
 * - Comprehensive API documentation with OpenAPI/Swagger
 */
import { PerformanceAnalyticsEngine } from './PerformanceAnalyticsEngine';
import { ErrorTrackingEngine } from './ErrorTrackingEngine';
import { CostMonitoringEngine } from './CostMonitoringEngine';
import { RealTimeMonitoringDashboard } from './RealTimeMonitoringDashboard';
export interface APIConfiguration {
    port: number;
    jwtSecret: string;
    corsOrigins: string[];
    rateLimiting: {
        windowMs: number;
        maxRequests: number;
    };
    websocket: {
        enabled: boolean;
        updateInterval: number;
    };
    graphql: {
        enabled: boolean;
        introspection: boolean;
        playground: boolean;
    };
    authentication: {
        required: boolean;
        roles: string[];
    };
}
export interface APIUser {
    id: string;
    username: string;
    email: string;
    roles: string[];
    permissions: string[];
    lastLogin: Date;
}
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    metadata: {
        timestamp: Date;
        requestId: string;
        version: string;
        rateLimit?: {
            remaining: number;
            resetTime: Date;
        };
    };
}
export interface WebSocketEvent {
    type: 'metrics_update' | 'error_alert' | 'cost_alert' | 'system_health' | 'trace_update';
    data: any;
    timestamp: Date;
    source: string;
}
export interface GraphQLContext {
    user?: APIUser;
    dataSources: {
        performanceEngine: PerformanceAnalyticsEngine;
        errorEngine: ErrorTrackingEngine;
        costEngine: CostMonitoringEngine;
        dashboard: RealTimeMonitoringDashboard;
    };
}
/**
 * MonitoringAPI - Comprehensive real-time monitoring API system
 *
 * Provides REST, WebSocket, and GraphQL interfaces for accessing all
 * Universal AI Brain monitoring data with authentication and security.
 */
export declare class MonitoringAPI {
    private app;
    private server;
    private io?;
    private config;
    private performanceEngine;
    private errorEngine;
    private costEngine;
    private dashboard;
    private connectedClients;
    private updateInterval?;
    constructor(config: APIConfiguration, performanceEngine: PerformanceAnalyticsEngine, errorEngine: ErrorTrackingEngine, costEngine: CostMonitoringEngine, dashboard: RealTimeMonitoringDashboard);
    /**
     * Start the monitoring API server
     */
    start(): Promise<void>;
    /**
     * Stop the monitoring API server
     */
    stop(): Promise<void>;
    private setupMiddleware;
    private setupRoutes;
    private setupWebSocket;
    private getPerformanceMetrics;
    private getFrameworkPerformance;
    private getPerformanceTrends;
    private getErrorAnalytics;
    private getErrorPatterns;
    private trackError;
    private getCostAnalytics;
    private getCostBudgets;
    private createCostBudget;
    private getCostOptimizations;
    private getDashboardMetrics;
    private getDashboardWidgets;
    private createDashboardWidget;
    private getRealTimeStatus;
    private subscribeToUpdates;
    private authenticateToken;
    private verifyToken;
    private createResponse;
    private parseTimeRange;
    private startRealTimeUpdates;
    private broadcastToSubscribers;
    private getAPIDocumentation;
}
//# sourceMappingURL=MonitoringAPI.d.ts.map