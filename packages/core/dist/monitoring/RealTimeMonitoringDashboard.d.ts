/**
 * @file RealTimeMonitoringDashboard - Comprehensive real-time monitoring system
 *
 * This dashboard provides real-time monitoring of the Universal AI Brain with live
 * performance metrics, safety alerts, system health, and framework-specific insights.
 * Integrates all monitoring components into a unified dashboard interface.
 *
 * Features:
 * - Real-time performance metrics streaming
 * - Live safety and compliance monitoring
 * - System health and resource utilization tracking
 * - Framework-specific performance dashboards
 * - Alert management and notification system
 * - Historical data visualization and trending
 * - Interactive analytics and drill-down capabilities
 */
import { PerformanceAnalyticsEngine, PerformanceMetrics, PerformanceAlert } from './PerformanceAnalyticsEngine';
import { FrameworkSafetyIntegration, FrameworkSafetyMetrics } from '../safety/FrameworkSafetyIntegration';
import { ComplianceAuditLogger } from '../safety/ComplianceAuditLogger';
import { SelfImprovementMetrics } from '../self-improvement/SelfImprovementMetrics';
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface DashboardMetrics {
    timestamp: Date;
    systemHealth: {
        status: 'healthy' | 'warning' | 'critical' | 'degraded';
        uptime: number;
        availability: number;
        responseTime: number;
        errorRate: number;
        throughput: number;
    };
    performance: PerformanceMetrics;
    safety: {
        overallSafetyScore: number;
        activeAlerts: number;
        complianceStatus: 'compliant' | 'warning' | 'violation';
        recentViolations: number;
        frameworkSafety: Record<string, FrameworkSafetyMetrics>;
    };
    frameworks: {
        name: string;
        status: 'active' | 'inactive' | 'error';
        performance: {
            responseTime: number;
            successRate: number;
            throughput: number;
            errorRate: number;
        };
        lastActivity: Date;
    }[];
    alerts: PerformanceAlert[];
    trends: {
        responseTime: 'improving' | 'stable' | 'degrading';
        cost: 'decreasing' | 'stable' | 'increasing';
        safety: 'improving' | 'stable' | 'degrading';
        usage: 'growing' | 'stable' | 'declining';
    };
}
export interface DashboardConfiguration {
    refreshInterval: number;
    alertThresholds: {
        responseTime: number;
        errorRate: number;
        safetyScore: number;
        costPerOperation: number;
    };
    enabledFrameworks: string[];
    displayOptions: {
        showHistoricalData: boolean;
        timeRange: '1h' | '6h' | '24h' | '7d' | '30d';
        autoRefresh: boolean;
        enableNotifications: boolean;
    };
    customMetrics: {
        name: string;
        query: string;
        threshold?: number;
        enabled: boolean;
    }[];
}
export interface DashboardWidget {
    widgetId: string;
    type: 'metric' | 'chart' | 'alert' | 'table' | 'gauge' | 'heatmap';
    title: string;
    data: any;
    configuration: {
        size: 'small' | 'medium' | 'large';
        position: {
            x: number;
            y: number;
        };
        refreshRate: number;
        alertEnabled: boolean;
    };
    lastUpdated: Date;
}
export interface AlertNotification {
    notificationId: string;
    timestamp: Date;
    type: 'email' | 'slack' | 'webhook' | 'sms';
    recipient: string;
    alert: PerformanceAlert;
    status: 'pending' | 'sent' | 'failed' | 'acknowledged';
    retryCount: number;
}
/**
 * RealTimeMonitoringDashboard - Comprehensive real-time monitoring system
 *
 * Provides unified real-time monitoring of all Universal AI Brain components
 * with interactive dashboards, alerting, and analytics.
 */
export declare class RealTimeMonitoringDashboard {
    private performanceEngine;
    private safetyIntegration;
    private complianceLogger;
    private improvementMetrics;
    private tracingCollection;
    private memoryCollection;
    private configuration;
    private widgets;
    private activeAlerts;
    private notificationQueue;
    private refreshTimer?;
    private isRunning;
    constructor(performanceEngine: PerformanceAnalyticsEngine, safetyIntegration: FrameworkSafetyIntegration, complianceLogger: ComplianceAuditLogger, improvementMetrics: SelfImprovementMetrics, tracingCollection: TracingCollection, memoryCollection: MemoryCollection, configuration?: Partial<DashboardConfiguration>);
    /**
     * Start real-time monitoring dashboard
     */
    startMonitoring(): Promise<void>;
    /**
     * Stop real-time monitoring dashboard
     */
    stopMonitoring(): Promise<void>;
    /**
     * Get current dashboard metrics
     */
    getCurrentDashboardMetrics(): Promise<DashboardMetrics>;
    /**
     * Add custom widget to dashboard
     */
    addWidget(widget: Omit<DashboardWidget, 'widgetId' | 'lastUpdated'>): Promise<string>;
    /**
     * Update dashboard configuration
     */
    updateConfiguration(newConfig: Partial<DashboardConfiguration>): Promise<void>;
    /**
     * Get dashboard analytics for a specific time range
     */
    getDashboardAnalytics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<{
        totalOperations: number;
        averageResponseTime: number;
        successRate: number;
        costAnalysis: any;
        topErrors: {
            error: string;
            count: number;
        }[];
        frameworkComparison: any;
    }>;
    private refreshDashboard;
    private updateWidget;
    private checkAlerts;
    private createAlert;
    private processNotifications;
    private sendNotification;
    private initializeDefaultWidgets;
    private getTimeRangeFromConfig;
    private calculateSystemHealth;
    private calculateOverallSafetyScore;
    private getFrameworkStatus;
    private calculateTrends;
    private getMetricData;
    private getChartData;
    private getTableData;
}
//# sourceMappingURL=RealTimeMonitoringDashboard.d.ts.map