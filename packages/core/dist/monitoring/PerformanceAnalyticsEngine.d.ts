/**
 * @file PerformanceAnalyticsEngine - Comprehensive performance analytics system
 *
 * This engine tracks response times, token usage, cost per operation, success rates,
 * and framework-specific metrics with real-time aggregation and historical trending
 * using official MongoDB monitoring patterns and time series collections.
 *
 * Features:
 * - Real-time performance metrics collection using MongoDB time series
 * - Framework-specific performance analytics (Vercel AI, Mastra, OpenAI Agents, LangChain)
 * - Query profiler integration for operation execution analysis
 * - Cost tracking and optimization recommendations
 * - Historical trending with MongoDB aggregation pipelines
 * - Performance alerting and anomaly detection
 * - Resource utilization monitoring
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface PerformanceMetrics {
    metricId: string;
    timestamp: Date;
    framework: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain' | 'all';
    timeRange: {
        start: Date;
        end: Date;
    };
    responseTime: {
        average: number;
        p50: number;
        p95: number;
        p99: number;
        min: number;
        max: number;
        trend: 'improving' | 'stable' | 'degrading';
    };
    tokenUsage: {
        totalTokens: number;
        inputTokens: number;
        outputTokens: number;
        averagePerOperation: number;
        efficiency: number;
    };
    costAnalysis: {
        totalCost: number;
        costPerOperation: number;
        costPerToken: number;
        costBreakdown: {
            modelCosts: number;
            embeddingCosts: number;
            mongodbCosts: number;
            otherCosts: number;
        };
        trend: 'increasing' | 'stable' | 'decreasing';
    };
    successRates: {
        overall: number;
        byFramework: Record<string, number>;
        byOperation: Record<string, number>;
        errorBreakdown: {
            type: string;
            count: number;
            percentage: number;
        }[];
    };
    throughput: {
        operationsPerSecond: number;
        operationsPerMinute: number;
        operationsPerHour: number;
        peakThroughput: number;
        averageThroughput: number;
    };
    resourceUtilization: {
        cpuUsage: number;
        memoryUsage: number;
        diskUsage: number;
        networkIO: number;
        mongodbConnections: number;
    };
}
export interface FrameworkPerformanceComparison {
    comparisonId: string;
    timestamp: Date;
    timeRange: {
        start: Date;
        end: Date;
    };
    frameworks: {
        name: string;
        metrics: {
            averageResponseTime: number;
            successRate: number;
            costPerOperation: number;
            tokenEfficiency: number;
            errorRate: number;
            throughput: number;
        };
        ranking: number;
        strengths: string[];
        weaknesses: string[];
    }[];
    recommendations: {
        framework: string;
        recommendation: string;
        priority: 'high' | 'medium' | 'low';
        expectedImprovement: string;
    }[];
}
export interface PerformanceAlert {
    alertId: string;
    timestamp: Date;
    type: 'performance_degradation' | 'cost_spike' | 'error_rate_increase' | 'resource_exhaustion' | 'anomaly_detected';
    severity: 'critical' | 'high' | 'medium' | 'low';
    framework?: string;
    metric: string;
    currentValue: number;
    threshold: number;
    description: string;
    recommendations: string[];
    autoResolution?: {
        possible: boolean;
        action: string;
        confidence: number;
    };
}
export interface PerformanceTrend {
    trendId: string;
    metric: string;
    framework?: string;
    timeRange: {
        start: Date;
        end: Date;
    };
    dataPoints: {
        timestamp: Date;
        value: number;
    }[];
    trendDirection: 'upward' | 'downward' | 'stable' | 'volatile';
    changeRate: number;
    forecast: {
        nextWeek: number;
        nextMonth: number;
        confidence: number;
    };
    seasonality?: {
        detected: boolean;
        pattern: 'daily' | 'weekly' | 'monthly';
        strength: number;
    };
}
/**
 * PerformanceAnalyticsEngine - Comprehensive performance analytics and monitoring
 *
 * Provides real-time performance tracking, historical analysis, and predictive
 * insights using MongoDB's official monitoring patterns and time series collections.
 */
export declare class PerformanceAnalyticsEngine {
    private tracingCollection;
    private memoryCollection;
    private metricsCollection;
    private alertThresholds;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, metricsCollection: MemoryCollection);
    /**
     * Generate comprehensive performance metrics using MongoDB time series aggregation
     */
    generatePerformanceMetrics(framework: "vercel-ai" | "mastra" | "openai-agents" | "langchain" | "all" | undefined, timeRange: {
        start: Date;
        end: Date;
    }): Promise<PerformanceMetrics>;
    /**
     * Compare performance across frameworks
     */
    compareFrameworkPerformance(timeRange: {
        start: Date;
        end: Date;
    }): Promise<FrameworkPerformanceComparison>;
    /**
     * Generate performance trend analysis
     */
    generatePerformanceTrend(metric: string, framework?: string, timeRange?: {
        start: Date;
        end: Date;
    }): Promise<PerformanceTrend>;
    /**
     * Store performance metrics in time series collection
     */
    storePerformanceMetrics(metrics: PerformanceMetrics): Promise<void>;
    private initializeAlertThresholds;
    private calculateTrend;
    private processSuccessRateData;
    private getResourceUtilization;
    private rankFrameworks;
    private generateFrameworkRecommendations;
    private getMetricAggregation;
    private calculateTrendDirection;
    private calculateChangeRate;
    private generateForecast;
    private detectSeasonality;
    private calculateVariance;
    private calculateLinearTrend;
}
//# sourceMappingURL=PerformanceAnalyticsEngine.d.ts.map