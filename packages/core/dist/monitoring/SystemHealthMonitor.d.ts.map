{"version": 3, "file": "SystemHealthMonitor.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/SystemHealthMonitor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;GAgBG;AAEH,OAAO,EAAE,WAAW,EAAgB,MAAM,SAAS,CAAC;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAEnE,MAAM,WAAW,iBAAiB;IAChC,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,SAAS,CAAC;IACzD,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,OAAO,EAAE;QACP,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAC9B,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,cAAc,CAAC,EAAE,IAAI,CAAC;QACtB,mBAAmB,CAAC,EAAE,MAAM,CAAC;KAC9B,CAAC;IACF,YAAY,CAAC,EAAE,iBAAiB,EAAE,CAAC;CACpC;AAED,MAAM,WAAW,YAAY;IAC3B,OAAO,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,CAAC;IAC9C,SAAS,EAAE,IAAI,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,QAAQ,EAAE;QACR,OAAO,EAAE,iBAAiB,CAAC;QAC3B,UAAU,EAAE;YACV,QAAQ,EAAE,iBAAiB,CAAC;YAC5B,MAAM,EAAE,iBAAiB,CAAC;YAC1B,YAAY,EAAE,iBAAiB,CAAC;YAChC,SAAS,EAAE,iBAAiB,CAAC;SAC9B,CAAC;QACF,QAAQ,EAAE;YACR,SAAS,EAAE,iBAAiB,CAAC;YAC7B,gBAAgB,EAAE,iBAAiB,CAAC;YACpC,YAAY,EAAE,iBAAiB,CAAC;SACjC,CAAC;QACF,MAAM,EAAE;YACN,GAAG,EAAE,iBAAiB,CAAC;YACvB,MAAM,EAAE,iBAAiB,CAAC;YAC1B,IAAI,EAAE,iBAAiB,CAAC;YACxB,OAAO,EAAE,iBAAiB,CAAC;SAC5B,CAAC;KACH,CAAC;IACF,MAAM,EAAE,WAAW,EAAE,CAAC;IACtB,eAAe,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,WAAW;IAC1B,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjD,OAAO,EAAE,MAAM,CAAC;IAChB,IAAI,EAAE,cAAc,GAAG,sBAAsB,GAAG,oBAAoB,GAAG,oBAAoB,CAAC;IAC5F,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC7B,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,YAAY,CAAC;IAC7C,UAAU,CAAC,EAAE,IAAI,CAAC;CACnB;AAED,MAAM,WAAW,mBAAmB;IAClC,aAAa,EAAE,MAAM,CAAC;IACtB,QAAQ,EAAE;QACR,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,EAAE,MAAM,CAAC;QACnB,QAAQ,EAAE,MAAM,CAAC;QACjB,MAAM,EAAE,MAAM,CAAC;KAChB,CAAC;IACF,UAAU,EAAE;QACV,YAAY,EAAE;YACZ,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,GAAG,EAAE;YACH,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,MAAM,EAAE;YACN,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;QACF,IAAI,EAAE;YACJ,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;SAClB,CAAC;KACH,CAAC;IACF,OAAO,EAAE;QACP,WAAW,EAAE,MAAM,CAAC;QACpB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,QAAQ,EAAE;QACR,OAAO,EAAE,OAAO,CAAC;QACjB,QAAQ,EAAE,MAAM,EAAE,CAAC;QACnB,KAAK,EAAE,MAAM,EAAE,CAAC;QAChB,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;CACH;AAED;;;;;GAKG;AACH,qBAAa,mBAAmB;IAC9B,OAAO,CAAC,MAAM,CAAsB;IACpC,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,gBAAgB,CAAmB;IAE3C,OAAO,CAAC,YAAY,CAAkB;IACtC,OAAO,CAAC,kBAAkB,CAAC,CAAiB;IAC5C,OAAO,CAAC,SAAS,CAAoB;IACrC,OAAO,CAAC,eAAe,CAAC,CAAe;IACvC,OAAO,CAAC,YAAY,CAAuC;IAC3D,OAAO,CAAC,oBAAoB,CAAkC;gBAG5D,MAAM,EAAE,mBAAmB,EAC3B,WAAW,EAAE,WAAW,EACxB,iBAAiB,EAAE,iBAAiB,EACpC,gBAAgB,EAAE,gBAAgB,EAClC,gBAAgB,EAAE,gBAAgB;IASpC;;OAEG;IACG,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;IAuBtC;;OAEG;IACG,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;IAUrC;;OAEG;IACG,gBAAgB,IAAI,OAAO,CAAC,YAAY,CAAC;IAQ/C;;OAEG;IACG,gBAAgB,CAAC,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;IAgBtF;;OAEG;YACW,kBAAkB;IA6DhC;;OAEG;YACW,kBAAkB;IA0GhC;;OAEG;YACW,qBAAqB;IAoBnC;;OAEG;YACW,oBAAoB;IAqDlC;;OAEG;YACW,2BAA2B;IAkBzC;;OAEG;YACW,0BAA0B;YAsB1B,0BAA0B;YAgC1B,iBAAiB;YAajB,2BAA2B;YAa3B,uBAAuB;YAavB,cAAc;YAoBd,iBAAiB;YAoBjB,eAAe;YAoBf,kBAAkB;IAahC,OAAO,CAAC,sBAAsB;IAQ9B,OAAO,CAAC,6BAA6B;YAuBvB,gBAAgB;YAiBhB,mBAAmB;YASnB,iBAAiB;YAuCjB,wBAAwB;CAUvC"}