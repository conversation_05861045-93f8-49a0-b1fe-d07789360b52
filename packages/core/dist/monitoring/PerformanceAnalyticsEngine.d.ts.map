{"version": 3, "file": "PerformanceAnalyticsEngine.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/PerformanceAnalyticsEngine.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAE,iBAAiB,EAAc,MAAM,kCAAkC,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAGnE,MAAM,WAAW,kBAAkB;IACjC,QAAQ,EAAE,MAAM,CAAC;IACjB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,KAAK,CAAC;IAC1E,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,YAAY,EAAE;QACZ,OAAO,EAAE,MAAM,CAAC;QAChB,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,KAAK,EAAE,WAAW,GAAG,QAAQ,GAAG,WAAW,CAAC;KAC7C,CAAC;IACF,UAAU,EAAE;QACV,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC;QACrB,mBAAmB,EAAE,MAAM,CAAC;QAC5B,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,YAAY,EAAE;QACZ,SAAS,EAAE,MAAM,CAAC;QAClB,gBAAgB,EAAE,MAAM,CAAC;QACzB,YAAY,EAAE,MAAM,CAAC;QACrB,aAAa,EAAE;YACb,UAAU,EAAE,MAAM,CAAC;YACnB,cAAc,EAAE,MAAM,CAAC;YACvB,YAAY,EAAE,MAAM,CAAC;YACrB,UAAU,EAAE,MAAM,CAAC;SACpB,CAAC;QACF,KAAK,EAAE,YAAY,GAAG,QAAQ,GAAG,YAAY,CAAC;KAC/C,CAAC;IACF,YAAY,EAAE;QACZ,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpC,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACpC,cAAc,EAAE;YACd,IAAI,EAAE,MAAM,CAAC;YACb,KAAK,EAAE,MAAM,CAAC;YACd,UAAU,EAAE,MAAM,CAAC;SACpB,EAAE,CAAC;KACL,CAAC;IACF,UAAU,EAAE;QACV,mBAAmB,EAAE,MAAM,CAAC;QAC5B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,iBAAiB,EAAE,MAAM,CAAC;QAC1B,cAAc,EAAE,MAAM,CAAC;QACvB,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,mBAAmB,EAAE;QACnB,QAAQ,EAAE,MAAM,CAAC;QACjB,WAAW,EAAE,MAAM,CAAC;QACpB,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,EAAE,MAAM,CAAC;QAClB,kBAAkB,EAAE,MAAM,CAAC;KAC5B,CAAC;CACH;AAED,MAAM,WAAW,8BAA8B;IAC7C,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,UAAU,EAAE;QACV,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE;YACP,mBAAmB,EAAE,MAAM,CAAC;YAC5B,WAAW,EAAE,MAAM,CAAC;YACpB,gBAAgB,EAAE,MAAM,CAAC;YACzB,eAAe,EAAE,MAAM,CAAC;YACxB,SAAS,EAAE,MAAM,CAAC;YAClB,UAAU,EAAE,MAAM,CAAC;SACpB,CAAC;QACF,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,EAAE,CAAC;QACpB,UAAU,EAAE,MAAM,EAAE,CAAC;KACtB,EAAE,CAAC;IACJ,eAAe,EAAE;QACf,SAAS,EAAE,MAAM,CAAC;QAClB,cAAc,EAAE,MAAM,CAAC;QACvB,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QACpC,mBAAmB,EAAE,MAAM,CAAC;KAC7B,EAAE,CAAC;CACL;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,yBAAyB,GAAG,YAAY,GAAG,qBAAqB,GAAG,qBAAqB,GAAG,kBAAkB,CAAC;IACpH,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjD,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,cAAc,CAAC,EAAE;QACf,QAAQ,EAAE,OAAO,CAAC;QAClB,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,UAAU,EAAE;QACV,SAAS,EAAE,IAAI,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;KACf,EAAE,CAAC;IACJ,cAAc,EAAE,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC;IAC9D,UAAU,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE;QACR,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,WAAW,CAAC,EAAE;QACZ,QAAQ,EAAE,OAAO,CAAC;QAClB,OAAO,EAAE,OAAO,GAAG,QAAQ,GAAG,SAAS,CAAC;QACxC,QAAQ,EAAE,MAAM,CAAC;KAClB,CAAC;CACH;AAED;;;;;GAKG;AACH,qBAAa,0BAA0B;IACrC,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,iBAAiB,CAAmB;IAC5C,OAAO,CAAC,eAAe,CAAwF;gBAG7G,iBAAiB,EAAE,iBAAiB,EACpC,gBAAgB,EAAE,gBAAgB,EAClC,iBAAiB,EAAE,gBAAgB;IAQrC;;OAEG;IACG,0BAA0B,CAC9B,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,KAAK,YAAQ,EACjF,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GACpC,OAAO,CAAC,kBAAkB,CAAC;IAsO9B;;OAEG;IACG,2BAA2B,CAC/B,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GACpC,OAAO,CAAC,8BAA8B,CAAC;IAmC1C;;OAEG;IACG,wBAAwB,CAC5B,MAAM,EAAE,MAAM,EACd,SAAS,CAAC,EAAE,MAAM,EAClB,SAAS,GAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAGlC,GACA,OAAO,CAAC,gBAAgB,CAAC;IAiD5B;;OAEG;IACG,uBAAuB,CAAC,OAAO,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC;IA0BzE,OAAO,CAAC,yBAAyB;IASjC,OAAO,CAAC,cAAc;IAgBtB,OAAO,CAAC,sBAAsB;YA2ChB,sBAAsB;IAWpC,OAAO,CAAC,cAAc;IA6BtB,OAAO,CAAC,gCAAgC;IA0BxC,OAAO,CAAC,oBAAoB;IAe5B,OAAO,CAAC,uBAAuB;IAa/B,OAAO,CAAC,mBAAmB;IAW3B,OAAO,CAAC,gBAAgB;IAaxB,OAAO,CAAC,iBAAiB;IAkCzB,OAAO,CAAC,iBAAiB;IAMzB,OAAO,CAAC,oBAAoB;CAU7B"}