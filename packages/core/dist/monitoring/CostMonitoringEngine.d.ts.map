{"version": 3, "file": "CostMonitoringEngine.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/CostMonitoringEngine.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAE,iBAAiB,EAAc,MAAM,kCAAkC,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAY,EAAE,EAAE,MAAM,SAAS,CAAC;AAEvC,MAAM,WAAW,SAAS;IACxB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC7E,SAAS,EAAE;QACT,IAAI,EAAE,iBAAiB,GAAG,WAAW,GAAG,eAAe,GAAG,mBAAmB,GAAG,mBAAmB,CAAC;QACpG,WAAW,EAAE,MAAM,CAAC;QACpB,OAAO,CAAC,EAAE,MAAM,CAAC;KAClB,CAAC;IACF,KAAK,EAAE;QACL,KAAK,EAAE,MAAM,CAAC;QACd,SAAS,EAAE,MAAM,CAAC;QAClB,OAAO,EAAE,MAAM,CAAC;QAChB,YAAY,EAAE,MAAM,CAAC;QACrB,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;IACF,KAAK,EAAE;QACL,WAAW,EAAE,MAAM,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC;QACrB,WAAW,EAAE,MAAM,CAAC;QACpB,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAC7B,kBAAkB,EAAE,MAAM,CAAC;QAC3B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,OAAO,EAAE;QACP,kBAAkB,EAAE,MAAM,CAAC;QAC3B,sBAAsB,EAAE,MAAM,CAAC;QAC/B,wBAAwB,EAAE,MAAM,CAAC;QACjC,yBAAyB,EAAE,MAAM,CAAC;KACnC,CAAC;IACF,QAAQ,EAAE;QACR,SAAS,EAAE,MAAM,CAAC;QAClB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,IAAI,EAAE,MAAM,EAAE,CAAC;KAChB,CAAC;CACH;AAED,MAAM,WAAW,UAAU;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,KAAK,EAAE;QACL,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;QACtB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;QACtB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;QACjB,SAAS,EAAE,OAAO,GAAG,QAAQ,GAAG,SAAS,GAAG,QAAQ,CAAC;KACtD,CAAC;IACF,MAAM,EAAE;QACN,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,UAAU,CAAC,EAAE,MAAM,CAAC;KACrB,CAAC;IACF,MAAM,EAAE;QACN,UAAU,EAAE,MAAM,EAAE,CAAC;QACrB,aAAa,EAAE;YACb,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;YACjB,KAAK,CAAC,EAAE,MAAM,CAAC;YACf,OAAO,CAAC,EAAE,MAAM,CAAC;SAClB,CAAC;KACH,CAAC;IACF,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,MAAM,CAAC;IACxB,MAAM,EAAE,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAC;IACzC,SAAS,EAAE,IAAI,CAAC;IAChB,WAAW,EAAE,IAAI,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,cAAc,EAAE,MAAM,CAAC;IACvB,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,iBAAiB,GAAG,kBAAkB,GAAG,SAAS,GAAG,kBAAkB,GAAG,mBAAmB,CAAC;IACpG,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,CAAC;IACtB,gBAAgB,EAAE,MAAM,CAAC;IACzB,iBAAiB,EAAE,MAAM,CAAC;IAC1B,cAAc,EAAE;QACd,KAAK,EAAE,MAAM,CAAC;QACd,WAAW,EAAE,MAAM,CAAC;QACpB,MAAM,EAAE,MAAM,CAAC;QACf,cAAc,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,CAAC;QACzD,MAAM,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;QAClC,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,MAAM,EAAE;QACN,aAAa,EAAE,MAAM,CAAC;QACtB,iBAAiB,EAAE,UAAU,GAAG,SAAS,GAAG,UAAU,CAAC;QACvD,SAAS,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAC;KACtC,CAAC;IACF,MAAM,EAAE,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,SAAS,CAAC;CAC5D;AAED,MAAM,WAAW,aAAa;IAC5B,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE;QACb,KAAK,EAAE,MAAM,CAAC;QACd,SAAS,EAAE,MAAM,CAAC;QAClB,OAAO,EAAE,MAAM,CAAC;QAChB,YAAY,EAAE,MAAM,CAAC;QACrB,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;QAChB,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IACF,cAAc,EAAE;QACd,SAAS,EAAE,MAAM,CAAC;QAClB,IAAI,EAAE,MAAM,CAAC;QACb,UAAU,EAAE,MAAM,CAAC;QACnB,KAAK,EAAE,YAAY,GAAG,QAAQ,GAAG,YAAY,CAAC;QAC9C,UAAU,EAAE,MAAM,CAAC;KACpB,EAAE,CAAC;IACJ,cAAc,EAAE;QACd,SAAS,EAAE,MAAM,CAAC;QAClB,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;QACd,WAAW,EAAE,MAAM,CAAC;QACpB,KAAK,EAAE,YAAY,GAAG,QAAQ,GAAG,YAAY,CAAC;KAC/C,EAAE,CAAC;IACJ,eAAe,EAAE;QACf,WAAW,EAAE,MAAM,CAAC;QACpB,YAAY,EAAE,MAAM,CAAC;QACrB,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,MAAM,CAAC;KACtB,CAAC;IACF,MAAM,EAAE;QACN,UAAU,EAAE;YAAE,IAAI,EAAE,IAAI,CAAC;YAAC,IAAI,EAAE,MAAM,CAAA;SAAE,EAAE,CAAC;QAC3C,WAAW,EAAE;YAAE,IAAI,EAAE,MAAM,CAAC;YAAC,IAAI,EAAE,MAAM,CAAA;SAAE,EAAE,CAAC;QAC9C,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;IACF,YAAY,EAAE;QACZ,WAAW,EAAE,MAAM,CAAC;QACpB,WAAW,EAAE,MAAM,CAAC;QACpB,eAAe,EAAE,MAAM,CAAC;QACxB,QAAQ,EAAE,MAAM,CAAC;QACjB,gBAAgB,EAAE,IAAI,CAAC;KACxB,CAAC;IACF,aAAa,EAAE,gBAAgB,EAAE,CAAC;CACnC;AAED,MAAM,WAAW,SAAS;IACxB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,kBAAkB,GAAG,YAAY,GAAG,iBAAiB,GAAG,0BAA0B,CAAC;IACzF,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjD,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,MAAM,EAAE,QAAQ,GAAG,cAAc,GAAG,UAAU,CAAC;CAChD;AAED;;;;;GAKG;AACH,qBAAa,oBAAoB;IAC/B,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,cAAc,CAAmB;IACzC,OAAO,CAAC,OAAO,CAAsC;IACrD,OAAO,CAAC,YAAY,CAAqC;IACzD,OAAO,CAAC,YAAY,CAAkB;IACtC,OAAO,CAAC,EAAE,CAAK;gBAGb,iBAAiB,EAAE,iBAAiB,EACpC,gBAAgB,EAAE,gBAAgB,EAClC,cAAc,EAAE,gBAAgB,EAChC,EAAE,EAAE,EAAE;IASR;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IA6B1C;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC,IAAI,CAAC;IAKzC;;OAEG;IACG,SAAS,CACb,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,SAAS,CAAC,WAAW,CAAC,EACjC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,EACzB,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,EACzB,OAAO,EAAE,SAAS,CAAC,SAAS,CAAC,EAC7B,QAAQ,GAAE,SAAS,CAAC,UAAU,CAAsC,GACnE,OAAO,CAAC,MAAM,CAAC;IA6ClB;;OAEG;IACG,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,GAAG,cAAc,GAAG,iBAAiB,GAAG,WAAW,GAAG,aAAa,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IAkC5I;;OAEG;IACG,qBAAqB,CAAC,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GAAG,OAAO,CAAC,aAAa,CAAC;IAiQ1F;;OAEG;IACG,mCAAmC,CAAC,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;IA4F7G;;OAEG;IACG,eAAe,CACnB,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,EACvB,QAAQ,EAAE,SAAS,CAAC,UAAU,CAAC,EAC/B,KAAK,EAAE,MAAM,EACb,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,EACnB,SAAS,CAAC,EAAE,MAAM,EAClB,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,MAAM,CAAC;YAyCJ,gBAAgB;YA2ChB,iBAAiB;YAmCjB,8BAA8B;IA6B5C,OAAO,CAAC,wBAAwB;IAwBhC,OAAO,CAAC,oBAAoB;IAwB5B,OAAO,CAAC,wBAAwB;IAOhC,OAAO,CAAC,2BAA2B;YA+BrB,cAAc;IAsB5B,OAAO,CAAC,uBAAuB;YAYjB,qBAAqB;YAuBrB,4BAA4B;YAiB5B,uBAAuB;IA8BrC,OAAO,CAAC,wBAAwB;CA4CjC"}