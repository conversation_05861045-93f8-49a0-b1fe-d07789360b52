/**
 * @file SystemHealthMonitor - Comprehensive system health monitoring
 *
 * This monitor tracks MongoDB connection health, framework availability, embedding
 * service status, and overall system performance with automated health checks,
 * status reporting, and proactive alerting using official MongoDB monitoring patterns.
 *
 * Features:
 * - MongoDB connection health monitoring with replica set status
 * - Framework availability and response time tracking
 * - Embedding service health checks and failover detection
 * - System resource monitoring (CPU, memory, disk)
 * - Automated health checks with configurable intervals
 * - Health status reporting and alerting
 * - Service dependency mapping and cascade failure detection
 * - Performance degradation detection and auto-recovery
 */
import { MongoClient } from 'mongodb';
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface HealthCheckResult {
    service: string;
    status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
    responseTime: number;
    timestamp: Date;
    details: {
        message: string;
        metrics?: Record<string, any>;
        error?: string;
        lastSuccessful?: Date;
        consecutiveFailures?: number;
    };
    dependencies?: HealthCheckResult[];
}
export interface SystemHealth {
    overall: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: Date;
    uptime: number;
    services: {
        mongodb: HealthCheckResult;
        frameworks: {
            vercelAI: HealthCheckResult;
            mastra: HealthCheckResult;
            openaiAgents: HealthCheckResult;
            langchain: HealthCheckResult;
        };
        external: {
            openaiAPI: HealthCheckResult;
            embeddingService: HealthCheckResult;
            vectorSearch: HealthCheckResult;
        };
        system: {
            cpu: HealthCheckResult;
            memory: HealthCheckResult;
            disk: HealthCheckResult;
            network: HealthCheckResult;
        };
    };
    alerts: HealthAlert[];
    recommendations: string[];
}
export interface HealthAlert {
    alertId: string;
    timestamp: Date;
    severity: 'critical' | 'high' | 'medium' | 'low';
    service: string;
    type: 'service_down' | 'performance_degraded' | 'resource_exhausted' | 'dependency_failure';
    message: string;
    details: Record<string, any>;
    status: 'active' | 'resolved' | 'suppressed';
    resolvedAt?: Date;
}
export interface HealthConfiguration {
    checkInterval: number;
    timeouts: {
        mongodb: number;
        frameworks: number;
        external: number;
        system: number;
    };
    thresholds: {
        responseTime: {
            warning: number;
            critical: number;
        };
        cpu: {
            warning: number;
            critical: number;
        };
        memory: {
            warning: number;
            critical: number;
        };
        disk: {
            warning: number;
            critical: number;
        };
    };
    retries: {
        maxAttempts: number;
        backoffMultiplier: number;
        initialDelay: number;
    };
    alerting: {
        enabled: boolean;
        webhooks: string[];
        email: string[];
        suppressionWindow: number;
    };
}
/**
 * SystemHealthMonitor - Comprehensive system health monitoring
 *
 * Monitors all system components including MongoDB, frameworks, external services,
 * and system resources with automated health checks and alerting.
 */
export declare class SystemHealthMonitor {
    private config;
    private mongoClient;
    private tracingCollection;
    private memoryCollection;
    private healthCollection;
    private isMonitoring;
    private monitoringInterval?;
    private startTime;
    private lastHealthCheck?;
    private activeAlerts;
    private serviceFailureCounts;
    constructor(config: HealthConfiguration, mongoClient: MongoClient, tracingCollection: TracingCollection, memoryCollection: MemoryCollection, healthCollection: MemoryCollection);
    /**
     * Start system health monitoring
     */
    startMonitoring(): Promise<void>;
    /**
     * Stop system health monitoring
     */
    stopMonitoring(): Promise<void>;
    /**
     * Get current system health status
     */
    getCurrentHealth(): Promise<SystemHealth>;
    /**
     * Get health history for a specific time range
     */
    getHealthHistory(timeRange: {
        start: Date;
        end: Date;
    }): Promise<SystemHealth[]>;
    /**
     * Perform comprehensive health check
     */
    private performHealthCheck;
    /**
     * Check MongoDB health using official MongoDB monitoring patterns
     */
    private checkMongoDBHealth;
    /**
     * Check framework health and availability
     */
    private checkFrameworksHealth;
    /**
     * Check individual framework health
     */
    private checkFrameworkHealth;
    /**
     * Check external services health
     */
    private checkExternalServicesHealth;
    /**
     * Check system resources health
     */
    private checkSystemResourcesHealth;
    private getRecentFrameworkActivity;
    private checkOpenAIHealth;
    private checkEmbeddingServiceHealth;
    private checkVectorSearchHealth;
    private checkCPUHealth;
    private checkMemoryHealth;
    private checkDiskHealth;
    private checkNetworkHealth;
    private determineOverallHealth;
    private generateHealthRecommendations;
    private storeHealthCheck;
    private processHealthAlerts;
    private createHealthAlert;
    private handleHealthCheckFailure;
}
//# sourceMappingURL=SystemHealthMonitor.d.ts.map