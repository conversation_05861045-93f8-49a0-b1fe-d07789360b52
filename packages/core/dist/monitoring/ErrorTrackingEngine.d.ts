/**
 * @file ErrorTrackingEngine - Real-time error detection and alerting system
 *
 * This engine implements comprehensive error tracking with real-time detection,
 * categorization, alerting, and automated recovery suggestions using MongoDB's
 * official structured logging patterns and aggregation pipelines.
 *
 * Features:
 * - Real-time error detection using MongoDB Change Streams
 * - Error categorization and pattern analysis
 * - Automated alerting with webhook support
 * - Recovery suggestions based on error patterns
 * - Integration with popular monitoring services
 * - Error trend analysis and forecasting
 * - Custom error rules and thresholds
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface ErrorEvent {
    errorId: string;
    timestamp: Date;
    framework: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain' | 'system';
    severity: 'critical' | 'high' | 'medium' | 'low';
    category: 'connection' | 'authentication' | 'validation' | 'timeout' | 'rate_limit' | 'model_error' | 'system_error' | 'unknown';
    errorType: string;
    errorMessage: string;
    errorCode?: string;
    stackTrace?: string;
    context: {
        traceId?: string;
        sessionId?: string;
        userId?: string;
        operation?: string;
        input?: string;
        framework?: string;
        modelUsed?: string;
    };
    metadata: {
        component: string;
        logLevel: 'I' | 'W' | 'E' | 'F' | 'D';
        source: string;
        tags: string[];
        additionalData?: Record<string, any>;
    };
    resolution?: {
        suggested: boolean;
        actions: string[];
        automatable: boolean;
        confidence: number;
    };
}
export interface ErrorPattern {
    patternId: string;
    name: string;
    description: string;
    errorTypes: string[];
    frequency: number;
    firstSeen: Date;
    lastSeen: Date;
    affectedFrameworks: string[];
    commonContext: Record<string, any>;
    suggestedFixes: {
        action: string;
        description: string;
        priority: 'immediate' | 'high' | 'medium' | 'low';
        automatable: boolean;
    }[];
    trend: 'increasing' | 'stable' | 'decreasing';
}
export interface ErrorAlert {
    alertId: string;
    timestamp: Date;
    type: 'threshold_exceeded' | 'new_error_pattern' | 'critical_error' | 'system_failure' | 'recovery_needed';
    severity: 'critical' | 'high' | 'medium' | 'low';
    title: string;
    description: string;
    errorEvents: string[];
    affectedSystems: string[];
    metrics: {
        errorCount: number;
        errorRate: number;
        timeWindow: number;
        threshold: number;
    };
    notifications: {
        email?: string[];
        slack?: string;
        webhook?: string;
        sms?: string[];
    };
    status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
    acknowledgedBy?: string;
    acknowledgedAt?: Date;
    resolvedAt?: Date;
}
export interface ErrorRecoveryAction {
    actionId: string;
    errorPattern: string;
    action: 'retry' | 'fallback' | 'circuit_breaker' | 'rate_limit' | 'escalate' | 'restart' | 'custom';
    description: string;
    parameters: Record<string, any>;
    conditions: {
        errorCount?: number;
        timeWindow?: number;
        errorRate?: number;
        consecutiveFailures?: number;
    };
    automatable: boolean;
    successRate: number;
    lastExecuted?: Date;
    executionCount: number;
}
export interface ErrorAnalytics {
    timeRange: {
        start: Date;
        end: Date;
    };
    totalErrors: number;
    errorRate: number;
    topErrorTypes: {
        errorType: string;
        count: number;
        percentage: number;
        trend: 'increasing' | 'stable' | 'decreasing';
    }[];
    frameworkBreakdown: {
        framework: string;
        errorCount: number;
        errorRate: number;
        topErrors: string[];
    }[];
    severityDistribution: {
        critical: number;
        high: number;
        medium: number;
        low: number;
    };
    patterns: ErrorPattern[];
    mttr: number;
    mtbf: number;
    availability: number;
}
/**
 * ErrorTrackingEngine - Comprehensive error tracking and alerting system
 *
 * Provides real-time error detection, pattern analysis, and automated recovery
 * using MongoDB's official structured logging and aggregation patterns.
 */
export declare class ErrorTrackingEngine {
    private tracingCollection;
    private memoryCollection;
    private errorCollection;
    private alertThresholds;
    private recoveryActions;
    private activeAlerts;
    private isMonitoring;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, errorCollection: MemoryCollection);
    /**
     * Start real-time error monitoring using MongoDB Change Streams
     */
    startErrorMonitoring(): Promise<void>;
    /**
     * Stop error monitoring
     */
    stopErrorMonitoring(): Promise<void>;
    /**
     * Track error event with MongoDB structured logging patterns
     */
    trackError(framework: string, errorType: string, errorMessage: string, context?: any, severity?: ErrorEvent['severity']): Promise<string>;
    /**
     * Analyze error patterns using MongoDB aggregation pipelines
     */
    analyzeErrorPatterns(timeRange: {
        start: Date;
        end: Date;
    }): Promise<ErrorPattern[]>;
    /**
     * Generate error analytics report
     */
    generateErrorAnalytics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<ErrorAnalytics>;
    /**
     * Create error alert with webhook notifications
     */
    createAlert(type: ErrorAlert['type'], severity: ErrorAlert['severity'], title: string, description: string, errorEvents: string[], notifications?: ErrorAlert['notifications']): Promise<string>;
    private processErrorEvent;
    private categorizeError;
    private getComponentFromFramework;
    private severityToLogLevel;
    private generateResolutionSuggestions;
    private getResolutionSuggestions;
    private isAutomatable;
    private calculateResolutionConfidence;
    private checkAlertConditions;
    private countRecentErrors;
    private updateErrorPatterns;
    private processPatternResults;
    private calculateMTTR;
    private calculateMTBF;
    private calculateAvailability;
    private getAffectedSystems;
    private calculateAlertMetrics;
    private sendAlertNotifications;
    private sendWebhookNotification;
    private mapErrorSeverity;
    private initializeDefaultThresholds;
    private initializeDefaultRecoveryActions;
}
//# sourceMappingURL=ErrorTrackingEngine.d.ts.map