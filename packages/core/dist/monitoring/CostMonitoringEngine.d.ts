/**
 * @file CostMonitoringEngine - Comprehensive cost tracking and optimization system
 *
 * This engine tracks API costs, token usage, embedding costs, and MongoDB operations
 * with budget alerts, cost optimization suggestions, and detailed cost breakdown by
 * framework and operation using MongoDB's official cost monitoring patterns.
 *
 * Features:
 * - Real-time cost tracking using MongoDB time series collections
 * - Budget alerts and cost threshold monitoring
 * - Cost optimization suggestions based on usage patterns
 * - Framework-specific cost analysis and comparison
 * - Token usage efficiency tracking and optimization
 * - MongoDB Atlas cost integration and monitoring
 * - Predictive cost forecasting and budget planning
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface CostEvent {
    costId: string;
    timestamp: Date;
    framework: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain' | 'system';
    operation: {
        type: 'chat_completion' | 'embedding' | 'vector_search' | 'mongodb_operation' | 'context_retrieval';
        operationId: string;
        traceId?: string;
    };
    costs: {
        model: number;
        embedding: number;
        mongodb: number;
        vectorSearch: number;
        storage: number;
        compute: number;
        network: number;
        total: number;
    };
    usage: {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
        embeddingDimensions?: number;
        documentsProcessed: number;
        vectorSearchQueries: number;
        mongodbOperations: number;
    };
    pricing: {
        modelPricePerToken: number;
        embeddingPricePerToken: number;
        mongodbPricePerOperation: number;
        vectorSearchPricePerQuery: number;
    };
    metadata: {
        modelUsed: string;
        region?: string;
        tier?: string;
        userId?: string;
        sessionId?: string;
        tags: string[];
    };
}
export interface CostBudget {
    budgetId: string;
    name: string;
    description: string;
    scope: {
        frameworks?: string[];
        operations?: string[];
        users?: string[];
        timeRange: 'daily' | 'weekly' | 'monthly' | 'yearly';
    };
    limits: {
        totalCost: number;
        modelCost?: number;
        embeddingCost?: number;
        mongodbCost?: number;
        tokenUsage?: number;
    };
    alerts: {
        thresholds: number[];
        notifications: {
            email?: string[];
            slack?: string;
            webhook?: string;
        };
    };
    currentSpend: number;
    remainingBudget: number;
    status: 'active' | 'exceeded' | 'paused';
    createdAt: Date;
    lastUpdated: Date;
}
export interface CostOptimization {
    optimizationId: string;
    timestamp: Date;
    type: 'model_selection' | 'token_efficiency' | 'caching' | 'batch_processing' | 'tier_optimization';
    framework: string;
    currentCost: number;
    optimizedCost: number;
    potentialSavings: number;
    savingsPercentage: number;
    recommendation: {
        title: string;
        description: string;
        action: string;
        implementation: 'automatic' | 'manual' | 'configuration';
        effort: 'low' | 'medium' | 'high';
        confidence: number;
    };
    impact: {
        costReduction: number;
        performanceImpact: 'positive' | 'neutral' | 'negative';
        riskLevel: 'low' | 'medium' | 'high';
    };
    status: 'pending' | 'implemented' | 'rejected' | 'expired';
}
export interface CostAnalytics {
    timeRange: {
        start: Date;
        end: Date;
    };
    totalCost: number;
    costBreakdown: {
        model: number;
        embedding: number;
        mongodb: number;
        vectorSearch: number;
        storage: number;
        compute: number;
        network: number;
    };
    frameworkCosts: {
        framework: string;
        cost: number;
        percentage: number;
        trend: 'increasing' | 'stable' | 'decreasing';
        efficiency: number;
    }[];
    operationCosts: {
        operation: string;
        cost: number;
        count: number;
        averageCost: number;
        trend: 'increasing' | 'stable' | 'decreasing';
    }[];
    tokenEfficiency: {
        totalTokens: number;
        costPerToken: number;
        efficiency: number;
        wastedTokens: number;
    };
    trends: {
        dailyCosts: {
            date: Date;
            cost: number;
        }[];
        hourlyCosts: {
            hour: number;
            cost: number;
        }[];
        costGrowthRate: number;
    };
    budgetStatus: {
        totalBudget: number;
        spentAmount: number;
        remainingBudget: number;
        burnRate: number;
        projectedEndDate: Date;
    };
    optimizations: CostOptimization[];
}
export interface CostAlert {
    alertId: string;
    timestamp: Date;
    type: 'budget_threshold' | 'cost_spike' | 'efficiency_drop' | 'optimization_opportunity';
    severity: 'critical' | 'high' | 'medium' | 'low';
    title: string;
    description: string;
    budgetId?: string;
    currentCost: number;
    threshold?: number;
    recommendations: string[];
    status: 'active' | 'acknowledged' | 'resolved';
}
/**
 * CostMonitoringEngine - Comprehensive cost tracking and optimization system
 *
 * Provides real-time cost monitoring, budget management, and optimization
 * recommendations using MongoDB's official cost monitoring patterns.
 */
export declare class CostMonitoringEngine {
    private tracingCollection;
    private memoryCollection;
    private costCollection;
    private budgets;
    private activeAlerts;
    private isMonitoring;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, costCollection: MemoryCollection);
    /**
     * Start real-time cost monitoring using MongoDB Change Streams
     */
    startCostMonitoring(): Promise<void>;
    /**
     * Stop cost monitoring
     */
    stopCostMonitoring(): Promise<void>;
    /**
     * Track cost event with MongoDB time series patterns
     */
    trackCost(framework: string, operation: CostEvent['operation'], costs: CostEvent['costs'], usage: CostEvent['usage'], pricing: CostEvent['pricing'], metadata?: CostEvent['metadata']): Promise<string>;
    /**
     * Create cost budget with MongoDB validation
     */
    createBudget(budget: Omit<CostBudget, 'budgetId' | 'currentSpend' | 'remainingBudget' | 'createdAt' | 'lastUpdated'>): Promise<string>;
    /**
     * Generate comprehensive cost analytics using MongoDB aggregation
     */
    generateCostAnalytics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<CostAnalytics>;
    /**
     * Generate cost optimization recommendations
     */
    generateOptimizationRecommendations(timeRange: {
        start: Date;
        end: Date;
    }): Promise<CostOptimization[]>;
    /**
     * Create cost alert
     */
    createCostAlert(type: CostAlert['type'], severity: CostAlert['severity'], title: string, description: string, currentCost: number, threshold?: number, budgetId?: string): Promise<string>;
    private processCostEvent;
    private checkBudgetAlerts;
    private checkOptimizationOpportunities;
    private isCostEventInBudgetScope;
    private getBudgetPeriodStart;
    private getSeverityFromThreshold;
    private getCostAlertRecommendations;
    private getRecentCosts;
    private calculateCostGrowthRate;
    private calculateBudgetStatus;
    private getOptimizationOpportunities;
    private detectRepetitiveQueries;
    private initializeDefaultBudgets;
}
//# sourceMappingURL=CostMonitoringEngine.d.ts.map