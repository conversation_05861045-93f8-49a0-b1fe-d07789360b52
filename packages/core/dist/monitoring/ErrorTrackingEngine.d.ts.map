{"version": 3, "file": "ErrorTrackingEngine.d.ts", "sourceRoot": "", "sources": ["../../src/monitoring/ErrorTrackingEngine.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAE,iBAAiB,EAAc,MAAM,kCAAkC,CAAC;AACjF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAGnE,MAAM,WAAW,UAAU;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,QAAQ,CAAC;IAC7E,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjD,QAAQ,EAAE,YAAY,GAAG,gBAAgB,GAAG,YAAY,GAAG,SAAS,GAAG,YAAY,GAAG,aAAa,GAAG,cAAc,GAAG,SAAS,CAAC;IACjI,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,OAAO,EAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,QAAQ,EAAE;QACR,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;QACtC,MAAM,EAAE,MAAM,CAAC;QACf,IAAI,EAAE,MAAM,EAAE,CAAC;QACf,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACtC,CAAC;IACF,UAAU,CAAC,EAAE;QACX,SAAS,EAAE,OAAO,CAAC;QACnB,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB,WAAW,EAAE,OAAO,CAAC;QACrB,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,YAAY;IAC3B,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,EAAE,IAAI,CAAC;IACf,kBAAkB,EAAE,MAAM,EAAE,CAAC;IAC7B,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACnC,cAAc,EAAE;QACd,MAAM,EAAE,MAAM,CAAC;QACf,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QAClD,WAAW,EAAE,OAAO,CAAC;KACtB,EAAE,CAAC;IACJ,KAAK,EAAE,YAAY,GAAG,QAAQ,GAAG,YAAY,CAAC;CAC/C;AAED,MAAM,WAAW,UAAU;IACzB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,IAAI,CAAC;IAChB,IAAI,EAAE,oBAAoB,GAAG,mBAAmB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,iBAAiB,CAAC;IAC3G,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjD,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,eAAe,EAAE,MAAM,EAAE,CAAC;IAC1B,OAAO,EAAE;QACP,UAAU,EAAE,MAAM,CAAC;QACnB,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;QACnB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;IACF,aAAa,EAAE;QACb,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;QACjB,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC;KAChB,CAAC;IACF,MAAM,EAAE,QAAQ,GAAG,cAAc,GAAG,UAAU,GAAG,YAAY,CAAC;IAC9D,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,cAAc,CAAC,EAAE,IAAI,CAAC;IACtB,UAAU,CAAC,EAAE,IAAI,CAAC;CACnB;AAED,MAAM,WAAW,mBAAmB;IAClC,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE,MAAM,CAAC;IACrB,MAAM,EAAE,OAAO,GAAG,UAAU,GAAG,iBAAiB,GAAG,YAAY,GAAG,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC;IACpG,WAAW,EAAE,MAAM,CAAC;IACpB,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAChC,UAAU,EAAE;QACV,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,mBAAmB,CAAC,EAAE,MAAM,CAAC;KAC9B,CAAC;IACF,WAAW,EAAE,OAAO,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,CAAC,EAAE,IAAI,CAAC;IACpB,cAAc,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,cAAc;IAC7B,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,aAAa,EAAE;QACb,SAAS,EAAE,MAAM,CAAC;QAClB,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;QACnB,KAAK,EAAE,YAAY,GAAG,QAAQ,GAAG,YAAY,CAAC;KAC/C,EAAE,CAAC;IACJ,kBAAkB,EAAE;QAClB,SAAS,EAAE,MAAM,CAAC;QAClB,UAAU,EAAE,MAAM,CAAC;QACnB,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,EAAE,MAAM,EAAE,CAAC;KACrB,EAAE,CAAC;IACJ,oBAAoB,EAAE;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,MAAM,EAAE,MAAM,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;IACF,QAAQ,EAAE,YAAY,EAAE,CAAC;IACzB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;;;;GAKG;AACH,qBAAa,mBAAmB;IAC9B,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,eAAe,CAAmB;IAC1C,OAAO,CAAC,eAAe,CAAqE;IAC5F,OAAO,CAAC,eAAe,CAA+C;IACtE,OAAO,CAAC,YAAY,CAAsC;IAC1D,OAAO,CAAC,YAAY,CAAkB;gBAGpC,iBAAiB,EAAE,iBAAiB,EACpC,gBAAgB,EAAE,gBAAgB,EAClC,eAAe,EAAE,gBAAgB;IASnC;;OAEG;IACG,oBAAoB,IAAI,OAAO,CAAC,IAAI,CAAC;IA8B3C;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IAK1C;;OAEG;IACG,UAAU,CACd,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,MAAM,EACjB,YAAY,EAAE,MAAM,EACpB,OAAO,GAAE,GAAQ,EACjB,QAAQ,GAAE,UAAU,CAAC,UAAU,CAAY,GAC1C,OAAO,CAAC,MAAM,CAAC;IAsDlB;;OAEG;IACG,oBAAoB,CAAC,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;IAoF1F;;OAEG;IACG,sBAAsB,CAAC,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GAAG,OAAO,CAAC,cAAc,CAAC;IA0I5F;;OAEG;IACG,WAAW,CACf,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,EACxB,QAAQ,EAAE,UAAU,CAAC,UAAU,CAAC,EAChC,KAAK,EAAE,MAAM,EACb,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,EAAE,EACrB,aAAa,CAAC,EAAE,UAAU,CAAC,eAAe,CAAC,GAC1C,OAAO,CAAC,MAAM,CAAC;YA2CJ,iBAAiB;IAmB/B,OAAO,CAAC,eAAe;IAcvB,OAAO,CAAC,yBAAyB;IAajC,OAAO,CAAC,kBAAkB;YAYZ,6BAA6B;IAY3C,OAAO,CAAC,wBAAwB;IA2ChC,OAAO,CAAC,aAAa;IAKrB,OAAO,CAAC,6BAA6B;YAWvB,oBAAoB;YAyBpB,iBAAiB;YAmBjB,mBAAmB;IAKjC,OAAO,CAAC,qBAAqB;YAiBf,aAAa;YAKb,aAAa;YAKb,qBAAqB;IAKnC,OAAO,CAAC,kBAAkB;YAKZ,qBAAqB;YAUrB,sBAAsB;YAStB,uBAAuB;IASrC,OAAO,CAAC,gBAAgB;IAWxB,OAAO,CAAC,2BAA2B;IASnC,OAAO,CAAC,gCAAgC;CA0BzC"}