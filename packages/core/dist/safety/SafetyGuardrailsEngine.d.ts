/**
 * @file SafetyGuardrailsEngine - Comprehensive safety and content filtering system
 *
 * This engine provides multi-layered safety guardrails for the Universal AI Brain,
 * including content filtering, prompt injection detection, output validation,
 * and compliance monitoring using MongoDB for safety analytics and logging.
 *
 * Features:
 * - Multi-layered content filtering (input/output)
 * - Prompt injection attack detection
 * - Harmful content classification
 * - Compliance monitoring and reporting
 * - Real-time safety analytics with MongoDB
 * - Framework-agnostic safety enforcement
 * - Configurable safety policies
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface SafetyPolicy {
    id: string;
    name: string;
    description: string;
    enabled: boolean;
    severity: 'low' | 'medium' | 'high' | 'critical';
    rules: SafetyRule[];
    frameworks: string[];
    createdAt: Date;
    updatedAt: Date;
}
export interface SafetyRule {
    id: string;
    type: 'content_filter' | 'prompt_injection' | 'output_validation' | 'rate_limit' | 'compliance';
    pattern: string | RegExp;
    action: 'block' | 'warn' | 'log' | 'modify';
    threshold?: number;
    description: string;
    enabled: boolean;
}
export interface SafetyViolation {
    id: string;
    timestamp: Date;
    traceId?: string;
    sessionId: string;
    userId?: string;
    violationType: 'harmful_content' | 'prompt_injection' | 'policy_violation' | 'rate_limit' | 'compliance';
    severity: 'low' | 'medium' | 'high' | 'critical';
    content: {
        input?: string;
        output?: string;
        context?: string;
    };
    policyId: string;
    ruleId: string;
    action: 'blocked' | 'warned' | 'logged' | 'modified';
    framework: string;
    metadata: Record<string, any>;
}
export interface SafetyAnalytics {
    timeRange: {
        start: Date;
        end: Date;
    };
    totalViolations: number;
    violationsByType: Record<string, number>;
    violationsBySeverity: Record<string, number>;
    violationsByFramework: Record<string, number>;
    topViolatedPolicies: {
        policyId: string;
        policyName: string;
        violationCount: number;
    }[];
    safetyTrends: {
        date: Date;
        violationCount: number;
        blockedCount: number;
    }[];
    complianceScore: number;
}
export interface ContentAnalysisResult {
    isSafe: boolean;
    confidence: number;
    violations: {
        type: string;
        severity: string;
        description: string;
        confidence: number;
    }[];
    suggestedAction: 'allow' | 'block' | 'modify' | 'review';
    modifiedContent?: string;
}
/**
 * SafetyGuardrailsEngine - Comprehensive safety and content filtering
 *
 * Provides multi-layered safety protection for the Universal AI Brain
 * with real-time monitoring and analytics using MongoDB.
 */
export declare class SafetyGuardrailsEngine {
    private tracingCollection;
    private memoryCollection;
    private policies;
    private violationCache;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection);
    /**
     * Analyze input content for safety violations
     */
    analyzeInputSafety(input: string, context: {
        sessionId: string;
        userId?: string;
        framework: string;
        traceId?: string;
    }): Promise<ContentAnalysisResult>;
    /**
     * Analyze output content for safety violations
     */
    analyzeOutputSafety(output: string, context: {
        sessionId: string;
        userId?: string;
        framework: string;
        traceId?: string;
        originalInput?: string;
    }): Promise<ContentAnalysisResult>;
    /**
     * Generate safety analytics using MongoDB aggregation
     */
    generateSafetyAnalytics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<SafetyAnalytics>;
    /**
     * Add or update safety policy
     */
    updateSafetyPolicy(policy: SafetyPolicy): Promise<void>;
    /**
     * Get safety violations for a session
     */
    getSessionViolations(sessionId: string): Promise<SafetyViolation[]>;
    private initializeDefaultPolicies;
    private checkRule;
    private detectHarmfulContent;
    private detectDataLeakage;
    private checkCompliance;
    private determineSuggestedAction;
    private sanitizeContent;
    private logViolation;
    private mapRuleTypeToViolationType;
    private arrayToRecord;
    private parseViolationFromDocument;
}
//# sourceMappingURL=SafetyGuardrailsEngine.d.ts.map