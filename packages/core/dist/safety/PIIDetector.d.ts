/**
 * @file PIIDetector - Comprehensive PII detection and data leakage prevention system
 *
 * This system identifies and masks personally identifiable information, prevents data
 * leakage, implements data anonymization, and ensures compliance with privacy
 * regulations (GDPR, CCPA) using MongoDB for analytics and pattern tracking.
 *
 * Features:
 * - Multi-pattern PII detection (SSN, credit cards, emails, phones, etc.)
 * - Real-time data leakage prevention
 * - GDPR/CCPA compliance monitoring
 * - Configurable anonymization strategies
 * - Privacy analytics with MongoDB
 * - Framework-agnostic privacy protection
 * - Automated redaction and masking
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface PIIDetectionResult {
    hasPII: boolean;
    detectedPII: PIIMatch[];
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    sanitizedContent: string;
    complianceStatus: {
        gdpr: 'compliant' | 'violation' | 'warning';
        ccpa: 'compliant' | 'violation' | 'warning';
        hipaa: 'compliant' | 'violation' | 'warning';
    };
    recommendations: string[];
}
export interface PIIMatch {
    type: PIIType;
    value: string;
    maskedValue: string;
    location: {
        startIndex: number;
        endIndex: number;
    };
    confidence: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    regulation: ('gdpr' | 'ccpa' | 'hipaa')[];
}
export type PIIType = 'ssn' | 'credit_card' | 'email' | 'phone' | 'ip_address' | 'passport' | 'driver_license' | 'bank_account' | 'medical_record' | 'date_of_birth' | 'full_name' | 'address' | 'tax_id' | 'biometric' | 'financial_account';
export interface PIIPattern {
    type: PIIType;
    pattern: RegExp;
    severity: 'low' | 'medium' | 'high' | 'critical';
    maskingStrategy: 'full' | 'partial' | 'hash' | 'tokenize';
    regulations: ('gdpr' | 'ccpa' | 'hipaa')[];
    description: string;
}
export interface DataLeakageAnalysis {
    timeRange: {
        start: Date;
        end: Date;
    };
    totalScans: number;
    piiDetectionRate: number;
    leakagesByType: Record<PIIType, number>;
    leakagesBySeverity: Record<string, number>;
    complianceViolations: {
        gdpr: number;
        ccpa: number;
        hipaa: number;
    };
    frameworkComparison: {
        framework: string;
        piiDetectionRate: number;
        violationCount: number;
    }[];
    trends: {
        date: Date;
        piiCount: number;
        violationCount: number;
    }[];
}
export interface AnonymizationConfig {
    strategy: 'mask' | 'hash' | 'tokenize' | 'remove' | 'generalize';
    preserveFormat: boolean;
    customMaskChar: string;
    hashSalt?: string;
    tokenMapping?: Map<string, string>;
}
/**
 * PIIDetector - Comprehensive PII detection and data leakage prevention
 *
 * Provides enterprise-grade privacy protection with real-time PII detection,
 * automated anonymization, and compliance monitoring using MongoDB analytics.
 */
export declare class PIIDetector {
    private tracingCollection;
    private memoryCollection;
    private piiPatterns;
    private anonymizationConfigs;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection);
    /**
     * Scan content for PII and data leakage risks
     */
    scanForPII(content: string, context: {
        sessionId: string;
        userId?: string;
        framework: string;
        traceId?: string;
        contentType: 'input' | 'output' | 'context';
    }): Promise<PIIDetectionResult>;
    /**
     * Anonymize content using specified strategy
     */
    anonymizeContent(content: string, strategy?: 'conservative' | 'balanced' | 'aggressive'): Promise<string>;
    /**
     * Generate data leakage analytics using MongoDB aggregation
     */
    generateDataLeakageAnalytics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<DataLeakageAnalysis>;
    /**
     * Check if content is compliant with specific regulation
     */
    checkRegulationCompliance(content: string, regulation: 'gdpr' | 'ccpa' | 'hipaa'): Promise<{
        compliant: boolean;
        violations: PIIMatch[];
        recommendations: string[];
    }>;
    private initializePIIPatterns;
    private initializeAnonymizationConfigs;
    private findPIIMatches;
    private maskValue;
    private calculateRiskLevel;
    private sanitizeContent;
    private checkComplianceStatus;
    private generateRecommendations;
    private generateComplianceRecommendations;
    private getAnonymizationConfig;
    private applyAnonymization;
    private logPIIDetection;
    private arrayToRecord;
}
//# sourceMappingURL=PIIDetector.d.ts.map