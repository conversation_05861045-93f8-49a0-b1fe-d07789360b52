/**
 * @file ComplianceAuditLogger - Comprehensive compliance and audit logging system
 *
 * This system tracks all safety decisions, maintains compliance records, generates
 * audit reports, and provides full traceability for regulatory requirements using
 * MongoDB's official $jsonSchema validation and audit logging patterns.
 *
 * Features:
 * - Comprehensive audit trail with MongoDB validation
 * - Regulatory compliance tracking (GDPR, CCPA, HIPAA, SOX)
 * - Real-time audit logging with official MongoDB patterns
 * - Automated compliance reporting and analytics
 * - Immutable audit records with cryptographic integrity
 * - Framework-agnostic compliance monitoring
 * - Configurable retention policies
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
import { ObjectId } from 'mongodb';
export interface AuditEvent {
    auditId: ObjectId;
    timestamp: Date;
    eventType: 'safety_decision' | 'pii_detection' | 'hallucination_check' | 'content_filter' | 'access_control' | 'data_processing';
    action: 'allow' | 'block' | 'modify' | 'flag' | 'escalate';
    actor: {
        userId?: string;
        sessionId: string;
        framework: string;
        ipAddress?: string;
        userAgent?: string;
    };
    resource: {
        type: 'input' | 'output' | 'context' | 'memory' | 'configuration';
        identifier: string;
        classification: 'public' | 'internal' | 'confidential' | 'restricted';
    };
    decision: {
        outcome: 'approved' | 'denied' | 'modified' | 'escalated';
        reason: string;
        confidence: number;
        automaticDecision: boolean;
        reviewRequired: boolean;
    };
    compliance: {
        regulations: ('gdpr' | 'ccpa' | 'hipaa' | 'sox' | 'pci_dss')[];
        requirements: string[];
        status: 'compliant' | 'violation' | 'warning' | 'review_required';
        evidence: string[];
    };
    metadata: {
        traceId?: string;
        correlationId: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        tags: string[];
        additionalData?: Record<string, any>;
    };
    integrity: {
        hash: string;
        signature?: string;
        previousHash?: string;
    };
}
export interface ComplianceReport {
    reportId: string;
    generatedAt: Date;
    timeRange: {
        start: Date;
        end: Date;
    };
    scope: {
        regulations: string[];
        frameworks: string[];
        eventTypes: string[];
    };
    summary: {
        totalEvents: number;
        complianceRate: number;
        violationCount: number;
        criticalIssues: number;
    };
    violations: {
        regulation: string;
        requirement: string;
        violationCount: number;
        severity: string;
        examples: string[];
    }[];
    trends: {
        date: Date;
        complianceRate: number;
        violationCount: number;
    }[];
    recommendations: {
        priority: 'immediate' | 'high' | 'medium' | 'low';
        action: string;
        regulation: string;
        impact: string;
    }[];
    attestation: {
        certifiedBy: string;
        certificationDate: Date;
        digitalSignature: string;
    };
}
export interface RetentionPolicy {
    regulation: string;
    eventTypes: string[];
    retentionPeriod: number;
    archiveAfter: number;
    deleteAfter: number;
    encryptionRequired: boolean;
    immutableStorage: boolean;
}
/**
 * ComplianceAuditLogger - Comprehensive compliance and audit logging
 *
 * Provides enterprise-grade audit logging with regulatory compliance tracking
 * using MongoDB's official validation and audit patterns.
 */
export declare class ComplianceAuditLogger {
    private tracingCollection;
    private memoryCollection;
    private auditCollection;
    private retentionPolicies;
    private lastAuditHash;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, auditCollection: MemoryCollection);
    /**
     * Log audit event with MongoDB validation and integrity checking
     */
    logAuditEvent(event: Omit<AuditEvent, 'auditId' | 'timestamp' | 'integrity'>): Promise<string>;
    /**
     * Generate comprehensive compliance report using MongoDB aggregation
     */
    generateComplianceReport(timeRange: {
        start: Date;
        end: Date;
    }, scope?: {
        regulations?: string[];
        frameworks?: string[];
        eventTypes?: string[];
    }): Promise<ComplianceReport>;
    /**
     * Verify audit trail integrity
     */
    verifyAuditIntegrity(timeRange: {
        start: Date;
        end: Date;
    }): Promise<{
        valid: boolean;
        brokenChains: string[];
        tamperedEvents: string[];
        missingEvents: string[];
    }>;
    private initializeAuditSchema;
    private initializeRetentionPolicies;
    private validateAuditEvent;
    private calculateHash;
    private signEvent;
    private signReport;
    private generateComplianceRecommendations;
    private applyRetentionPolicy;
}
//# sourceMappingURL=ComplianceAuditLogger.d.ts.map