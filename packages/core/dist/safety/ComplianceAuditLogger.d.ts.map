{"version": 3, "file": "ComplianceAuditLogger.d.ts", "sourceRoot": "", "sources": ["../../src/safety/ComplianceAuditLogger.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;GAeG;AAEH,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAEnC,MAAM,WAAW,UAAU;IACzB,OAAO,EAAE,QAAQ,CAAC;IAClB,SAAS,EAAE,IAAI,CAAC;IAChB,SAAS,EAAE,iBAAiB,GAAG,eAAe,GAAG,qBAAqB,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,iBAAiB,CAAC;IACjI,MAAM,EAAE,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IAC3D,KAAK,EAAE;QACL,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,EAAE,MAAM,CAAC;QAClB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO,GAAG,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,eAAe,CAAC;QAClE,UAAU,EAAE,MAAM,CAAC;QACnB,cAAc,EAAE,QAAQ,GAAG,UAAU,GAAG,cAAc,GAAG,YAAY,CAAC;KACvE,CAAC;IACF,QAAQ,EAAE;QACR,OAAO,EAAE,UAAU,GAAG,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC;QAC1D,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;QACnB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,cAAc,EAAE,OAAO,CAAC;KACzB,CAAC;IACF,UAAU,EAAE;QACV,WAAW,EAAE,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC;QAC/D,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,MAAM,EAAE,WAAW,GAAG,WAAW,GAAG,SAAS,GAAG,iBAAiB,CAAC;QAClE,QAAQ,EAAE,MAAM,EAAE,CAAC;KACpB,CAAC;IACF,QAAQ,EAAE;QACR,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,aAAa,EAAE,MAAM,CAAC;QACtB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;QACjD,IAAI,EAAE,MAAM,EAAE,CAAC;QACf,cAAc,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;KACtC,CAAC;IACF,SAAS,EAAE;QACT,IAAI,EAAE,MAAM,CAAC;QACb,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,YAAY,CAAC,EAAE,MAAM,CAAC;KACvB,CAAC;CACH;AAED,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,EAAE,MAAM,CAAC;IACjB,WAAW,EAAE,IAAI,CAAC;IAClB,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,KAAK,EAAE;QACL,WAAW,EAAE,MAAM,EAAE,CAAC;QACtB,UAAU,EAAE,MAAM,EAAE,CAAC;QACrB,UAAU,EAAE,MAAM,EAAE,CAAC;KACtB,CAAC;IACF,OAAO,EAAE;QACP,WAAW,EAAE,MAAM,CAAC;QACpB,cAAc,EAAE,MAAM,CAAC;QACvB,cAAc,EAAE,MAAM,CAAC;QACvB,cAAc,EAAE,MAAM,CAAC;KACxB,CAAC;IACF,UAAU,EAAE;QACV,UAAU,EAAE,MAAM,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;QACpB,cAAc,EAAE,MAAM,CAAC;QACvB,QAAQ,EAAE,MAAM,CAAC;QACjB,QAAQ,EAAE,MAAM,EAAE,CAAC;KACpB,EAAE,CAAC;IACJ,MAAM,EAAE;QACN,IAAI,EAAE,IAAI,CAAC;QACX,cAAc,EAAE,MAAM,CAAC;QACvB,cAAc,EAAE,MAAM,CAAC;KACxB,EAAE,CAAC;IACJ,eAAe,EAAE;QACf,QAAQ,EAAE,WAAW,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QAClD,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,CAAC;QACnB,MAAM,EAAE,MAAM,CAAC;KAChB,EAAE,CAAC;IACJ,WAAW,EAAE;QACX,WAAW,EAAE,MAAM,CAAC;QACpB,iBAAiB,EAAE,IAAI,CAAC;QACxB,gBAAgB,EAAE,MAAM,CAAC;KAC1B,CAAC;CACH;AAED,MAAM,WAAW,eAAe;IAC9B,UAAU,EAAE,MAAM,CAAC;IACnB,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,eAAe,EAAE,MAAM,CAAC;IACxB,YAAY,EAAE,MAAM,CAAC;IACrB,WAAW,EAAE,MAAM,CAAC;IACpB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,gBAAgB,EAAE,OAAO,CAAC;CAC3B;AAED;;;;;GAKG;AACH,qBAAa,qBAAqB;IAChC,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,eAAe,CAAmB;IAC1C,OAAO,CAAC,iBAAiB,CAA2C;IACpE,OAAO,CAAC,aAAa,CAAc;gBAGjC,iBAAiB,EAAE,iBAAiB,EACpC,gBAAgB,EAAE,gBAAgB,EAClC,eAAe,EAAE,gBAAgB;IASnC;;OAEG;IACG,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,GAAG,WAAW,GAAG,WAAW,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;IA8CpG;;OAEG;IACG,wBAAwB,CAC5B,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,EACrC,KAAK,GAAE;QACL,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC;QACvB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;QACtB,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;KAClB,GACL,OAAO,CAAC,gBAAgB,CAAC;IAwL5B;;OAEG;IACG,oBAAoB,CAAC,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GAAG,OAAO,CAAC;QACzE,KAAK,EAAE,OAAO,CAAC;QACf,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,cAAc,EAAE,MAAM,EAAE,CAAC;QACzB,aAAa,EAAE,MAAM,EAAE,CAAC;KACzB,CAAC;YAiDY,qBAAqB;IA0DnC,OAAO,CAAC,2BAA2B;YAmCrB,kBAAkB;IAgBhC,OAAO,CAAC,aAAa;YAMP,SAAS;YAKT,UAAU;IAKxB,OAAO,CAAC,iCAAiC;YAwC3B,oBAAoB;CAUnC"}