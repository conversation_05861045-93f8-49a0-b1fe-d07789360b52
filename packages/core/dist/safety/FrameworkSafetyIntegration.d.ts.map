{"version": 3, "file": "FrameworkSafetyIntegration.d.ts", "sourceRoot": "", "sources": ["../../src/safety/FrameworkSafetyIntegration.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;GAgBG;AAEH,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAC;AAGlE,MAAM,WAAW,sBAAsB;IACrC,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAC;IAClD,UAAU,EAAE,MAAM,EAAE,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;CACpB;AACD,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AAGnE,MAAM,WAAW,mBAAmB;IAClC,SAAS,EAAE,WAAW,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,CAAC;IAClE,WAAW,EAAE,QAAQ,GAAG,UAAU,GAAG,YAAY,GAAG,QAAQ,CAAC;IAC7D,aAAa,EAAE;QACb,iBAAiB,EAAE,OAAO,CAAC;QAC3B,sBAAsB,EAAE,OAAO,CAAC;QAChC,YAAY,EAAE,OAAO,CAAC;QACtB,iBAAiB,EAAE,OAAO,CAAC;QAC3B,kBAAkB,EAAE,OAAO,CAAC;KAC7B,CAAC;IACF,UAAU,EAAE;QACV,aAAa,EAAE,MAAM,CAAC;QACtB,uBAAuB,EAAE,MAAM,CAAC;QAChC,cAAc,EAAE,MAAM,CAAC;QACvB,iBAAiB,EAAE,MAAM,CAAC;KAC3B,CAAC;IACF,OAAO,EAAE;QACP,eAAe,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC;QACrD,eAAe,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QACnD,aAAa,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;QACjD,qBAAqB,EAAE,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;KAC9D,CAAC;IACF,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC;CAC5B;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,kBAAkB,CAAC;IACxF,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;IACpB,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;IACjD,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,CAAC;IAC5C,OAAO,EAAE,OAAO,CAAC;CAClB;AAED,MAAM,WAAW,qBAAqB;IACpC,OAAO,EAAE,OAAO,CAAC;IACjB,MAAM,EAAE,SAAS,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC;IAChD,eAAe,EAAE,MAAM,CAAC;IACxB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,UAAU,EAAE;QACV,IAAI,EAAE,gBAAgB,GAAG,eAAe,GAAG,KAAK,GAAG,YAAY,GAAG,aAAa,CAAC;QAChF,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;QACjD,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB,EAAE,CAAC;IACJ,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,sBAAsB;IACrC,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE;QACT,KAAK,EAAE,IAAI,CAAC;QACZ,GAAG,EAAE,IAAI,CAAC;KACX,CAAC;IACF,aAAa,EAAE,MAAM,CAAC;IACtB,YAAY,EAAE;QACZ,iBAAiB,EAAE;YACjB,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC;QACF,sBAAsB,EAAE;YACtB,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,YAAY,EAAE;YACZ,OAAO,EAAE,MAAM,CAAC;YAChB,QAAQ,EAAE,MAAM,CAAC;YACjB,MAAM,EAAE,MAAM,CAAC;YACf,OAAO,EAAE,MAAM,CAAC;SACjB,CAAC;QACF,gBAAgB,EAAE;YAChB,SAAS,EAAE,MAAM,CAAC;YAClB,UAAU,EAAE,MAAM,CAAC;YACnB,SAAS,EAAE,MAAM,CAAC;SACnB,CAAC;KACH,CAAC;IACF,qBAAqB,EAAE,MAAM,CAAC;IAC9B,WAAW,EAAE,MAAM,CAAC;CACrB;AAED;;;;;GAKG;AACH,qBAAa,0BAA0B;IACrC,OAAO,CAAC,YAAY,CAAyB;IAC7C,OAAO,CAAC,qBAAqB,CAAwB;IACrD,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,qBAAqB,CAAwB;IACrD,OAAO,CAAC,iBAAiB,CAAoB;IAC7C,OAAO,CAAC,gBAAgB,CAAmB;IAC3C,OAAO,CAAC,uBAAuB,CAA+C;IAC9E,OAAO,CAAC,eAAe,CAAoF;gBAGzG,YAAY,EAAE,sBAAsB,EACpC,qBAAqB,EAAE,qBAAqB,EAC5C,WAAW,EAAE,WAAW,EACxB,qBAAqB,EAAE,qBAAqB,EAC5C,iBAAiB,EAAE,iBAAiB,EACpC,gBAAgB,EAAE,gBAAgB;IAWpC;;OAEG;IACG,aAAa,CACjB,SAAS,EAAE,MAAM,EACjB,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,GAAG,EACb,SAAS,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,qBAAqB,CAAC;IAqKjC;;OAEG;IACG,cAAc,CAClB,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,GAAG,EACb,SAAS,CAAC,EAAE,MAAM,GACjB,OAAO,CAAC,qBAAqB,CAAC;IAoHjC;;OAEG;IACG,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IA6BpF;;OAEG;IACG,gBAAgB,CACpB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE;QAAE,KAAK,EAAE,IAAI,CAAC;QAAC,GAAG,EAAE,IAAI,CAAA;KAAE,GACpC,OAAO,CAAC,sBAAsB,CAAC;IAqFlC,OAAO,CAAC,+BAA+B;IAkCvC,OAAO,CAAC,2BAA2B;IAgBnC,OAAO,CAAC,eAAe;IAevB,OAAO,CAAC,eAAe;IAoBvB,OAAO,CAAC,oBAAoB;IAa5B,OAAO,CAAC,2BAA2B;IAanC,OAAO,CAAC,qBAAqB;IAW7B,OAAO,CAAC,6BAA6B;IAUrC,OAAO,CAAC,mBAAmB;CAU5B"}