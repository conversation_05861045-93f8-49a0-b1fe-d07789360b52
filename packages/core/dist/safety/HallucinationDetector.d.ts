/**
 * @file HallucinationDetector - Advanced hallucination detection and factual accuracy validation
 *
 * This system compares AI responses against provided context, detects factual inconsistencies,
 * identifies unsupported claims, and provides confidence scores for response accuracy using
 * MongoDB for analytics and pattern recognition.
 *
 * Features:
 * - Context-grounded response validation
 * - Factual consistency checking
 * - Unsupported claim detection
 * - Confidence scoring and uncertainty quantification
 * - Real-time hallucination analytics with MongoDB
 * - Framework-agnostic accuracy assessment
 * - Automated fact-checking pipeline
 */
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
import { MongoVectorStore } from '../vector/MongoVectorStore';
export interface HallucinationAnalysis {
    isGrounded: boolean;
    confidenceScore: number;
    factualAccuracy: number;
    contextAlignment: number;
    detectedIssues: HallucinationIssue[];
    supportingEvidence: ContextEvidence[];
    unsupportedClaims: UnsupportedClaim[];
    overallRisk: 'low' | 'medium' | 'high' | 'critical';
    recommendations: string[];
}
export interface HallucinationIssue {
    type: 'factual_error' | 'unsupported_claim' | 'context_contradiction' | 'fabricated_detail' | 'temporal_inconsistency';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    location: {
        startIndex: number;
        endIndex: number;
        text: string;
    };
    confidence: number;
    suggestedCorrection?: string;
}
export interface ContextEvidence {
    claim: string;
    supportingContext: string;
    relevanceScore: number;
    sourceId: string;
    sourceType: 'document' | 'memory' | 'knowledge_base' | 'external';
}
export interface UnsupportedClaim {
    claim: string;
    location: {
        startIndex: number;
        endIndex: number;
    };
    confidence: number;
    reason: 'no_context_support' | 'contradicts_context' | 'fabricated_information' | 'speculative_statement';
    suggestedAction: 'remove' | 'qualify' | 'verify' | 'flag';
}
export interface HallucinationMetrics {
    timeRange: {
        start: Date;
        end: Date;
    };
    totalAnalyses: number;
    hallucinationRate: number;
    averageConfidence: number;
    issuesByType: Record<string, number>;
    issuesBySeverity: Record<string, number>;
    frameworkComparison: {
        framework: string;
        hallucinationRate: number;
        averageConfidence: number;
    }[];
    improvementTrends: {
        date: Date;
        hallucinationRate: number;
        confidence: number;
    }[];
}
export interface FactCheckResult {
    claim: string;
    isSupported: boolean;
    confidence: number;
    supportingEvidence: string[];
    contradictingEvidence: string[];
    uncertainty: number;
}
/**
 * HallucinationDetector - Advanced hallucination detection and factual accuracy validation
 *
 * Uses sophisticated algorithms and MongoDB analytics to detect hallucinations,
 * validate factual accuracy, and ensure AI responses are grounded in provided context.
 */
export declare class HallucinationDetector {
    private tracingCollection;
    private memoryCollection;
    private vectorStore;
    constructor(tracingCollection: TracingCollection, memoryCollection: MemoryCollection, vectorStore: MongoVectorStore);
    /**
     * Analyze response for hallucinations and factual accuracy
     */
    analyzeResponse(response: string, context: {
        providedContext: string[];
        originalQuery: string;
        framework: string;
        sessionId: string;
        traceId?: string;
    }): Promise<HallucinationAnalysis>;
    /**
     * Generate hallucination metrics using MongoDB aggregation
     */
    generateHallucinationMetrics(timeRange: {
        start: Date;
        end: Date;
    }): Promise<HallucinationMetrics>;
    /**
     * Fact-check specific claims against knowledge base
     */
    factCheckClaim(claim: string, context: string[]): Promise<FactCheckResult>;
    private extractClaims;
    private analyzeClaim;
    private detectFactualInconsistencies;
    private detectFabricatedDetails;
    private analyzeTemporalConsistency;
    private calculateContextAlignment;
    private calculateFactualAccuracy;
    private calculateOverallRisk;
    private createUnsupportedClaim;
    private createContextEvidence;
    private generateRecommendations;
    private calculateSemanticSimilarity;
    private detectContradiction;
    private logHallucinationAnalysis;
    private arrayToRecord;
}
//# sourceMappingURL=HallucinationDetector.d.ts.map