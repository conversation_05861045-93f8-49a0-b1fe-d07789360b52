/**
 * @file FrameworkSafetyIntegration - Framework safety integration system
 *
 * This system integrates all safety components (SafetyEngine, HallucinationDetector,
 * PIIDetector, ComplianceAuditLogger) into framework adapters with pre-generation
 * validation, post-generation filtering, and real-time safety monitoring while
 * preserving exact framework API compatibility.
 *
 * Features:
 * - Pre-generation input validation and sanitization
 * - Post-generation output filtering and safety checks
 * - Real-time safety monitoring with MongoDB validation
 * - Framework API compatibility preservation
 * - Configurable safety levels per framework
 * - Comprehensive safety audit logging
 * - Emergency safety circuit breakers
 */
import { SafetyEngine } from './SafetyEngine';
import { HallucinationDetector } from './HallucinationDetector';
import { PIIDetector } from './PIIDetector';
import { ComplianceAuditLogger } from './ComplianceAuditLogger';
import { TracingCollection } from '../collections/TracingCollection';
import { MemoryCollection } from '../collections/MemoryCollection';
export interface SafetyConfiguration {
    framework: 'vercel-ai' | 'mastra' | 'openai-agents' | 'langchain';
    safetyLevel: 'strict' | 'moderate' | 'permissive' | 'custom';
    enabledChecks: {
        contentValidation: boolean;
        hallucinationDetection: boolean;
        piiDetection: boolean;
        complianceLogging: boolean;
        realTimeMonitoring: boolean;
    };
    thresholds: {
        contentSafety: number;
        hallucinationConfidence: number;
        piiSensitivity: number;
        responseTimeLimit: number;
    };
    actions: {
        onUnsafeContent: 'block' | 'filter' | 'warn' | 'log';
        onHallucination: 'block' | 'flag' | 'warn' | 'log';
        onPIIDetected: 'block' | 'mask' | 'warn' | 'log';
        onComplianceViolation: 'block' | 'escalate' | 'warn' | 'log';
    };
    customRules?: SafetyRule[];
}
export interface SafetyRule {
    ruleId: string;
    name: string;
    description: string;
    type: 'input_validation' | 'output_filtering' | 'content_analysis' | 'compliance_check';
    pattern?: string;
    keywords?: string[];
    severity: 'critical' | 'high' | 'medium' | 'low';
    action: 'block' | 'filter' | 'warn' | 'log';
    enabled: boolean;
}
export interface SafetyInterceptResult {
    allowed: boolean;
    action: 'proceed' | 'block' | 'filter' | 'warn';
    originalContent: string;
    filteredContent?: string;
    violations: {
        type: 'content_safety' | 'hallucination' | 'pii' | 'compliance' | 'custom_rule';
        severity: 'critical' | 'high' | 'medium' | 'low';
        description: string;
        confidence: number;
        ruleId?: string;
    }[];
    auditId?: string;
    processingTime: number;
}
export interface FrameworkSafetyMetrics {
    framework: string;
    timeRange: {
        start: Date;
        end: Date;
    };
    totalRequests: number;
    safetyChecks: {
        contentValidation: {
            checked: number;
            blocked: number;
            filtered: number;
            warned: number;
        };
        hallucinationDetection: {
            checked: number;
            flagged: number;
            blocked: number;
        };
        piiDetection: {
            checked: number;
            detected: number;
            masked: number;
            blocked: number;
        };
        complianceChecks: {
            performed: number;
            violations: number;
            escalated: number;
        };
    };
    averageProcessingTime: number;
    safetyScore: number;
}
/**
 * FrameworkSafetyIntegration - Comprehensive safety integration for all frameworks
 *
 * Provides seamless safety integration that preserves framework API compatibility
 * while ensuring enterprise-grade safety and compliance.
 */
export declare class FrameworkSafetyIntegration {
    private safetyEngine;
    private hallucinationDetector;
    private piiDetector;
    private complianceAuditLogger;
    private tracingCollection;
    private memoryCollection;
    private frameworkConfigurations;
    private circuitBreakers;
    constructor(safetyEngine: SafetyEngine, hallucinationDetector: HallucinationDetector, piiDetector: PIIDetector, complianceAuditLogger: ComplianceAuditLogger, tracingCollection: TracingCollection, memoryCollection: MemoryCollection);
    /**
     * Pre-generation safety validation for framework inputs
     */
    validateInput(framework: string, input: string, context?: any, sessionId?: string): Promise<SafetyInterceptResult>;
    /**
     * Post-generation safety filtering for framework outputs
     */
    validateOutput(framework: string, output: string, context?: any, sessionId?: string): Promise<SafetyInterceptResult>;
    /**
     * Configure safety settings for a specific framework
     */
    configureSafety(framework: string, config: SafetyConfiguration): Promise<void>;
    /**
     * Get safety metrics for a framework
     */
    getSafetyMetrics(framework: string, timeRange: {
        start: Date;
        end: Date;
    }): Promise<FrameworkSafetyMetrics>;
    private initializeDefaultConfigurations;
    private validateSafetyConfiguration;
    private determineAction;
    private checkCustomRule;
    private isCircuitBreakerOpen;
    private recordCircuitBreakerFailure;
    private aggregateCheckMetrics;
    private aggregateHallucinationMetrics;
    private aggregatePIIMetrics;
}
//# sourceMappingURL=FrameworkSafetyIntegration.d.ts.map