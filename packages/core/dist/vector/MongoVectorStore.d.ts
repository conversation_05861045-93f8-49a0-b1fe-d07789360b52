/**
 * @file MongoVectorStore - Production-ready MongoDB Atlas Vector Search implementation
 *
 * Based on MongoDB's official documentation and production RAG implementation.
 * This provides the core vector search capabilities for the Universal AI Brain.
 *
 * Features:
 * - Atlas Vector Search with proper indexing
 * - Hybrid search (vector + text)
 * - Automatic embedding generation
 * - Performance optimization
 * - Error handling and fallbacks
 */
import { ObjectId } from 'mongodb';
import { MongoConnection } from '../persistance/MongoConnection';
export interface VectorDocument {
    _id?: ObjectId;
    text: string;
    embedding: number[];
    metadata: Record<string, any>;
    source: string;
    timestamp: Date;
    chunkIndex?: number;
    parentDocumentId?: string;
    tokenCount?: number;
}
export interface VectorSearchOptions {
    limit?: number;
    numCandidates?: number;
    filter?: Record<string, any>;
    minScore?: number;
    index?: string;
    includeEmbeddings?: boolean;
    searchType?: 'vector' | 'hybrid' | 'text';
}
export interface VectorSearchResult extends VectorDocument {
    score: number;
}
export interface VectorIndexDefinition {
    name: string;
    type: 'vectorSearch';
    definition: {
        fields: Array<{
            type: 'vector' | 'filter';
            path: string;
            numDimensions?: number;
            similarity?: 'euclidean' | 'cosine' | 'dotProduct';
            quantization?: 'none' | 'scalar' | 'binary';
        }>;
    };
}
export interface EmbeddingProvider {
    generateEmbedding(text: string): Promise<number[]>;
    getDimensions(): number;
    getModel(): string;
}
/**
 * MongoVectorStore - Production-ready MongoDB Atlas Vector Search implementation
 *
 * This class provides comprehensive vector search capabilities using MongoDB Atlas Vector Search.
 * It follows MongoDB's best practices for production RAG applications.
 */
export declare class MongoVectorStore {
    private collection;
    private db;
    private vectorIndexName;
    private textIndexName;
    private embeddingProvider;
    private isInitialized;
    constructor(mongoConnection: MongoConnection, collectionName?: string, vectorIndexName?: string, textIndexName?: string);
    /**
     * Initialize the vector store with embedding provider
     */
    initialize(embeddingProvider: EmbeddingProvider): Promise<void>;
    /**
     * Store a document with its vector embedding
     */
    storeDocument(text: string, metadata?: Record<string, any>, source?: string, embedding?: number[]): Promise<string>;
    /**
     * Store multiple documents in batch
     */
    storeDocuments(documents: Array<{
        text: string;
        metadata?: Record<string, any>;
        source?: string;
        embedding?: number[];
    }>): Promise<string[]>;
    /**
     * Perform vector search using MongoDB Atlas Vector Search
     * Based on MongoDB's production RAG implementation
     */
    vectorSearch(query: string | number[], options?: VectorSearchOptions): Promise<VectorSearchResult[]>;
    /**
     * Hybrid search combining vector search with text search
     * Essential for production RAG applications
     */
    hybridSearch(query: string, options?: VectorSearchOptions): Promise<VectorSearchResult[]>;
    /**
     * Text search using MongoDB text indexes
     */
    textSearch(query: string, options?: VectorSearchOptions): Promise<VectorSearchResult[]>;
    /**
     * Find similar documents to a given document ID
     */
    findSimilar(documentId: string, options?: VectorSearchOptions): Promise<VectorSearchResult[]>;
    private ensureInitialized;
    private generateEmbedding;
    private estimateTokenCount;
    private ensureIndexes;
    /**
     * Create vector search index definition for Atlas
     * This needs to be created in Atlas UI or via Atlas CLI
     */
    getVectorIndexDefinition(dimensions?: number): VectorIndexDefinition;
    /**
     * Get collection statistics
     */
    getStats(): Promise<any>;
    /**
     * Delete documents by filter
     */
    deleteDocuments(filter: Record<string, any>): Promise<number>;
    /**
     * Update document metadata
     */
    updateDocumentMetadata(documentId: string, metadata: Record<string, any>): Promise<boolean>;
    /**
     * Get document by ID
     */
    getDocument(documentId: string, includeEmbedding?: boolean): Promise<VectorDocument | null>;
    /**
     * Search documents by metadata
     */
    searchByMetadata(filter: Record<string, any>, options?: {
        limit?: number;
        sort?: Record<string, 1 | -1>;
    }): Promise<VectorDocument[]>;
    /**
     * Get recent documents
     */
    getRecentDocuments(limit?: number, source?: string): Promise<VectorDocument[]>;
    /**
     * Cleanup old documents
     */
    cleanupOldDocuments(olderThanDays: number, source?: string): Promise<number>;
    /**
     * Perform health check
     */
    healthCheck(): Promise<{
        isHealthy: boolean;
        details: any;
    }>;
}
//# sourceMappingURL=MongoVectorStore.d.ts.map