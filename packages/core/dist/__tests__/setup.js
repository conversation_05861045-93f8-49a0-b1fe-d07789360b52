"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupTestDb = setupTestDb;
exports.teardownTestDb = teardownTestDb;
exports.getTestDb = getTestDb;
const mongodb_memory_server_1 = require("mongodb-memory-server");
const mongodb_1 = require("mongodb");
let mongod;
let client;
let db;
async function setupTestDb() {
    mongod = await mongodb_memory_server_1.MongoMemoryServer.create();
    const uri = mongod.getUri();
    client = new mongodb_1.MongoClient(uri);
    await client.connect();
    db = client.db('test_ai_agents');
    return db;
}
async function teardownTestDb() {
    if (client) {
        await client.close();
    }
    if (mongod) {
        await mongod.stop();
    }
}
function getTestDb() {
    return db;
}
