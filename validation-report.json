{"timestamp": "2025-06-23T19:52:52.869Z", "summary": {"totalComponents": 5, "passedComponents": 4, "totalIssues": 1, "criticalIssues": 0, "compilationErrors": 453, "testFailures": 0, "overallStatus": "GOOD"}, "frameworkValidations": [{"framework": "<PERSON><PERSON>", "adapterFile": "packages/core/src/adapters/MastraAdapter.ts", "apiAccuracy": {"component": "API Accuracy", "status": "FAIL", "issues": ["Missing: Uses real Agent.generate() method", "Missing: Uses real Agent.stream() method", "Missing: Uses real createTool pattern"], "recommendations": [], "severity": "HIGH"}, "typeCompatibility": {"component": "Type Compatibility", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "implementationCompleteness": {"component": "Implementation Completeness", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "documentationAlignment": {"component": "Documentation Alignment", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}}, {"framework": "Vercel AI", "adapterFile": "packages/core/src/adapters/VercelAIAdapter.ts", "apiAccuracy": {"component": "API Accuracy", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "typeCompatibility": {"component": "Type Compatibility", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "implementationCompleteness": {"component": "Implementation Completeness", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "documentationAlignment": {"component": "Documentation Alignment", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}}, {"framework": "LangChain.js", "adapterFile": "packages/core/src/adapters/LangChainJSAdapter.ts", "apiAccuracy": {"component": "API Accuracy", "status": "FAIL", "issues": ["Missing: Uses correct base model class", "Missing: Uses proper prompt templates", "Missing: Uses <PERSON><PERSON><PERSON><PERSON> runnable pattern"], "recommendations": [], "severity": "HIGH"}, "typeCompatibility": {"component": "Type Compatibility", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "implementationCompleteness": {"component": "Implementation Completeness", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "documentationAlignment": {"component": "Documentation Alignment", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}}, {"framework": "OpenAI Agents", "adapterFile": "packages/core/src/adapters/OpenAIAgentsAdapter.ts", "apiAccuracy": {"component": "API Accuracy", "status": "FAIL", "issues": ["Missing: Uses chat completions API"], "recommendations": [], "severity": "HIGH"}, "typeCompatibility": {"component": "Type Compatibility", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "implementationCompleteness": {"component": "Implementation Completeness", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, "documentationAlignment": {"component": "Documentation Alignment", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}}], "compilationAnalysis": {"totalErrors": 453, "errorsByCategory": {"Missing Imports": 23, "Other": 263, "Missing Properties": 140, "Type Mismatches": 27}, "errorsByFile": {"examples/basic-usage.ts": 3, "examples/customer-service/src/CustomerServiceAgent.test.ts": 1, "examples/customer-service/src/CustomerServiceAgent.ts": 2, "examples/customer-service/src/example.ts": 1, "examples/framework-integrations/langchain-example.ts": 8, "examples/framework-integrations/mastra-example.ts": 2, "examples/framework-integrations/openai-agents-example.ts": 5, "examples/framework-integrations/universal-integration-example.ts": 11, "examples/framework-integrations/vercel-ai-example.ts": 7, "examples/integration-tests/complete-system-test.ts": 3, "examples/production-ready/company-chooses-mastra.ts": 4, "examples/production-ready/company-chooses-vercel-ai.ts": 5, "examples/universal-framework-integration/all-frameworks-example.ts": 5, "examples/universal-framework-integration/vercel-ai-example.ts": 4, "packages/core/src/__tests__/FrameworkAdapters.test.ts": 20, "packages/core/src/__tests__/HybridSearch.test.ts": 2, "packages/core/src/__tests__/integration.test.ts": 1, "packages/core/src/__tests__/integration/complete-system-validation.test.ts": 3, "packages/core/src/__tests__/integration/framework-integration.test.ts": 21, "packages/core/src/__tests__/integration/mongodb-validation.test.ts": 37, "packages/core/src/__tests__/MongoDataStore.test.ts": 3, "packages/core/src/__tests__/MongoMemoryProvider.test.ts": 8, "packages/core/src/__tests__/MongoVectorStore.test.ts": 1, "packages/core/src/__tests__/PerformanceBenchmarks.test.ts": 1, "packages/core/src/__tests__/SchemaValidator.test.ts": 8, "packages/core/src/adapters/BaseFrameworkAdapter.ts": 3, "packages/core/src/adapters/FrameworkAdapterManager.ts": 15, "packages/core/src/adapters/LangChainJSAdapter.ts": 8, "packages/core/src/adapters/MastraAdapter.ts": 6, "packages/core/src/adapters/OpenAIAgentsAdapter.ts": 9, "packages/core/src/adapters/VercelAIAdapter.ts": 15, "packages/core/src/brain/UniversalAIBrain.ts": 7, "packages/core/src/collections/AgentCollection.ts": 7, "packages/core/src/collections/BaseCollection.ts": 8, "packages/core/src/collections/index.ts": 3, "packages/core/src/collections/MemoryCollection.ts": 7, "packages/core/src/collections/MetricsCollection.ts": 6, "packages/core/src/collections/ToolCollection.ts": 7, "packages/core/src/collections/TracingCollection.ts": 7, "packages/core/src/collections/WorkflowCollection.ts": 7, "packages/core/src/embeddings/OpenAIEmbeddingProvider.ts": 5, "packages/core/src/index.ts": 3, "packages/core/src/intelligence/ContextInjectionEngine.ts": 1, "packages/core/src/intelligence/SemanticMemoryEngine.ts": 6, "packages/core/src/intelligence/VectorSearchEngine.ts": 3, "packages/core/src/monitoring/CostMonitoringEngine.ts": 14, "packages/core/src/monitoring/ErrorTrackingEngine.ts": 8, "packages/core/src/monitoring/MonitoringAPI.ts": 6, "packages/core/src/monitoring/PerformanceAnalyticsEngine.ts": 4, "packages/core/src/monitoring/RealTimeMonitoringDashboard.ts": 4, "packages/core/src/monitoring/SystemHealthMonitor.ts": 9, "packages/core/src/persistance/MongoEmbeddingProvider.ts": 1, "packages/core/src/real-time/WorkflowChangeStream.ts": 1, "packages/core/src/safety/ComplianceAuditLogger.ts": 2, "packages/core/src/safety/FrameworkSafetyIntegration.ts": 5, "packages/core/src/safety/HallucinationDetector.ts": 1, "packages/core/src/safety/PIIDetector.ts": 1, "packages/core/src/safety/SafetyGuardrailsEngine.ts": 2, "packages/core/src/self-improvement/ContextLearningEngine.ts": 3, "packages/core/src/self-improvement/FrameworkOptimizationEngine.ts": 2, "packages/core/src/self-improvement/SelfImprovementMetrics.ts": 7, "packages/core/src/tracing/ChangeStreamManager.ts": 6, "packages/core/src/tracing/index.ts": 31, "packages/core/src/tracing/RealTimeMonitor.ts": 1, "packages/core/src/tracing/TracingEngine.ts": 10, "packages/core/src/types/index.ts": 2, "packages/core/src/UniversalAIBrain.ts": 18, "packages/core/src/vector/MongoVectorStore.ts": 14, "scripts/validate-implementations.ts": 2}, "criticalErrors": ["examples/basic-usage.ts(10,77): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/customer-service/src/CustomerServiceAgent.test.ts(2,33): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/customer-service/src/CustomerServiceAgent.ts(8,8): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/customer-service/src/example.ts(4,33): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/framework-integrations/langchain-example.ts(8,54): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/framework-integrations/mastra-example.ts(8,49): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/framework-integrations/openai-agents-example.ts(8,55): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/framework-integrations/universal-integration-example.ts(14,8): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/framework-integrations/vercel-ai-example.ts(8,51): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/integration-tests/complete-system-test.ts(19,8): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/production-ready/company-chooses-mastra.ts(17,8): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/production-ready/company-chooses-vercel-ai.ts(16,8): error TS2307: Cannot find module '@mongodb-ai/core' or its corresponding type declarations.", "examples/universal-framework-integration/all-frameworks-example.ts(14,33): error TS2307: Cannot find module '@universal-ai-brain/vercel-ai' or its corresponding type declarations.", "examples/universal-framework-integration/all-frameworks-example.ts(15,31): error TS2307: Cannot find module '@universal-ai-brain/mastra' or its corresponding type declarations.", "examples/universal-framework-integration/all-frameworks-example.ts(16,37): error TS2307: Cannot find module '@universal-ai-brain/openai-agents' or its corresponding type declarations.", "examples/universal-framework-integration/all-frameworks-example.ts(17,36): error TS2307: Cannot find module '@universal-ai-brain/langchain' or its corresponding type declarations.", "examples/universal-framework-integration/vercel-ai-example.ts(12,33): error TS2307: Cannot find module '@universal-ai-brain/vercel-ai' or its corresponding type declarations.", "packages/core/src/__tests__/integration/framework-integration.test.ts(17,34): error TS2307: Cannot find module '../../adapters/LangChainAdapter' or its corresponding type declarations.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(12,36): error TS2307: Cannot find module '../../engines/VectorSearchEngine' or its corresponding type declarations.", "packages/core/src/monitoring/MonitoringAPI.ts(19,42): error TS2307: Cannot find module 'socket.io' or its corresponding type declarations.", "packages/core/src/monitoring/MonitoringAPI.ts(21,17): error TS2307: Cannot find module 'jsonwebtoken' or its corresponding type declarations.", "packages/core/src/monitoring/MonitoringAPI.ts(24,20): error TS2307: Cannot find module 'helmet' or its corresponding type declarations.", "packages/core/src/safety/FrameworkSafetyIntegration.ts(19,54): error TS2307: Cannot find module './SafetyEngine' or its corresponding type declarations."], "fixableErrors": ["examples/basic-usage.ts(125,30): error TS7006: Parameter 'context' implicitly has an 'any' type.", "examples/basic-usage.ts(125,39): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/customer-service/src/CustomerServiceAgent.ts(354,26): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(103,26): error TS7006: Parameter 'doc' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(103,31): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(225,26): error TS7006: Parameter 'doc' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(225,31): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(236,21): error TS7006: Parameter 'doc' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(279,25): error TS7006: Parameter 'tool' implicitly has an 'any' type.", "examples/framework-integrations/langchain-example.ts(279,31): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/framework-integrations/mastra-example.ts(126,80): error TS7006: Parameter 'c' implicitly has an 'any' type.", "examples/framework-integrations/openai-agents-example.ts(252,25): error TS7006: Parameter 'tool' implicitly has an 'any' type.", "examples/framework-integrations/openai-agents-example.ts(252,31): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/framework-integrations/openai-agents-example.ts(284,25): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/framework-integrations/openai-agents-example.ts(284,33): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/framework-integrations/universal-integration-example.ts(267,5): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "examples/framework-integrations/universal-integration-example.ts(285,25): error TS18046: 'framework' is of type 'unknown'.", "examples/framework-integrations/universal-integration-example.ts(293,26): error TS18046: 'framework' is of type 'unknown'.", "examples/framework-integrations/universal-integration-example.ts(303,31): error TS18046: 'framework' is of type 'unknown'.", "examples/framework-integrations/universal-integration-example.ts(313,7): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "examples/framework-integrations/universal-integration-example.ts(319,7): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "examples/framework-integrations/universal-integration-example.ts(321,16): error TS18046: 'error' is of type 'unknown'.", "examples/framework-integrations/universal-integration-example.ts(333,20): error TS18046: 'result' is of type 'unknown'.", "examples/framework-integrations/universal-integration-example.ts(334,18): error TS18046: 'result' is of type 'unknown'.", "examples/framework-integrations/universal-integration-example.ts(335,20): error TS18046: 'result' is of type 'unknown'.", "examples/framework-integrations/vercel-ai-example.ts(97,78): error TS7006: Parameter 'c' implicitly has an 'any' type.", "examples/framework-integrations/vercel-ai-example.ts(109,16): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/framework-integrations/vercel-ai-example.ts(271,33): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/framework-integrations/vercel-ai-example.ts(271,41): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/framework-integrations/vercel-ai-example.ts(340,17): error TS7006: Parameter 'chunk' implicitly has an 'any' type.", "examples/framework-integrations/vercel-ai-example.ts(343,18): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/integration-tests/complete-system-test.ts(330,9): error TS2454: Variable 'mongoClient' is used before being assigned.", "examples/integration-tests/complete-system-test.ts(333,9): error TS2454: Variable 'mongoServer' is used before being assigned.", "examples/production-ready/company-chooses-mastra.ts(223,75): error TS7006: Parameter 'c' implicitly has an 'any' type.", "examples/production-ready/company-chooses-mastra.ts(237,73): error TS7006: Parameter 'c' implicitly has an 'any' type.", "examples/production-ready/company-chooses-mastra.ts(251,72): error TS7006: Parameter 'c' implicitly has an 'any' type.", "examples/production-ready/company-chooses-vercel-ai.ts(157,18): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/production-ready/company-chooses-vercel-ai.ts(225,77): error TS7006: Parameter 'c' implicitly has an 'any' type.", "examples/production-ready/company-chooses-vercel-ai.ts(251,33): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/production-ready/company-chooses-vercel-ai.ts(251,41): error TS7006: Parameter 'index' implicitly has an 'any' type.", "examples/universal-framework-integration/all-frameworks-example.ts(274,15): error TS2339: Property 'cleanup' does not exist on type 'UniversalAIBrain'.", "examples/universal-framework-integration/vercel-ai-example.ts(88,16): error TS7006: Parameter 'result' implicitly has an 'any' type.", "examples/universal-framework-integration/vercel-ai-example.ts(264,46): error TS18046: 'error' is of type 'unknown'.", "examples/universal-framework-integration/vercel-ai-example.ts(272,15): error TS2339: Property 'cleanup' does not exist on type 'UniversalAIBrain'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(63,9): error TS2353: Object literal may only specify known properties, and 'maxResults' does not exist in type '{ indexName: string; collectionName: string; minScore: number; }'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(98,9): error TS2561: Object literal may only specify known properties, but 'enablePromptEnhancement' does not exist in type 'Partial<VercelAIAdapterConfig>'. Did you mean to write 'enableStreamEnhancement'?", "packages/core/src/__tests__/FrameworkAdapters.test.ts(182,9): error TS2561: Object literal may only specify known properties, but 'enablePromptEnhancement' does not exist in type 'Partial<MastraAdapterConfig>'. Did you mean to write 'enableToolEnhancement'?", "packages/core/src/__tests__/FrameworkAdapters.test.ts(210,9): error TS2561: Object literal may only specify known properties, but 'enablePromptEnhancement' does not exist in type 'Partial<OpenAIAgentsAdapterConfig>'. Did you mean to write 'enableAgentEnhancement'?", "packages/core/src/__tests__/FrameworkAdapters.test.ts(232,9): error TS2561: Object literal may only specify known properties, but 'enablePromptEnhancement' does not exist in type 'Partial<LangChainJSAdapterConfig>'. Did you mean to write 'enableContextEnhancement'?", "packages/core/src/__tests__/FrameworkAdapters.test.ts(261,28): error TS2339: Property 'isReady' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(262,28): error TS2339: Property 'isReady' does not exist on type 'MastraAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(276,24): error TS2339: Property 'isReady' does not exist on type 'MastraAdapter | VercelAIAdapter | LangChainJSAdapter | OpenAIAgentsAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(291,22): error TS2339: Property 'isReady' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(293,21): error TS2339: Property 'cleanup' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(295,22): error TS2339: Property 'isReady' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(304,33): error TS2339: Property 'getConfig' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(306,35): error TS2339: Property 'getConfig' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(330,9): error TS2353: Object literal may only specify known properties, and 'enableMetrics' does not exist in type 'Partial<VercelAIAdapterConfig>'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(356,35): error TS2445: Property 'checkFrameworkAvailability' is protected and only accessible within class 'VercelAIAdapter' and its subclasses.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(371,35): error TS2445: Property 'checkFrameworkAvailability' is protected and only accessible within class 'MastraAdapter' and its subclasses.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(386,35): error TS2445: Property 'checkFrameworkAvailability' is protected and only accessible within class 'OpenAIAgentsAdapter' and its subclasses.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(401,35): error TS2445: Property 'checkFrameworkAvailability' is protected and only accessible within class 'LangChainJSAdapter' and its subclasses.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(421,37): error TS2339: Property 'checkFrameworkAvailability' does not exist on type 'MastraAdapter | VercelAIAdapter | LangChainJSAdapter | OpenAIAgentsAdapter'.", "packages/core/src/__tests__/FrameworkAdapters.test.ts(431,24): error TS2339: Property 'isReady' does not exist on type 'MastraAdapter | VercelAIAdapter | LangChainJSAdapter | OpenAIAgentsAdapter'.", "packages/core/src/__tests__/HybridSearch.test.ts(7,26): error TS2314: Generic type 'MongoEmbeddingProvider<T>' requires 1 type argument(s).", "packages/core/src/__tests__/HybridSearch.test.ts(15,25): error TS2554: Expected 3 arguments, but got 1.", "packages/core/src/__tests__/integration.test.ts(23,58): error TS2554: Expected 1 arguments, but got 2.", "packages/core/src/__tests__/integration/complete-system-validation.test.ts(76,17): error TS2339: Property 'cleanup' does not exist on type 'UniversalAIBrain'.", "packages/core/src/__tests__/integration/complete-system-validation.test.ts(105,39): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/complete-system-validation.test.ts(299,39): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(74,7): error TS2739: Type '{ provider: \"openai\"; apiKey: string; }' is missing the following properties from type '{ provider: \"openai\" | \"cohere\" | \"huggingface\"; model: string; apiKey: string; dimensions: number; }': model, dimensions", "packages/core/src/__tests__/integration/framework-integration.test.ts(84,17): error TS2339: Property 'cleanup' does not exist on type 'UniversalAIBrain'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(105,38): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(116,38): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(129,36): error TS2345: Argument of type '{ conversationId: string; userMessage: string; assistantResponse: string; context: never[]; framework: string; }' is not assignable to parameter of type 'Omit<Interaction, \"timestamp\" | \"id\">'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(137,38): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(150,38): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(163,38): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(266,38): error TS2551: Property 'createEnhancedClient' does not exist on type 'OpenAIAgentsAdapter'. Did you mean 'createEnhancedAgent'?", "packages/core/src/__tests__/integration/framework-integration.test.ts(275,36): error TS2345: Argument of type '{ conversationId: string; userMessage: string; assistantResponse: string; context: never[]; framework: string; }' is not assignable to parameter of type 'Omit<Interaction, \"timestamp\" | \"id\">'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(283,38): error TS2551: Property 'createEnhancedClient' does not exist on type 'OpenAIAgentsAdapter'. Did you mean 'createEnhancedAgent'?", "packages/core/src/__tests__/integration/framework-integration.test.ts(296,38): error TS2551: Property 'createEnhancedClient' does not exist on type 'OpenAIAgentsAdapter'. Did you mean 'createEnhancedAgent'?", "packages/core/src/__tests__/integration/framework-integration.test.ts(309,38): error TS2551: Property 'createEnhancedClient' does not exist on type 'OpenAIAgentsAdapter'. Did you mean 'createEnhancedAgent'?", "packages/core/src/__tests__/integration/framework-integration.test.ts(371,39): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(387,36): error TS2345: Argument of type '{ conversationId: string; userMessage: string; assistantResponse: string; context: never[]; framework: string; }' is not assignable to parameter of type 'Omit<Interaction, \"timestamp\" | \"id\">'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(396,35): error TS2551: Property 'getRelevantContext' does not exist on type 'UniversalAIBrain'. Did you mean 'retrieveRelevantContext'?", "packages/core/src/__tests__/integration/framework-integration.test.ts(413,39): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(449,16): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(461,34): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/framework-integration.test.ts(489,32): error TS2339: Property 'createEnhancedSDK' does not exist on type 'VercelAIAdapter'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(45,25): error TS2554: Expected 3 arguments, but got 2.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(50,29): error TS2339: Property 'initialize' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(51,28): error TS2339: Property 'initialize' does not exist on type 'MemoryCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(78,30): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(108,30): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(129,46): error TS2339: Property 'watch' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(137,34): error TS7006: Parameter 'change' implicitly has an 'any' type.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(146,11): error TS2322: Type '\"test-framework\"' is not assignable to type '\"vercel-ai\" | \"unknown\" | \"langchain\" | \"mastra\" | \"openai-agents\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(150,11): error TS2322: Type '\"test_operation\"' is not assignable to type '\"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(170,46): error TS2339: Property 'watch' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(172,33): error TS7006: Parameter 'error' implicitly has an 'any' type.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(210,44): error TS2345: Argument of type '{ framework: { frameworkName: string; }; operation: { type: string; }; performance: { responseTime: number; }; cost: { total: number; }; timestamp: Date; traceId: string; }' is not assignable to parameter of type '{ traceId: string; agentId: ObjectId; sessionId: string; conversationId?: string | undefined; operation: { type: \"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"; description?: string | undefined; userInput: string; finalOutput?: string | undefined; outp...'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(223,24): error TS2339: Property 'overallMetrics' does not exist on type 'PerformanceMetrics'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(224,24): error TS2339: Property 'frameworkMetrics' does not exist on type 'PerformanceMetrics'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(225,38): error TS2339: Property 'frameworkMetrics' does not exist on type 'PerformanceMetrics'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(260,26): error TS2322: Type '\"test\"' is not assignable to type '\"vercel-ai\" | \"unknown\" | \"langchain\" | \"mastra\" | \"openai-agents\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(261,26): error TS2322: Type '\"test\"' is not assignable to type '\"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(264,34): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(278,45): error TS2339: Property 'getTrace' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(290,26): error TS2322: Type '\"test\"' is not assignable to type '\"vercel-ai\" | \"unknown\" | \"langchain\" | \"mastra\" | \"openai-agents\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(291,26): error TS2322: Type '\"test\"' is not assignable to type '\"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(299,16): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(307,45): error TS2339: Property 'getTrace' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(317,22): error TS2322: Type '\"test\"' is not assignable to type '\"vercel-ai\" | \"unknown\" | \"langchain\" | \"mastra\" | \"openai-agents\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(318,22): error TS2322: Type '\"test\"' is not assignable to type '\"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(324,45): error TS2339: Property 'getTrace' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(329,31): error TS2339: Property 'updateTrace' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(334,52): error TS2339: Property 'getTrace' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(365,44): error TS2345: Argument of type '{ traceId: string; framework: any; operation: any; }' is not assignable to parameter of type '{ traceId: string; agentId: ObjectId; sessionId: string; conversationId?: string | undefined; operation: { type: \"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"; description?: string | undefined; userInput: string; finalOutput?: string | undefined; outp...'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(384,11): error TS2322: Type '\"test-framework\"' is not assignable to type '\"vercel-ai\" | \"unknown\" | \"langchain\" | \"mastra\" | \"openai-agents\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(388,11): error TS2322: Type '\"test_operation\"' is not assignable to type '\"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(396,51): error TS2339: Property 'getTrace' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(411,26): error TS2322: Type '\"test\"' is not assignable to type '\"vercel-ai\" | \"unknown\" | \"langchain\" | \"mastra\" | \"openai-agents\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(412,26): error TS2322: Type '\"performance_test\"' is not assignable to type '\"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(423,52): error TS2339: Property 'getRecentTraces' does not exist on type 'TracingCollection'.", "packages/core/src/__tests__/integration/mongodb-validation.test.ts(440,13): error TS2322: Type '\"test_operation\"' is not assignable to type '\"embedding\" | \"vector_search\" | \"chat_completion\" | \"mongodb_operation\" | \"context_retrieval\"'.", "packages/core/src/__tests__/MongoDataStore.test.ts(44,37): error TS2339: Property 'findById' does not exist on type 'MongoDataStore<TestDocument>'.", "packages/core/src/__tests__/MongoDataStore.test.ts(53,38): error TS2339: Property 'findById' does not exist on type 'MongoDataStore<TestDocument>'.", "packages/core/src/__tests__/MongoDataStore.test.ts(107,37): error TS2339: Property 'findById' does not exist on type 'MongoDataStore<TestDocument>'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(2,10): error TS2305: Module '\"../persistance/IMemoryStore\"' has no exported member 'ChatMessage'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(78,28): error TS2339: Property 'updateWorkingState' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(79,46): error TS2339: Property 'getWorkingState' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(85,42): error TS2339: Property 'getWorkingState' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(98,28): error TS2339: Property 'updateTempFindings' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(99,46): error TS2339: Property 'getTempFindings' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(105,45): error TS2339: Property 'getTempFindings' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoMemoryProvider.test.ts(145,45): error TS2339: Property 'getActiveSessions' does not exist on type 'MongoMemoryProvider'.", "packages/core/src/__tests__/MongoVectorStore.test.ts(367,9): error TS2304: Cannot find name 'mongoConnection'.", "packages/core/src/__tests__/PerformanceBenchmarks.test.ts(48,9): error TS2353: Object literal may only specify known properties, and 'maxResults' does not exist in type '{ indexName: string; collectionName: string; minScore: number; }'.", "packages/core/src/__tests__/SchemaValidator.test.ts(17,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(28,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(46,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(68,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(87,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(111,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(131,38): error TS2339: Property 'validate' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/__tests__/SchemaValidator.test.ts(168,39): error TS2339: Property 'getAvailableSchemas' does not exist on type 'typeof SchemaValidator'.", "packages/core/src/adapters/BaseFrameworkAdapter.ts(88,42): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/BaseFrameworkAdapter.ts(89,18): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/BaseFrameworkAdapter.ts(114,9): error TS2322: Type 'EnhancementStrategy' is not assignable to type '\"hybrid\" | \"semantic\" | \"conversational\" | undefined'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(47,12): error TS2314: Generic type 'BaseFrameworkAdapter<T>' requires 1 type argument(s).", "packages/core/src/adapters/FrameworkAdapterManager.ts(124,9): error TS2561: Object literal may only specify known properties, but 'enablePromptEnhancement' does not exist in type 'AdapterConfig'. Did you mean to write 'enableContextEnhancement'?", "packages/core/src/adapters/FrameworkAdapterManager.ts(151,14): error TS2314: Generic type 'BaseFrameworkAdapter<T>' requires 1 type argument(s).", "packages/core/src/adapters/FrameworkAdapterManager.ts(172,85): error TS2345: Argument of type '{ enableMemoryInjection?: boolean | undefined; enableContextEnhancement?: boolean | undefined; enableToolIntegration?: boolean | undefined; maxContextItems?: number | undefined; enhancementStrategy?: \"hybrid\" | ... 2 more ... | undefined; }' is not assignable to parameter of type 'AdapterConfig'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(211,38): error TS2314: Generic type 'BaseFrameworkAdapter<T>' requires 1 type argument(s).", "packages/core/src/adapters/FrameworkAdapterManager.ts(258,75): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(291,82): error TS2314: Generic type 'BaseFrameworkAdapter<T>' requires 1 type argument(s).", "packages/core/src/adapters/FrameworkAdapterManager.ts(364,50): error TS2339: Property 'healthCheck' does not exist on type 'UniversalAIBrain'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(422,46): error TS2304: Cannot find name 'FrameworkDetectionResult'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(423,31): error TS2304: Cannot find name 'FrameworkInfo'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(440,37): error TS2339: Property 'checkFrameworkAvailability' does not exist on type 'MastraAdapter | VercelAIAdapter | LangChainJSAdapter | OpenAIAgentsAdapter'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(441,38): error TS2339: Property 'checkVersionCompatibility' does not exist on type 'MastraAdapter | VercelAIAdapter | LangChainJSAdapter | OpenAIAgentsAdapter'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(466,65): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(500,12): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ 'Vercel AI': string; Mastra: string; 'OpenAI Agents': string; 'LangChain.js': string; }'.", "packages/core/src/adapters/FrameworkAdapterManager.ts(506,45): error TS2304: Cannot find name 'FrameworkInfo'.", "packages/core/src/adapters/LangChainJSAdapter.ts(87,12): error TS2348: Value of type 'typeof MongoDBVectorStore' is not callable. Did you mean to include 'new'?", "packages/core/src/adapters/LangChainJSAdapter.ts(145,12): error TS2348: Value of type 'typeof MongoDBMemory' is not callable. Did you mean to include 'new'?", "packages/core/src/adapters/LangChainJSAdapter.ts(217,21): error TS2339: Property 'brain' does not exist on type '{ invoke(messages: LangChainMessage[]): Promise<any>; stream(messages: LangChainMessage[]): Promise<any>; }'.", "packages/core/src/adapters/LangChainJSAdapter.ts(223,38): error TS2339: Property 'extractUserMessage' does not exist on type '{ invoke(messages: LangChainMessage[]): Promise<any>; stream(messages: LangChainMessage[]): Promise<any>; }'.", "packages/core/src/adapters/LangChainJSAdapter.ts(227,43): error TS2339: Property 'brain' does not exist on type '{ invoke(messages: LangChainMessage[]): Promise<any>; stream(messages: LangChainMessage[]): Promise<any>; }'.", "packages/core/src/adapters/LangChainJSAdapter.ts(234,45): error TS2339: Property 'replaceUserMessage' does not exist on type '{ invoke(messages: LangChainMessage[]): Promise<any>; stream(messages: LangChainMessage[]): Promise<any>; }'.", "packages/core/src/adapters/LangChainJSAdapter.ts(240,26): error TS2339: Property 'brain' does not exist on type '{ invoke(messages: LangChainMessage[]): Promise<any>; stream(messages: LangChainMessage[]): Promise<any>; }'.", "packages/core/src/adapters/LangChainJSAdapter.ts(337,50): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/MastraAdapter.ts(72,9): error TS2416: Property 'integrate' in type 'MastraAdapter' is not assignable to the same property in base type 'BaseFrameworkAdapter<MastraAgent>'.", "packages/core/src/adapters/MastraAdapter.ts(119,13): error TS2353: Object literal may only specify known properties, and 'userId' does not exist in type '{ frameworkType?: string | undefined; conversationId?: string | undefined; maxContextItems?: number | undefined; enhancementStrategy?: \"hybrid\" | \"semantic\" | \"conversational\" | undefined; }'.", "packages/core/src/adapters/MastraAdapter.ts(347,37): error TS2339: Property 'content' does not exist on type 'GenerateTextResult<any, unknown>'.", "packages/core/src/adapters/MastraAdapter.ts(353,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/MastraAdapter.ts(384,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/MastraAdapter.ts(462,22): error TS2339: Property 'Memory' does not exist on type '{ default: typeof import(\"/Users/<USER>/Dev/ai_brain/AI_Brain/node_modules/@mastra/core/dist/index\"); ExecutionEngine: typeof ExecutionEngine; Mastra: typeof Mastra; ... 47 more ...; Workflow: typeof Workflow; }'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(37,18): error TS2430: Interface 'OpenAIAgentsAdapterConfig' incorrectly extends interface 'AdapterConfig'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(72,9): error TS2416: Property 'integrate' in type 'OpenAIAgentsAdapter' is not assignable to the same property in base type 'BaseFrameworkAdapter<OpenAIAgent>'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(328,50): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(458,9): error TS2353: Object literal may only specify known properties, and 'guardrails' does not exist in type '{ name: string; instructions?: string | ((runContext: RunContext<unknown>, agent: Agent<unknown, \"text\">) => string | Promise<string>) | undefined; prompt?: Prompt | ((runContext: RunContext<...>, agent: Agent<...>) => Prompt | Promise<...>) | undefined; ... 11 more ...; resetToolChoice?: boolean | undefined; }'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(468,52): error TS2367: This comparison appears to be unintentional because the types '\"tool_approval_item\" | \"message_output_item\" | \"tool_call_item\" | \"reasoning_item\" | \"handoff_call_item\" | \"tool_call_output_item\" | \"handoff_output_item\"' and '\"tool_call\"' have no overlap.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(470,23): error TS2339: Property 'runId' does not exist on type 'RunResult<undefined, Agent<unknown, \"text\">>'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(474,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(498,9): error TS2353: Object literal may only specify known properties, and 'guardrails' does not exist in type '{ name: string; instructions?: string | ((runContext: RunContext<unknown>, agent: Agent<unknown, \"text\">) => string | Promise<string>) | undefined; prompt?: Prompt | ((runContext: RunContext<...>, agent: Agent<...>) => Prompt | Promise<...>) | undefined; ... 11 more ...; resetToolChoice?: boolean | undefined; }'.", "packages/core/src/adapters/OpenAIAgentsAdapter.ts(507,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(35,18): error TS2430: Interface 'VercelAIAdapterConfig' incorrectly extends interface 'AdapterConfig'.", "packages/core/src/adapters/VercelAIAdapter.ts(270,46): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(271,13): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.", "packages/core/src/adapters/VercelAIAdapter.ts(442,47): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(443,19): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.", "packages/core/src/adapters/VercelAIAdapter.ts(488,44): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(489,13): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.", "packages/core/src/adapters/VercelAIAdapter.ts(686,48): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(687,13): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.", "packages/core/src/adapters/VercelAIAdapter.ts(779,56): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(940,41): error TS2345: Argument of type 'AISDKGenerateOptions' is not assignable to parameter of type 'CallSettings & Prompt & { model: LanguageModelV1; tools?: ToolSet | undefined; toolChoice?: ToolChoice<ToolSet> | undefined; ... 11 more ...; _internal?: { ...; } | undefined; }'.", "packages/core/src/adapters/VercelAIAdapter.ts(945,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(963,39): error TS2345: Argument of type 'AISDKStreamOptions' is not assignable to parameter of type 'CallSettings & Prompt & { model: LanguageModelV1; tools?: ToolSet | undefined; toolChoice?: ToolChoice<ToolSet> | undefined; ... 16 more ...; _internal?: { ...; } | undefined; }'.", "packages/core/src/adapters/VercelAIAdapter.ts(968,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/adapters/VercelAIAdapter.ts(996,11): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/brain/UniversalAIBrain.ts(282,52): error TS2339: Property 'vectorSearchWithMetadata' does not exist on type 'EmbeddingProvider'.", "packages/core/src/brain/UniversalAIBrain.ts(287,26): error TS7006: Parameter 'result' implicitly has an 'any' type.", "packages/core/src/brain/UniversalAIBrain.ts(343,41): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/brain/UniversalAIBrain.ts(367,49): error TS2339: Property 'getCollection' does not exist on type 'MongoVectorStore'.", "packages/core/src/brain/UniversalAIBrain.ts(378,58): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/brain/UniversalAIBrain.ts(387,56): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/brain/UniversalAIBrain.ts(501,12): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ mastra: string; 'vercel-ai': string; langchain: string; 'openai-agents': string; unknown: string; }'.", "packages/core/src/collections/AgentCollection.ts(9,10): error TS2305: Module '\"../types/index\"' has no exported member 'Agent'.", "packages/core/src/collections/AgentCollection.ts(9,17): error TS2305: Module '\"../types/index\"' has no exported member 'AgentConfiguration'.", "packages/core/src/collections/AgentCollection.ts(9,37): error TS2305: Module '\"../types/index\"' has no exported member 'AgentStatus'.", "packages/core/src/collections/AgentCollection.ts(268,41): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/AgentCollection.ts(268,46): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/AgentCollection.ts(272,47): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/AgentCollection.ts(272,52): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/BaseCollection.ts(123,7): error TS2322: Type 'WithId<T>[]' is not assignable to type 'T[]'.", "packages/core/src/collections/BaseCollection.ts(135,5): error TS2322: Type 'WithId<T> | null' is not assignable to type 'T | null'.", "packages/core/src/collections/BaseCollection.ts(143,5): error TS2322: Type 'WithId<T> | null' is not assignable to type 'T | null'.", "packages/core/src/collections/BaseCollection.ts(160,52): error TS2345: Argument of type 'T' is not assignable to parameter of type 'OptionalUnlessRequiredId<T>'.", "packages/core/src/collections/BaseCollection.ts(186,53): error TS2345: Argument of type 'T[]' is not assignable to parameter of type 'readonly OptionalUnlessRequiredId<T>[]'.", "packages/core/src/collections/BaseCollection.ts(220,12): error TS18047: 'result' is possibly 'null'.", "packages/core/src/collections/BaseCollection.ts(220,19): error TS2339: Property 'value' does not exist on type '(BaseDocument | Pick<BaseDocument, \"createdAt\" | \"updatedAt\">) & { _id: InferIdType<T>; }'.", "packages/core/src/collections/BaseCollection.ts(316,5): error TS2322: Type 'WithId<T>[]' is not assignable to type 'T[]'.", "packages/core/src/collections/index.ts(33,3): error TS2300: Duplicate identifier 'ContextItem'.", "packages/core/src/collections/index.ts(62,3): error TS2300: Duplicate identifier 'ContextItem'.", "packages/core/src/collections/index.ts(253,18): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/collections/MemoryCollection.ts(9,10): error TS2305: Module '\"../types/index\"' has no exported member 'AgentMemory'.", "packages/core/src/collections/MemoryCollection.ts(9,23): error TS2305: Module '\"../types/index\"' has no exported member 'MemoryType'.", "packages/core/src/collections/MemoryCollection.ts(9,35): error TS2305: Module '\"../types/index\"' has no exported member 'MemoryImportance'.", "packages/core/src/collections/MemoryCollection.ts(391,37): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/MemoryCollection.ts(391,42): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/MemoryCollection.ts(395,49): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/MemoryCollection.ts(395,54): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/MetricsCollection.ts(9,10): error TS2724: '\"../types/index\"' has no exported member named 'AgentPerformanceMetrics'. Did you mean 'PerformanceMetrics'?", "packages/core/src/collections/MetricsCollection.ts(227,37): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/MetricsCollection.ts(227,42): error TS7006: Parameter 'metric' implicitly has an 'any' type.", "packages/core/src/collections/MetricsCollection.ts(355,55): error TS2345: Argument of type 'string | ObjectId | undefined' is not assignable to parameter of type 'string | ObjectId'.", "packages/core/src/collections/MetricsCollection.ts(357,7): error TS7053: Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "packages/core/src/collections/MetricsCollection.ts(428,7): error TS2322: Type '{ metric: string; value: any; threshold: number; severity: string; }[]' is not assignable to type '{ metric: string; value: number; threshold: number; severity: \"critical\" | \"warning\"; }[]'.", "packages/core/src/collections/ToolCollection.ts(9,10): error TS2305: Module '\"../types/index\"' has no exported member 'AgentTool'.", "packages/core/src/collections/ToolCollection.ts(9,21): error TS2305: Module '\"../types/index\"' has no exported member 'ToolExecution'.", "packages/core/src/collections/ToolCollection.ts(9,36): error TS2305: Module '\"../types/index\"' has no exported member 'ToolStatus'.", "packages/core/src/collections/ToolCollection.ts(442,41): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/ToolCollection.ts(442,46): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/ToolCollection.ts(446,45): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/ToolCollection.ts(446,50): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/TracingCollection.ts(391,9): error TS7053: Element implicitly has an 'any' type because expression of type '`performance.${string}`' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/TracingCollection.ts(393,7): error TS7053: Element implicitly has an 'any' type because expression of type '\"performance.totalDuration\"' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/TracingCollection.ts(399,9): error TS7053: Element implicitly has an 'any' type because expression of type '`tokensUsed.${string}`' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/TracingCollection.ts(406,9): error TS7053: Element implicitly has an 'any' type because expression of type '`cost.${string}`' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/TracingCollection.ts(408,7): error TS7053: Element implicitly has an 'any' type because expression of type '\"cost.calculatedAt\"' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/TracingCollection.ts(423,7): error TS7053: Element implicitly has an 'any' type because expression of type '\"safetyChecks\"' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/TracingCollection.ts(428,7): error TS7053: Element implicitly has an 'any' type because expression of type '\"debugInfo\"' can't be used to index type '{ endTime: Date; totalDuration: number; status: \"timeout\" | \"completed\" | \"failed\" | \"cancelled\"; 'operation.finalOutput': string | undefined; 'operation.outputType': \"object\" | \"error\" | \"text\" | \"stream\" | undefined; updatedAt: Date; }'.", "packages/core/src/collections/WorkflowCollection.ts(9,10): error TS2305: Module '\"../types/index\"' has no exported member 'AgentWorkflow'.", "packages/core/src/collections/WorkflowCollection.ts(9,25): error TS2305: Module '\"../types/index\"' has no exported member 'WorkflowStatus'.", "packages/core/src/collections/WorkflowCollection.ts(9,41): error TS2305: Module '\"../types/index\"' has no exported member 'WorkflowStep'.", "packages/core/src/collections/WorkflowCollection.ts(408,41): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/WorkflowCollection.ts(408,46): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/collections/WorkflowCollection.ts(412,47): error TS7006: Parameter 'acc' implicitly has an 'any' type.", "packages/core/src/collections/WorkflowCollection.ts(412,52): error TS7006: Parameter 'item' implicitly has an 'any' type.", "packages/core/src/embeddings/OpenAIEmbeddingProvider.ts(74,56): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/embeddings/OpenAIEmbeddingProvider.ts(101,57): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/embeddings/OpenAIEmbeddingProvider.ts(185,15): error TS2322: Type 'unknown' is not assignable to type 'EmbeddingResponse'.", "packages/core/src/embeddings/OpenAIEmbeddingProvider.ts(202,96): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/embeddings/OpenAIEmbeddingProvider.ts(298,27): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/index.ts(43,1): error TS2308: Module './types' has already exported a member named 'PerformanceMetrics'. Consider explicitly re-exporting to resolve the ambiguity.", "packages/core/src/index.ts(72,1): error TS2308: Module './types' has already exported a member named 'EmbeddingProvider'. Consider explicitly re-exporting to resolve the ambiguity.", "packages/core/src/index.ts(73,1): error TS2308: Module './tracing' has already exported a member named 'ChangeStreamManager'. Consider explicitly re-exporting to resolve the ambiguity.", "packages/core/src/intelligence/ContextInjectionEngine.ts(570,32): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "packages/core/src/intelligence/SemanticMemoryEngine.ts(152,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/intelligence/SemanticMemoryEngine.ts(308,33): error TS2339: Property 'updateDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/intelligence/SemanticMemoryEngine.ts(352,29): error TS2339: Property 'updateDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/intelligence/SemanticMemoryEngine.ts(357,29): error TS2339: Property 'updateDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/intelligence/SemanticMemoryEngine.ts(499,31): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "packages/core/src/intelligence/SemanticMemoryEngine.ts(516,37): error TS2339: Property 'updateDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/intelligence/VectorSearchEngine.ts(248,55): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/intelligence/VectorSearchEngine.ts(415,52): error TS2769: No overload matches this call.", "packages/core/src/intelligence/VectorSearchEngine.ts(608,31): error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(222,49): error TS2339: Property 'watch' does not exist on type 'TracingCollection'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(230,38): error TS7006: Parameter 'change' implicitly has an 'any' type.", "packages/core/src/monitoring/CostMonitoringEngine.ts(236,31): error TS7006: Parameter 'error' implicitly has an 'any' type.", "packages/core/src/monitoring/CostMonitoringEngine.ts(276,31): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(320,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(721,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(748,7): error TS2345: Argument of type 'CostBreakdown' is not assignable to parameter of type '{ model: number; embedding: number; mongodb: number; vectorSearch: number; storage: number; compute: number; network: number; total: number; }'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(750,40): error TS2339: Property 'input' does not exist on type 'TokenUsage'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(751,41): error TS2339: Property 'output' does not exist on type 'TokenUsage'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(752,40): error TS2339: Property 'total' does not exist on type 'TokenUsage'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(758,40): error TS2339: Property 'total' does not exist on type 'CostBreakdown'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(758,67): error TS2339: Property 'total' does not exist on type 'TokenUsage'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(764,36): error TS2339: Property 'model' does not exist on type 'FrameworkMetadata'.", "packages/core/src/monitoring/CostMonitoringEngine.ts(765,23): error TS2339: Property 'userId' does not exist on type 'AgentTrace'.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(188,49): error TS2339: Property 'watch' does not exist on type 'TracingCollection'.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(196,38): error TS7006: Parameter 'change' implicitly has an 'any' type.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(202,31): error TS7006: Parameter 'error' implicitly has an 'any' type.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(252,32): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(535,32): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(561,15): error TS2339: Property 'errorMessage' does not exist on type 'AgentError'.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(566,34): error TS2339: Property 'input' does not exist on type '{ type: \"chat\" | \"custom\" | \"generate_text\" | \"stream_text\" | \"generate_object\" | \"memory_retrieval\" | \"context_search\"; description?: string | undefined; userInput: string; finalOutput?: string | undefined; outputType?: \"object\" | ... 3 more ... | undefined; }'.", "packages/core/src/monitoring/ErrorTrackingEngine.ts(568,37): error TS2339: Property 'severity' does not exist on type 'AgentError'.", "packages/core/src/monitoring/MonitoringAPI.ts(18,58): error TS7016: Could not find a declaration file for module 'express'. '/Users/<USER>/Dev/ai_brain/AI_Brain/node_modules/express/index.js' implicitly has an 'any' type.", "packages/core/src/monitoring/MonitoringAPI.ts(23,18): error TS7016: Could not find a declaration file for module 'cors'. '/Users/<USER>/Dev/ai_brain/AI_Brain/node_modules/cors/lib/index.js' implicitly has an 'any' type.", "packages/core/src/monitoring/MonitoringAPI.ts(274,31): error TS7006: Parameter 'socket' implicitly has an 'any' type.", "packages/core/src/monitoring/PerformanceAnalyticsEngine.ts(401,9): error TS2322: Type '\"improving\" | \"stable\" | \"degrading\"' is not assignable to type '\"stable\" | \"increasing\" | \"decreasing\"'.", "packages/core/src/monitoring/PerformanceAnalyticsEngine.ts(519,34): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/PerformanceAnalyticsEngine.ts(645,11): error TS7034: Variable 'recommendations' implicitly has type 'any[]' in some locations where its type cannot be determined.", "packages/core/src/monitoring/PerformanceAnalyticsEngine.ts(667,12): error TS7005: Variable 'recommendations' implicitly has an 'any[]' type.", "packages/core/src/monitoring/RealTimeMonitoringDashboard.ts(275,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/RealTimeMonitoringDashboard.ts(301,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/RealTimeMonitoringDashboard.ts(508,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/RealTimeMonitoringDashboard.ts(683,5): error TS2322: Type '{ name: string; status: string; performance: { responseTime: any; successRate: number; throughput: number; errorRate: number; }; lastActivity: any; }[]' is not assignable to type '{ name: string; status: \"error\" | \"active\" | \"inactive\"; performance: { responseTime: number; successRate: number; throughput: number; errorRate: number; }; lastActivity: Date; }[]'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(232,9): error TS2451: Cannot redeclare block-scoped variable 'systemHealth'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(249,13): error TS2451: Cannot redeclare block-scoped variable 'systemHealth'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(268,7): error TS2740: Type '{ cpu: HealthCheckResult; memory: HealthCheckResult; disk: HealthCheckResult; network: HealthCheckResult; }' is missing the following properties from type 'SystemHealth': overall, timestamp, uptime, services, and 2 more.", "packages/core/src/monitoring/SystemHealthMonitor.ts(271,35): error TS2345: Argument of type '{ cpu: HealthCheckResult; memory: HealthCheckResult; disk: HealthCheckResult; network: HealthCheckResult; }' is not assignable to parameter of type 'SystemHealth'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(274,38): error TS2345: Argument of type '{ cpu: HealthCheckResult; memory: HealthCheckResult; disk: HealthCheckResult; network: HealthCheckResult; }' is not assignable to parameter of type 'SystemHealth'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(694,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(711,58): error TS2345: Argument of type '\"system_down\"' is not assignable to parameter of type '\"service_down\" | \"performance_degraded\" | \"resource_exhausted\" | \"dependency_failure\"'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(737,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/monitoring/SystemHealthMonitor.ts(756,7): error TS2345: Argument of type '\"system_down\"' is not assignable to parameter of type '\"service_down\" | \"performance_degraded\" | \"resource_exhausted\" | \"dependency_failure\"'.", "packages/core/src/persistance/MongoEmbeddingProvider.ts(191,36): error TS2339: Property 'stats' does not exist on type 'Collection<EmbeddedDocument<T>>'.", "packages/core/src/real-time/WorkflowChangeStream.ts(28,7): error TS2353: Object literal may only specify known properties, and 'status' does not exist in type 'LogContext'.", "packages/core/src/safety/ComplianceAuditLogger.ts(171,32): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/safety/ComplianceAuditLogger.ts(373,32): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/safety/FrameworkSafetyIntegration.ts(203,50): error TS2339: Property 'detectPII' does not exist on type 'PIIDetector'.", "packages/core/src/safety/FrameworkSafetyIntegration.ts(336,70): error TS2339: Property 'detectHallucination' does not exist on type 'HallucinationDetector'.", "packages/core/src/safety/FrameworkSafetyIntegration.ts(364,50): error TS2339: Property 'detectPII' does not exist on type 'PIIDetector'.", "packages/core/src/safety/FrameworkSafetyIntegration.ts(438,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/safety/HallucinationDetector.ts(594,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/safety/PIIDetector.ts(660,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/safety/SafetyGuardrailsEngine.ts(364,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/safety/SafetyGuardrailsEngine.ts(587,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/self-improvement/ContextLearningEngine.ts(360,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/self-improvement/ContextLearningEngine.ts(386,28): error TS2339: Property 'updateRelevanceScoring' does not exist on type 'MongoVectorStore'.", "packages/core/src/self-improvement/ContextLearningEngine.ts(492,28): error TS2339: Property 'updateSearchParameters' does not exist on type 'MongoVectorStore'.", "packages/core/src/self-improvement/FrameworkOptimizationEngine.ts(180,7): error TS2739: Type '{ responseTime: number; accuracy: number; userSatisfaction: number; costPerToken: number; errorCount: number; successRate: number; }' is missing the following properties from type '{ responseTime: number; accuracy: number; userSatisfaction: number; costEfficiency: number; errorRate: number; }': costEfficiency, errorRate", "packages/core/src/self-improvement/FrameworkOptimizationEngine.ts(580,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(326,7): error TS2322: Type '{ loopId: string; type: \"automated\" | \"user_feedback\" | \"performance_based\" | \"error_triggered\"; status: string; triggerCondition: string; lastTriggered: Date; improvementGenerated: number; }[]' is not assignable to type '{ loopId: string; type: \"automated\" | \"user_feedback\" | \"performance_based\" | \"error_triggered\"; status: \"active\" | \"completed\" | \"paused\"; triggerCondition: string; lastTriggered: Date; improvementGenerated: number; }[]'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(375,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(415,33): error TS2339: Property 'storeDocument' does not exist on type 'MemoryCollection'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(519,7): error TS2322: Type '{ current: number; baseline: number; improvement: number; trend: string; }' is not assignable to type '{ current: number; baseline: number; improvement: number; trend: \"improving\" | \"declining\" | \"stable\"; }'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(520,7): error TS2322: Type '{ current: number; baseline: number; improvement: number; trend: string; }' is not assignable to type '{ current: number; baseline: number; improvement: number; trend: \"improving\" | \"declining\" | \"stable\"; }'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(521,7): error TS2322: Type '{ current: number; baseline: number; improvement: number; trend: string; }' is not assignable to type '{ current: number; baseline: number; improvement: number; trend: \"improving\" | \"declining\" | \"stable\"; }'.", "packages/core/src/self-improvement/SelfImprovementMetrics.ts(522,7): error TS2322: Type '{ current: number; baseline: number; improvement: number; trend: string; }' is not assignable to type '{ current: number; baseline: number; improvement: number; trend: \"improving\" | \"declining\" | \"stable\"; }'.", "packages/core/src/tracing/ChangeStreamManager.ts(284,23): error TS2339: Property 'fullDocument' does not exist on type 'ChangeStreamDocument<AgentTrace>'.", "packages/core/src/tracing/ChangeStreamManager.ts(285,23): error TS2339: Property 'fullDocument' does not exist on type 'ChangeStreamDocument<AgentTrace>'.", "packages/core/src/tracing/ChangeStreamManager.ts(286,25): error TS2339: Property 'fullDocument' does not exist on type 'ChangeStreamDocument<AgentTrace>'.", "packages/core/src/tracing/ChangeStreamManager.ts(287,28): error TS2339: Property 'fullDocument' does not exist on type 'ChangeStreamDocument<AgentTrace>'.", "packages/core/src/tracing/ChangeStreamManager.ts(288,33): error TS2339: Property 'updateDescription' does not exist on type 'ChangeStreamDocument<AgentTrace>'.", "packages/core/src/tracing/ChangeStreamManager.ts(318,30): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error'.", "packages/core/src/tracing/index.ts(24,3): error TS2300: Duplicate identifier 'PerformanceMetrics'.", "packages/core/src/tracing/index.ts(32,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(33,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(34,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(35,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(35,3): error TS2300: Duplicate identifier 'PerformanceMetrics'.", "packages/core/src/tracing/index.ts(36,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(37,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(38,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(39,3): error TS1205: Re-exporting a type when 'isolatedModules' is enabled requires using 'export type'.", "packages/core/src/tracing/index.ts(56,16): error TS2552: Cannot find name 'AgentError'. Did you mean 'RangeError'?", "packages/core/src/tracing/index.ts(61,6): error TS2552: Cannot find name 'AgentError'. Did you mean 'RangeError'?", "packages/core/src/tracing/index.ts(84,7): error TS2304: Cannot find name 'PerformanceMetrics'.", "packages/core/src/tracing/index.ts(111,6): error TS2304: Cannot find name 'ContextItem'.", "packages/core/src/tracing/index.ts(129,23): error TS2304: Cannot find name 'TokenUsage'.", "packages/core/src/tracing/index.ts(130,6): error TS2304: Cannot find name 'TokenUsage'.", "packages/core/src/tracing/index.ts(148,24): error TS2304: Cannot find name 'CostBreakdown'.", "packages/core/src/tracing/index.ts(149,20): error TS2304: Cannot find name 'CostBreakdown'.", "packages/core/src/tracing/index.ts(152,6): error TS2304: Cannot find name 'CostBreakdown'.", "packages/core/src/tracing/index.ts(178,20): error TS2304: Cannot find name 'FrameworkMetadata'.", "packages/core/src/tracing/index.ts(179,28): error TS2304: Cannot find name 'FrameworkMetadata'.", "packages/core/src/tracing/index.ts(180,6): error TS2304: Cannot find name 'FrameworkMetadata'.", "packages/core/src/tracing/index.ts(219,38): error TS2304: Cannot find name 'AgentTrace'.", "packages/core/src/tracing/index.ts(242,53): error TS7006: Parameter 'e' implicitly has an 'any' type.", "packages/core/src/tracing/index.ts(276,35): error TS2304: Cannot find name 'AgentTrace'.", "packages/core/src/tracing/index.ts(306,26): error TS2304: Cannot find name 'AgentStep'.", "packages/core/src/tracing/index.ts(311,36): error TS2339: Property 'tracingEngine' does not exist on type 'PropertyDescriptor'.", "packages/core/src/tracing/index.ts(311,53): error TS2304: Cannot find name 'TracingEngine'.", "packages/core/src/tracing/index.ts(312,30): error TS2339: Property 'currentTraceId' does not exist on type 'PropertyDescriptor'.", "packages/core/src/tracing/index.ts(339,42): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/index.ts(340,13): error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'.", "packages/core/src/tracing/RealTimeMonitor.ts(558,28): error TS2683: 'this' implicitly has type 'any' because it does not have a type annotation.", "packages/core/src/tracing/TracingEngine.ts(109,49): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(110,49): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(142,48): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(143,48): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(188,51): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(189,51): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(201,52): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(214,50): error TS18046: 'recordError' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(256,52): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/tracing/TracingEngine.ts(257,52): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/types/index.ts(98,20): error TS2304: Cannot find name 'UniversalAIBrain'.", "packages/core/src/types/index.ts(99,50): error TS2304: Cannot find name 'UniversalAIBrain'.", "packages/core/src/UniversalAIBrain.ts(229,65): error TS2339: Property 'injectContext' does not exist on type 'ContextInjectionEngine'.", "packages/core/src/UniversalAIBrain.ts(236,62): error TS2339: Property 'searchSimilar' does not exist on type 'VectorSearchEngine'.", "packages/core/src/UniversalAIBrain.ts(311,40): error TS2551: Property 'analyzeFailure' does not exist on type 'FailureAnalysisEngine'. Did you mean 'analyzeFailures'?", "packages/core/src/UniversalAIBrain.ts(374,67): error TS2554: Expected 1 arguments, but got 2.", "packages/core/src/UniversalAIBrain.ts(375,65): error TS2554: Expected 1 arguments, but got 2.", "packages/core/src/UniversalAIBrain.ts(377,66): error TS2554: Expected 1 arguments, but got 2.", "packages/core/src/UniversalAIBrain.ts(378,64): error TS2554: Expected 1 arguments, but got 2.", "packages/core/src/UniversalAIBrain.ts(381,30): error TS2339: Property 'initialize' does not exist on type 'TracingCollection'.", "packages/core/src/UniversalAIBrain.ts(382,29): error TS2339: Property 'initialize' does not exist on type 'MemoryCollection'.", "packages/core/src/UniversalAIBrain.ts(384,30): error TS2339: Property 'initialize' does not exist on type 'MemoryCollection'.", "packages/core/src/UniversalAIBrain.ts(385,28): error TS2339: Property 'initialize' does not exist on type 'MemoryCollection'.", "packages/core/src/UniversalAIBrain.ts(391,54): error TS2345: Argument of type 'MemoryCollection' is not assignable to parameter of type 'Db'.", "packages/core/src/UniversalAIBrain.ts(395,7): error TS2554: Expected 2 arguments, but got 3.", "packages/core/src/UniversalAIBrain.ts(400,25): error TS2554: Expected 2 arguments, but got 0.", "packages/core/src/UniversalAIBrain.ts(401,34): error TS2554: Expected 3 arguments, but got 1.", "packages/core/src/UniversalAIBrain.ts(402,24): error TS2554: Expected 2 arguments, but got 0.", "packages/core/src/UniversalAIBrain.ts(420,34): error TS2554: Expected 3 arguments, but got 2.", "packages/core/src/UniversalAIBrain.ts(472,34): error TS2551: Property 'storeTrace' does not exist on type 'TracingCollection'. Did you mean 'startTrace'?", "packages/core/src/vector/MongoVectorStore.ts(138,52): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(174,53): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(230,11): error TS2353: Object literal may only specify known properties, and '$project' does not exist in type '{ $vectorSearch: { index: string; queryVector: number[]; path: string; filter: Record<string, any>; limit: number; numCandidates: number; }; $addFields?: undefined; $match?: undefined; } | { ...; } | { ...; }'.", "packages/core/src/vector/MongoVectorStore.ts(240,48): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(333,33): error TS2353: Object literal may only specify known properties, and '$match' does not exist in type '{ $search: { index: string; text: { query: string; path: string[]; }; }; $addFields?: undefined; $limit?: undefined; } | { $addFields: { score: { $meta: string; }; }; $search?: undefined; $limit?: undefined; } | { ...; }'.", "packages/core/src/vector/MongoVectorStore.ts(339,88): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(370,60): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(415,58): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(458,25): error TS2339: Property 'stats' does not exist on type 'Collection<VectorDocument>'.", "packages/core/src/vector/MongoVectorStore.ts(473,23): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(486,54): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(512,53): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(549,56): error TS18046: 'error' is of type 'unknown'.", "packages/core/src/vector/MongoVectorStore.ts(608,27): error TS18046: 'error' is of type 'unknown'.", "scripts/validate-implementations.ts(55,7): error TS2769: No overload matches this call.", "scripts/validate-implementations.ts(672,7): error TS2353: Object literal may only specify known properties, and 'error_message' does not exist in type 'LogContext'."]}, "testAnalysis": {"totalTests": 0, "passingTests": 0, "failingTests": 0, "failuresByCategory": {}, "criticalFailures": [], "infrastructureIssues": []}, "systemValidations": [{"component": "UniversalAIBrain", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, {"component": "MongoVectorStore", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, {"component": "Collection Management", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}, {"component": "MongoDB Integration", "status": "FAIL", "issues": ["Missing: Uses singleton pattern"], "recommendations": ["Consider adding Atlas-optimized connection options"], "severity": "HIGH"}, {"component": "Overall Architecture", "status": "PASS", "issues": [], "recommendations": [], "severity": "LOW"}], "recommendations": ["Fix Mastra adapter API compatibility issues", "Fix LangChain.js adapter API compatibility issues", "Fix OpenAI Agents adapter API compatibility issues", "Resolve TypeScript compilation errors", "Update import statements and type definitions"], "actionPlan": {"phase1": "Fix critical TypeScript compilation errors", "phase2": "Resolve framework adapter compatibility issues", "phase3": "Fix failing test cases", "phase4": "Optimize MongoDB integration", "phase5": "Enhance documentation and examples"}}