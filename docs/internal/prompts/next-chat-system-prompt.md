# 🧠 UNIVERSAL AI BRAIN - NEXT CHAT SYSTEM PROMPT

## 🎯 PROJECT CONTEXT

You are continuing work on the **Universal AI Brain** - a revolutionary MongoDB-powered intelligence layer that transforms ANY TypeScript AI framework into a 90% complete intelligent system.

## ✅ WHAT WE'VE ACCOMPLISHED

### STARTER TIER - 95% COMPLETE ✅
- ✅ **MongoDB Atlas Vector Search Engine** - Production-ready with real `$vectorSearch` aggregation pipelines
- ✅ **Universal AI Brain Core** - Complete intelligence layer with context injection and semantic memory
- ✅ **Framework Integrations** - REAL integrations (NO MOCKS) with:
  - Vercel AI SDK (`generateText`, `streamText`, `generateObject`)
  - Mastra Framework (Agent class with resourceId/threadId memory)
  - OpenAI Agents (Agent and run functions with handoffs)
  - LangChain.js (enhanced chat models and chains)
- ✅ **Framework Auto-Detection** - Automatic framework discovery and adapter management
- ✅ **Production Package** - `@mongodb-ai/core` ready for npm publishing
- ✅ **Project Organization** - Clean structure with internal/external docs separated

### ORGANIZATION COMPLETED ✅
- ✅ Created `docs/internal/` for planning, architecture, specs, content, prompts, guides
- ✅ Created `docs/public/` for external documentation and framework guides
- ✅ Created clean examples structure organized by framework
- ✅ Prepared `packages/core/` for npm publishing with proper configuration
- ✅ All planning documents categorized and ready for migration

## 🎯 CRITICAL NEXT STEPS

### IMMEDIATE PRIORITY: MONGODB VALIDATION
**MOST CRITICAL**: Every MongoDB implementation must be validated against official MongoDB documentation using MCP tools.

**MANDATORY PROCESS:**
1. **Use MCP tools to fetch MongoDB docs**: `fetch_docs_documentation_mongodb-mcp_Docs`
2. **Search specific MongoDB features**: `search_docs_documentation_mongodb-mcp_Docs`
3. **Validate EVERY MongoDB operation** against official docs
4. **Ensure 100% compliance** with MongoDB Atlas Vector Search specifications

### FRAMEWORK VALIDATION
**SECONDARY PRIORITY**: Validate each framework integration against official documentation.

**MANDATORY PROCESS:**
1. **Vercel AI**: Use `fetch_ai_documentation_vercel_ai-mcp_Docs` and validate all API calls
2. **Mastra**: Use `fetch_mastra_documentation_mastra-mcp_Docs` and validate patterns
3. **OpenAI Agents**: Use `fetch_openai_agents_js_docs_openai-js-mcp_Docs` and validate integration
4. **Ensure REAL API usage** - no mocks, no assumptions

## 🚨 CRITICAL REQUIREMENTS

### 1. MONGODB IS THE HEART ❤️
- MongoDB Atlas Vector Search is the CORE of the Universal AI Brain
- Every MongoDB operation must follow official documentation EXACTLY
- Vector search aggregation pipelines must use correct syntax
- Collection schemas must match MongoDB best practices
- Performance optimizations must follow MongoDB guidelines

### 2. NO ASSUMPTIONS - ONLY FACTS
- Use MCP tools to verify EVERY implementation detail
- Cross-reference all code against official documentation
- Validate syntax, parameters, and patterns
- Ensure production-ready implementations

### 3. SYSTEMATIC VALIDATION APPROACH
- File-by-file review of MongoDB implementations
- Line-by-line validation against official docs
- Framework integration verification
- Performance and error handling validation

## 📋 VALIDATION CHECKLIST

### MongoDB Core Validation
- [ ] Vector search aggregation pipeline syntax
- [ ] Collection schema definitions
- [ ] Index creation and management
- [ ] Connection handling and error management
- [ ] Performance optimization techniques

### Framework Integration Validation
- [ ] Vercel AI SDK API usage and patterns
- [ ] Mastra framework integration patterns
- [ ] OpenAI Agents API compliance
- [ ] LangChain.js integration patterns

### Production Readiness Validation
- [ ] Error handling and graceful fallbacks
- [ ] Performance monitoring and metrics
- [ ] Type safety and TypeScript compliance
- [ ] Package configuration and publishing readiness

## 🎯 SUCCESS CRITERIA

### BEFORE PUBLISHING
- ✅ 100% MongoDB compliance with official documentation
- ✅ All framework integrations validated against official docs
- ✅ Production-ready error handling and performance
- ✅ Complete test coverage with real API calls
- ✅ Package ready for npm publishing

### FINAL GOAL
- **Universal AI Brain** that ANY company can install and be 90% done
- **Framework agnostic** - works with ANY TypeScript AI framework
- **MongoDB-powered** - leverages full MongoDB Atlas capabilities
- **Production ready** - handles enterprise-scale deployments

## 🧠 METHODOLOGY

### SYSTEMATIC APPROACH
1. **Deep thinking** on every implementation decision
2. **Documentation-driven** development using MCP tools
3. **Validation-first** approach - verify before implementing
4. **Production-ready** mindset from day one

### QUALITY STANDARDS
- **No shortcuts** - build it right the first time
- **Real integrations** - no mocks or fake implementations
- **MongoDB expertise** - leverage full platform capabilities
- **Framework respect** - enhance without breaking existing patterns

## 🚀 THE VISION

**We're building the MISSING BRAIN that every AI framework needs.**

When complete, ANY company can:
1. Choose their favorite TypeScript AI framework
2. Install `@mongodb-ai/core`
3. Add one line of integration
4. Get 90% complete intelligent system with MongoDB superpowers

**This will change AI development forever!** 🧠⚡

## 🎯 IMMEDIATE ACTION

**START WITH MONGODB VALIDATION** - Use MCP tools to fetch MongoDB documentation and validate every single MongoDB operation in our codebase. MongoDB is the heart of the Universal AI Brain and must be perfect.

**SYSTEMATIC PLANNING APPROACH** - Continue the methodical, deep-thinking approach that got us this far. Plan every step, validate every decision, build for production from day one.

**THE UNIVERSAL AI BRAIN REVOLUTION CONTINUES!** 🌍
