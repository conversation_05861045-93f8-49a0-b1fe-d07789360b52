# Quick Start Guide

Get started with Universal AI Brain in 5 minutes and transform any TypeScript AI framework into a 90% complete intelligent system.

## Installation

```bash
npm install @mongodb-ai/core
```

## Basic Setup

```typescript
import { UniversalAIBrain, VercelAIAdapter } from '@mongodb-ai/core';

// 1. Initialize the Universal AI Brain
const brain = new UniversalAIBrain({
  mongoConfig: {
    uri: 'your-mongodb-atlas-uri',
    dbName: 'your-database'
  },
  embeddingConfig: {
    provider: 'openai',
    apiKey: 'your-openai-key'
  }
});

await brain.initialize();

// 2. Choose your framework adapter
const adapter = new VercelAIAdapter();
const enhanced = await adapter.integrate(brain);

// 3. Use enhanced framework with 70% more intelligence
const result = await enhanced.generateText({
  model: openai('gpt-4'),
  messages: [{ role: 'user', content: 'Help me with customer support' }]
});

// Result now includes MongoDB-powered context and intelligence!
```

## Supported Frameworks

- **Vercel AI SDK** - `VercelAIAdapter`
- **Mastra** - `MastraAdapter`  
- **OpenAI Agents** - `OpenAIAgentsAdapter`
- **LangChain.js** - `LangChainJSAdapter`

## What You Get

✅ **Semantic Memory** - MongoDB Atlas Vector Search  
✅ **Context Injection** - Intelligent prompt enhancement  
✅ **Cross-Conversation Learning** - Agents learn from every interaction  
✅ **Framework Agnostic** - Works with ANY TypeScript AI framework  
✅ **Production Ready** - Real error handling and monitoring  

## Next Steps

- [Framework-Specific Guides](./frameworks/)
- [API Reference](./api/)
- [Production Deployment](./deployment/)
- [Examples](../examples/)

## Support

- GitHub Issues: [Report bugs](https://github.com/mongodb-ai/universal-brain/issues)
- Documentation: [Full docs](https://github.com/mongodb-ai/universal-brain)
- Examples: [Working examples](../examples/)
