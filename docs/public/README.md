# Public Documentation

This directory contains all external-facing documentation for the Universal AI Brain project.

## Directory Structure

- `README.md` - Main project overview and introduction
- `quick-start.md` - Getting started guide for developers
- `frameworks/` - Framework-specific integration guides
- `api/` - API reference documentation
- `examples/` - Usage examples and tutorials
- `deployment/` - Production deployment guides

## Purpose

These documents are for:
- External developers using the Universal AI Brain
- Documentation website content
- GitHub repository documentation
- Public API references

## External Use

These documents are:
- Polished and ready for public consumption
- Maintained for accuracy and clarity
- Suitable for documentation websites
- Referenced in package documentation
