# Mastra Framework Integration

Enhance your Mastra agents with MongoDB-powered intelligence and memory.

## Installation

```bash
npm install @mongodb-ai/core @mastra/core
```

## Basic Integration

```typescript
import { Agent } from '@mastra/core';
import { UniversalAIBrain, MastraAdapter } from '@mongodb-ai/core';

// Initialize Universal AI Brain
const brain = new UniversalAIBrain({
  mongoConfig: {
    uri: process.env.MONGODB_URI,
    dbName: 'mastra-ai-app'
  },
  embeddingConfig: {
    provider: 'openai',
    apiKey: process.env.OPENAI_API_KEY
  }
});

await brain.initialize();

// Create enhanced Mastra agent
const adapter = new MastraAdapter();
const enhanced = await adapter.integrate(brain);

// Enhanced agent with MongoDB memory
const agent = enhanced.createAgent({
  name: 'Customer Support Agent',
  instructions: 'You are a helpful customer support agent.',
  model: 'gpt-4'
});

// Use with Mastra's resourceId and threadId for memory
const result = await agent.generate(
  [{ role: 'user', content: 'I need help with my order' }],
  { 
    resourceId: 'customer-123',
    threadId: 'conversation-456'
  }
);
```

## Enhanced Features

### Agent with Memory
```typescript
const agent = enhanced.createAgent({
  name: 'Sales Agent',
  instructions: 'You help customers with product recommendations.',
  model: 'gpt-4'
});

// Memory is automatically managed with resourceId/threadId
const response = await agent.generate(messages, {
  resourceId: 'customer-789',
  threadId: 'sales-conversation-123'
});
```

### Streaming Responses
```typescript
const stream = await agent.stream(
  'Tell me about your premium features',
  { 
    resourceId: 'customer-123',
    threadId: 'conversation-456'
  }
);

for await (const chunk of stream) {
  console.log(chunk.content);
}
```

## Mastra Memory Integration

The Universal AI Brain integrates seamlessly with Mastra's memory system:

- **resourceId** - Maps to user/customer identity in MongoDB
- **threadId** - Maps to conversation/session in MongoDB  
- **Automatic Context** - Injects relevant context from previous interactions
- **Cross-Thread Learning** - Learns patterns across all conversations

## What You Get

✅ **Enhanced Mastra Agents** - 70% more intelligent with MongoDB context  
✅ **Persistent Memory** - Remembers across resourceId and threadId  
✅ **Semantic Search** - Finds relevant context from conversation history  
✅ **Real-time Learning** - Improves based on interaction patterns  
✅ **Mastra Compatible** - Works with existing Mastra workflows  

## Examples

See [Mastra Examples](../../examples/mastra/) for complete working examples.
