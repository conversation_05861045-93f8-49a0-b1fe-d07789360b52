# Vercel AI SDK Integration

Transform your Vercel AI SDK applications with MongoDB-powered intelligence.

## Installation

```bash
npm install @mongodb-ai/core ai
```

## Basic Integration

```typescript
import { generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { UniversalAIBrain, VercelAIAdapter } from '@mongodb-ai/core';

// Initialize Universal AI Brain
const brain = new UniversalAIBrain({
  mongoConfig: {
    uri: process.env.MONGODB_URI,
    dbName: 'my-ai-app'
  },
  embeddingConfig: {
    provider: 'openai',
    apiKey: process.env.OPENAI_API_KEY
  }
});

await brain.initialize();

// Create enhanced Vercel AI functions
const adapter = new VercelAIAdapter();
const enhanced = await adapter.integrate(brain);

// Use enhanced generateText with MongoDB context
const result = await enhanced.generateText({
  model: openai('gpt-4'),
  messages: [
    { role: 'user', content: 'What are the best practices for customer onboarding?' }
  ]
});

console.log(result.text); // Enhanced with relevant context from MongoDB
```

## Enhanced Features

### generateText with Context
```typescript
const result = await enhanced.generateText({
  model: openai('gpt-4'),
  messages: [{ role: 'user', content: 'Help with pricing' }],
  conversationId: 'user-123' // Enables memory across conversations
});
```

### streamText with Intelligence
```typescript
const stream = await enhanced.streamText({
  model: openai('gpt-4'),
  messages: [{ role: 'user', content: 'Explain our product features' }]
});

for await (const chunk of stream.textStream) {
  process.stdout.write(chunk);
}
```

### generateObject with Context
```typescript
const result = await enhanced.generateObject({
  model: openai('gpt-4'),
  schema: z.object({
    recommendation: z.string(),
    confidence: z.number()
  }),
  prompt: 'Recommend next steps for this customer'
});
```

## MongoDB Tools

```typescript
const tools = enhanced.createMongoDBTools();

// Search knowledge base
const knowledge = await tools.searchKnowledgeBase.execute({
  query: 'customer onboarding',
  limit: 5
});

// Store new memory
await tools.storeMemory.execute({
  content: 'Customer prefers email communication',
  type: 'preference'
});
```

## What You Get

✅ **70% More Intelligent Responses** - Context from MongoDB Atlas Vector Search  
✅ **Semantic Memory** - Remembers and learns from every interaction  
✅ **Cross-Conversation Context** - Maintains context across sessions  
✅ **Real-time Learning** - Improves responses based on interactions  
✅ **Production Ready** - Error handling and monitoring built-in  

## Examples

See [Vercel AI Examples](../../examples/vercel-ai/) for complete working examples.
