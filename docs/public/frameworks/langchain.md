# LangChain.js Integration

Enhance your LangChain.js applications with MongoDB-powered intelligence and memory.

## Installation

```bash
npm install @mongodb-ai/core langchain @langchain/core
```

## Basic Integration

```typescript
import { ChatOpenAI } from '@langchain/openai';
import { LLMChain } from 'langchain/chains';
import { PromptTemplate } from '@langchain/core/prompts';
import { UniversalAIBrain, LangChainJSAdapter } from '@mongodb-ai/core';

// Initialize Universal AI Brain
const brain = new UniversalAIBrain({
  mongoConfig: {
    uri: process.env.MONGODB_URI,
    dbName: 'langchain-ai-app'
  },
  embeddingConfig: {
    provider: 'openai',
    apiKey: process.env.OPENAI_API_KEY
  }
});

await brain.initialize();

// Create enhanced LangChain components
const adapter = new LangChainJSAdapter();
const enhanced = await adapter.integrate(brain);

// Enhanced ChatModel with MongoDB context
const model = enhanced.createEnhancedChatModel(new ChatOpenAI({
  modelName: 'gpt-4',
  temperature: 0.7
}));

// Use in chains with automatic context injection
const chain = new LLMChain({
  llm: model,
  prompt: PromptTemplate.fromTemplate(
    'Answer the following question: {question}'
  )
});

const result = await chain.call({
  question: 'What are our company policies?'
});
```

## Enhanced Features

### Enhanced Chat Models
```typescript
const enhancedModel = enhanced.createEnhancedChatModel(
  new ChatOpenAI({ modelName: 'gpt-4' })
);

// Automatic context injection from MongoDB
const response = await enhancedModel.invoke([
  { role: 'user', content: 'Help me with customer onboarding' }
]);
```

### Memory-Enhanced Chains
```typescript
const conversationChain = enhanced.createEnhancedConversationChain({
  llm: new ChatOpenAI({ modelName: 'gpt-4' }),
  conversationId: 'customer-support-123'
});

const result = await conversationChain.call({
  input: 'I need help with my recent order'
});
```

### Vector Store Integration
```typescript
// Use MongoDB as LangChain vector store
const vectorStore = enhanced.createMongoDBVectorStore();

// Add documents
await vectorStore.addDocuments([
  { pageContent: 'Company policy document', metadata: { type: 'policy' } },
  { pageContent: 'Product documentation', metadata: { type: 'product' } }
]);

// Similarity search
const results = await vectorStore.similaritySearch('refund policy', 3);
```

## LangChain.js Features

The Universal AI Brain enhances all LangChain.js features:

- **Chains** - All chain types get MongoDB context injection
- **Agents** - Enhanced with semantic memory and learning
- **Memory** - Persistent memory across conversations  
- **Vector Stores** - MongoDB Atlas as high-performance vector store
- **Tools** - MongoDB tools integrated seamlessly

## What You Get

✅ **Enhanced LangChain Components** - 70% more intelligent with MongoDB context  
✅ **Persistent Memory** - Conversations remembered across sessions  
✅ **Vector Store** - MongoDB Atlas as LangChain vector store  
✅ **Context Injection** - Automatic relevant context in all chains  
✅ **LangChain Compatible** - Works with all existing LangChain patterns  

## Examples

See [LangChain.js Examples](../../examples/langchain/) for complete working examples.
