# OpenAI Agents Integration

Supercharge your OpenAI Agents with MongoDB-powered intelligence and context.

## Installation

```bash
npm install @mongodb-ai/core @openai/agents
```

## Basic Integration

```typescript
import { Agent, run } from '@openai/agents';
import { UniversalAIBrain, OpenAIAgentsAdapter } from '@mongodb-ai/core';

// Initialize Universal AI Brain
const brain = new UniversalAIBrain({
  mongoConfig: {
    uri: process.env.MONGODB_URI,
    dbName: 'openai-agents-app'
  },
  embeddingConfig: {
    provider: 'openai',
    apiKey: process.env.OPENAI_API_KEY
  }
});

await brain.initialize();

// Create enhanced OpenAI Agent
const adapter = new OpenAIAgentsAdapter();
const enhanced = await adapter.integrate(brain);

// Enhanced agent with MongoDB intelligence
const agent = enhanced.createAgent({
  name: 'Support Agent',
  instructions: 'You are a knowledgeable support agent.',
  tools: [],
  handoffs: []
});

// Run with enhanced context
const result = await enhanced.run(agent, 'Help me troubleshoot my issue', {
  conversationId: 'support-session-123'
});
```

## Enhanced Features

### Agent with Context Injection
```typescript
const agent = enhanced.createAgent({
  name: 'Technical Support',
  instructions: 'You provide technical support with access to knowledge base.',
  tools: [
    // Your existing tools
  ],
  handoffs: [
    // Your existing handoffs
  ]
});

const result = await enhanced.run(
  agent, 
  'My application is crashing on startup',
  { conversationId: 'tech-support-456' }
);
```

### Streaming Responses
```typescript
const stream = await enhanced.runStream(
  agent,
  'Explain the troubleshooting process',
  { conversationId: 'support-789' }
);

for await (const chunk of stream) {
  if (chunk.type === 'text') {
    process.stdout.write(chunk.content);
  }
}
```

### Tool Integration
```typescript
const agent = enhanced.createAgent({
  name: 'Enhanced Agent',
  instructions: 'You have access to MongoDB knowledge base.',
  tools: [
    // MongoDB tools are automatically available
    enhanced.createMongoDBTools().searchKnowledgeBase,
    enhanced.createMongoDBTools().storeMemory,
    // Your custom tools
  ]
});
```

## OpenAI Agents Features

The Universal AI Brain enhances all OpenAI Agents features:

- **Agent Handoffs** - Context preserved across agent handoffs
- **Tool Calling** - MongoDB tools integrated seamlessly  
- **Real-time Sessions** - Memory maintained across sessions
- **Guardrails** - Enhanced with context-aware guardrails

## What You Get

✅ **Enhanced OpenAI Agents** - 70% more intelligent with MongoDB context  
✅ **Context Preservation** - Maintains context across handoffs and sessions  
✅ **MongoDB Tools** - Built-in knowledge base and memory tools  
✅ **Real-time Intelligence** - Learns and improves from every interaction  
✅ **OpenAI Compatible** - Works with all OpenAI Agents features  

## Examples

See [OpenAI Agents Examples](../../examples/openai-agents/) for complete working examples.
