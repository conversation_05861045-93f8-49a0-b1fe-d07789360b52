# Publishing Guide

This guide covers how to publish and deploy the Universal AI Brain package.

## Publishing to NPM

### Prerequisites

1. **NPM Account** - Ensure you have an NPM account with publishing permissions
2. **Built Package** - Ensure the package is built and ready
3. **Version Update** - Update version in package.json

### Publishing Steps

```bash
# Navigate to core package
cd packages/core

# Clean and build
npm run clean
npm run build

# Verify build output
ls -la dist/

# Test the package locally (optional)
npm pack
npm install ./mongodb-ai-core-*.tgz

# Login to NPM (if not already logged in)
npm login

# Publish to NPM
npm publish
```

### Version Management

```bash
# Patch version (0.1.0 -> 0.1.1)
npm version patch

# Minor version (0.1.0 -> 0.2.0)  
npm version minor

# Major version (0.1.0 -> 1.0.0)
npm version major

# Then publish
npm publish
```

## Package Validation

Before publishing, verify:

```bash
# Check package contents
npm pack --dry-run

# Verify files included
tar -tzf mongodb-ai-core-*.tgz

# Test installation
npm install @mongodb-ai/core --dry-run
```

## Deployment Checklist

### Pre-Publishing
- [ ] All tests pass: `npm test`
- [ ] Build succeeds: `npm run build`
- [ ] Package.json version updated
- [ ] README.md is current
- [ ] LICENSE file exists
- [ ] .npmignore configured correctly

### Post-Publishing
- [ ] Verify package on npmjs.com
- [ ] Test installation: `npm install @mongodb-ai/core`
- [ ] Update documentation links
- [ ] Create GitHub release
- [ ] Update examples if needed

## MongoDB Atlas Setup

For production deployments, ensure:

### Atlas Configuration
```typescript
const brain = new UniversalAIBrain({
  mongoConfig: {
    uri: process.env.MONGODB_URI, // Atlas connection string
    dbName: process.env.MONGODB_DB_NAME,
    options: {
      retryWrites: true,
      w: 'majority'
    }
  }
});
```

### Vector Search Index
```javascript
// Create vector search index in Atlas
{
  "fields": [
    {
      "type": "vector",
      "path": "embedding",
      "numDimensions": 1536,
      "similarity": "cosine"
    }
  ]
}
```

## Environment Variables

Required environment variables for production:

```bash
# MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/
MONGODB_DB_NAME=your-production-db

# OpenAI (for embeddings)
OPENAI_API_KEY=your-openai-api-key

# Optional: Framework-specific keys
ANTHROPIC_API_KEY=your-anthropic-key
COHERE_API_KEY=your-cohere-key
```

## Production Deployment

### Docker Deployment
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: universal-ai-brain-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: universal-ai-brain-app
  template:
    metadata:
      labels:
        app: universal-ai-brain-app
    spec:
      containers:
      - name: app
        image: your-registry/universal-ai-brain-app:latest
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: uri
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-secret
              key: api-key
```

## Monitoring

### Health Checks
```typescript
// Add health check endpoint
app.get('/health', async (req, res) => {
  try {
    const isHealthy = await brain.healthCheck();
    res.status(isHealthy ? 200 : 503).json({ 
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({ 
      status: 'error', 
      error: error.message 
    });
  }
});
```

### Metrics
```typescript
// Monitor performance
const stats = brain.getStats();
console.log('Brain Stats:', {
  totalInteractions: stats.totalInteractions,
  averageResponseTime: stats.averageResponseTime,
  memoryUsage: stats.memoryUsage
});
```

## Support

For deployment issues:
- [GitHub Issues](https://github.com/mongodb-ai/universal-brain/issues)
- [Documentation](https://github.com/mongodb-ai/universal-brain/tree/main/docs)
- [Examples](https://github.com/mongodb-ai/universal-brain/tree/main/examples)
