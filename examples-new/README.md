# Universal AI Brain Examples

This directory contains clean, organized examples for each supported framework.

## Directory Structure

- `vercel-ai/` - Vercel AI SDK integration examples
- `mastra/` - Mastra framework integration examples  
- `openai-agents/` - OpenAI Agents integration examples
- `langchain/` - LangChain.js integration examples
- `production-ready/` - Real-world production examples

## Example Types

Each framework directory contains:
- `basic-usage.ts` - Simple getting started example
- `advanced-features.ts` - Advanced features demonstration
- `production-example.ts` - Production-ready implementation
- `README.md` - Framework-specific documentation

## Running Examples

Each example includes:
- Clear setup instructions
- Required dependencies
- Environment configuration
- Expected output

## Purpose

These examples demonstrate:
- How to integrate Universal AI Brain with each framework
- 70% intelligence enhancement in action
- Real-world usage patterns
- Best practices and patterns
