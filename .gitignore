# ============================================================================
# AI BRAIN PROJECT - GITIGNORE
# ============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Testing
coverage/
.nyc_output

# Turbo
.turbo

# ============================================================================
# INTERNAL FILES (NOT FOR OPEN SOURCE PUBLICATION)
# ============================================================================

# Internal development documentation and planning files
docs/internal/
internal/

# Legacy documentation files (to be moved to internal)
*_INTERNAL_*
*_LEGACY_*
*_PLANNING_*
*_ANALYSIS_*
*_STATUS_*
*_ASSESSMENT_*
*_ROADMAP_*
*_MASTER_PLAN_*
*_IMPLEMENTATION_*
*_VALIDATION_*
*_COMPREHENSIVE_*
*_DETAILED_*
*_STRATEGIC_*
*_REVOLUTIONARY_*
*_MONGODB_*_BLOG_*
*_SLACK_*
*_ANNOUNCEMENT_*
*_MANIFESTO_*
*_INNOVATION_*
*_ACHIEVEMENT_*
*_COMPLETE_*
*_TIER_*_SPEC_*
*_TIER_*_STATUS_*
*_TIER_*_COMPLETION_*
*_CHECKLIST_*
*_BREAKDOWN_*
*_PROMPT_*
*_SYSTEM_*
*_PRINCIPLES_*
*_INSIGHTS_*
*_RECOMMENDATIONS_*
*_EXECUTION_*
*_SUMMARY_*
*_REPORT_*
*_ORGANIZATION_*
*_PROJECT_*
*_TASKS_*
*_CLEANUP_*
*_CURRENT_*
*_FRAMEWORK_*_PLAN_*
*_INSTALLATION_*_GUIDE_*
*_TYPESCRIPT_*_PLAN_*
*_UNIVERSAL_*_STRATEGY_*
*_CONSOLIDATION_*

# Temporary development files
temp/
tmp/
.temp/
scratch/
playground/
