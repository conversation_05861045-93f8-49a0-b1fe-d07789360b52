// Global Jest setup
require('dotenv').config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai_agents_test';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods in tests to reduce noise
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test utilities
global.testUtils = {
  generateTestId: () => `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  createMockAgent: () => ({
    agent_id: global.testUtils.generateTestId(),
    name: 'Test Agent',
    version: '1.0.0',
    status: 'active',
    created_at: new Date(),
    updated_at: new Date(),
    capabilities: ['test'],
    tools: [],
    model_config: {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      max_tokens: 1000,
      system_prompt: 'You are a test agent'
    },
    performance_targets: {
      max_response_time_seconds: 30,
      min_confidence_score: 0.7,
      max_cost_per_execution: 0.50
    }
  })
};
